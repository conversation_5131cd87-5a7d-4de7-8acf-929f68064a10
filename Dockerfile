FROM node:24.4.1

ARG S3CONFIGUSER
ARG S3CONFIGPASS
ARG S3URL
ARG ENVNAME
ARG DATADOG_API_KEY
ARG DATADOG_SITE
ARG AWS_ACCESS_KEY_ID
ARG AWS_SECRET_ACCESS_KEY


ENV S3CONFIGUSER=$AWS_ACCESS_KEY_ID
ENV S3CONFIGPASS=$AWS_SECRET_ACCESS_KEY
ENV S3URL=$S3URL
ENV ENVNAME=$ENVNAME
ENV DATADOG_API_KEY=$DATADOG_API_KEY
ENV DATADOG_SITE=$DATADOG_SITE

WORKDIR /home/<USER>

RUN curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
RUN unzip awscliv2.zip
RUN ./aws/install
RUN aws configure set aws_access_key_id $S3CONFIGUSER
RUN aws configure set aws_secret_access_key $S3CONFIGPASS
RUN aws configure set AWS_DEFAULT_REGION us-west-2
RUN echo $S3URL
RUN aws s3 cp s3://$S3URL/$ENVNAME .env

COPY . .
RUN ls -al

RUN npm rebuild node-sass
RUN npm ci
RUN npm i -g serve
RUN npm run build

# RUN npm install -g @datadog/datadog-ci
# RUN DATADOG_API_KEY=$DATADOG_API_KEY DATADOG_SITE=$DATADOG_SITE datadog-ci sourcemaps upload ./dist/assets \
#   --service=internal-dashboard \
#   --release-version=1.0.0 \
#   --minified-path-prefix="https://business-admin.korapay.com/assets"

ENV APP_PORT=6100
EXPOSE $APP_PORT

CMD ["sh", "-c", "serve -s dist -l ${APP_PORT}"]
