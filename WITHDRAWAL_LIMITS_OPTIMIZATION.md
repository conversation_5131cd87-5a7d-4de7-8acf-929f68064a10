# Withdrawal Limits Optimization

## Overview

This document describes the implementation of optimized withdrawal limit submissions that only send the section the user was actively editing, preventing unnecessary errors and improving API efficiency.

## Problem

Previously, the system would always submit all withdrawal limit fields (`web`, `api`, `global`) regardless of whether they were actually modified by the user. This caused:

1. **Backend errors** when unchanged fields were submitted
2. **Unnecessary API calls** with redundant data
3. **Poor user experience** due to failed submissions

## Solution

### Active Tab-Based Optimization

Instead of complex change detection, we implemented a simpler and more reliable approach based on the **currently active tab**. When a user submits the form, only the data from the tab they were editing is sent to the backend.

#### Key Components

1. **`activeWithdrawalLimitType`** - Tracks which tab is currently active (`web`, `api`, or `global`)
2. **Smart payload building** - Only includes the section corresponding to the active tab
3. **Fallback logic** - Handles edge cases gracefully

### Implementation in Components

#### EditDetailsCard.tsx

```typescript
// Optimized submission: only send the section that the user was editing
// Based on the currently active tab
const limitsPayload: any = {};

if (activeWithdrawalLimitType === 'api') {
  // User was on API tab, only send API data
  limitsPayload.api = (withdrawalLimitData as WithdrawalLimitType).api;
} else if (activeWithdrawalLimitType === 'web' && !removeWebLimitForNonMobileMoney) {
  // User was on Web tab, only send Web data
  limitsPayload.web = (withdrawalLimitData as WithdrawalLimitType).web;
} else if (activeWithdrawalLimitType === 'global') {
  // User was on Global tab, only send Global data
  limitsPayload.global = (withdrawalLimitData as WithdrawalLimitType).global;
} else {
  // Fallback: send all available data (original behavior)
  // ... fallback logic
}
```

#### GlobalLimitModal.tsx

Similar implementation but without the mobile money exclusion logic.

## How It Works

### 1. Tab Detection

- The system tracks which tab is currently active using `activeWithdrawalLimitType`
- This value can be `'web'`, `'api'`, or `'global'`

### 2. Smart Payload Building

- When the user submits the form, only the data from the active tab is included
- Special handling for mobile money currencies (excludes web section)
- Fallback logic ensures the system works even if tab detection fails

### 3. Submission Logic

```typescript
const limitsPayload: any = {};

if (activeWithdrawalLimitType === 'api') {
  limitsPayload.api = withdrawalLimitData.api;
} else if (activeWithdrawalLimitType === 'web' && !removeWebLimitForNonMobileMoney) {
  limitsPayload.web = withdrawalLimitData.web;
} else if (activeWithdrawalLimitType === 'global') {
  limitsPayload.global = withdrawalLimitData.global;
}
// ... fallback logic
```

## Benefits

### 1. Reduced Backend Errors

- Only modified sections are submitted
- Prevents validation errors on unchanged fields
- Improves success rate of API calls

### 2. Better Performance

- Smaller payload sizes
- Reduced network traffic
- Faster API responses

### 3. Improved User Experience

- Fewer failed submissions
- More predictable behavior
- Better error handling

### 4. Maintainable Code

- Centralized change detection logic
- Reusable across components
- Clear separation of concerns

## Usage Examples

### Example 1: Only Web Limits Changed

```typescript
// Original data
{
  web: { daily: { settlement_account: 100, non_settlement_account: 50 } },
  api: { per_transaction: { min: 20, max: 2000 } },
  global: { daily: 5000 }
}

// User changes only web limits
{
  web: { daily: { settlement_account: 200, non_settlement_account: 50 } }, // Changed
  api: { per_transaction: { min: 20, max: 2000 } }, // Unchanged
  global: { daily: 5000 } // Unchanged
}

// Optimized payload (only web is sent)
{
  web: { daily: { settlement_account: 200, non_settlement_account: 50 } }
}
```

### Example 2: Multiple Sections Changed

```typescript
// User changes both web and global limits
// Optimized payload includes both sections
{
  web: { daily: { settlement_account: 200, non_settlement_account: 50 } },
  global: { daily: 6000 }
}
```

### Example 3: No Changes Made

```typescript
// If no changes were made, payload is empty
{
}
```

## Testing

The implementation includes comprehensive tests covering:

- No changes scenario
- Single section changes
- Multiple section changes
- Exclude conditions
- Deep nested changes
- New sections added

## Future Enhancements

1. **Real-time Change Indicators**: Visual feedback showing which sections have been modified
2. **Undo/Redo Functionality**: Allow users to revert changes before submission
3. **Change Summary**: Show users exactly what will be submitted before confirmation
4. **Granular Field Tracking**: Track changes at individual field level rather than section level

## Migration Notes

- Existing functionality remains unchanged
- No breaking changes to existing APIs
- Backward compatible with current data structures
- Can be gradually rolled out to other similar components
