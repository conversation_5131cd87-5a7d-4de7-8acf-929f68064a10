# Testing Instructions for business-admin-fe

Author: Guidance for GitHub Copilot and contributors when creating or updating tests.

## Core Principles

1. User-centric: Tests must exercise behavior as a user would (DOM queries by role/text, not implementation details).
2. AAA Pattern: Structure every test into Arrange, Act, Assert clearly (add blank lines between sections for readability).
3. Single Responsibility: One behavioral expectation per test; split scenarios instead of asserting many unrelated things.
4. Integration Bias: Prefer integration-style component tests over shallow/unit unless a pure function is isolated.
5. Meaningful Assertions: Avoid tests written only to bump coverage; assert observable outcomes, not internal variables.
6. Avoid Library Internals: Don’t test React, react-query, Zustand, or router internals—only your usage outcomes.
7. No Snapshot Tests: Prefer explicit semantic assertions; broad or granular Jest/Vitest snapshots are disallowed to avoid brittle noise.
8. Accessibility First: Add axe (`jest-axe`) checks for each new React component or dialog surface.
9. Deterministic Data: Use mock server handlers (MSW) and mock store utilities already present under `+mock`.
10. Clean Up Redundancy: Remove or refuse duplicate tests covering the same path unless they add unique edge coverage.
11. Use `screen` for all queries; avoid destructuring results from `render`.
12. Prefer `user-event` over `fireEvent` for realistic interactions.
13. `data-testid` is a last resort when accessible queries fail.
14. No manual `cleanup`; it runs automatically.

## Directory & File Conventions

- Component tests live in a `__test__` or `__tests__` folder adjacent to the component.
- Hook tests live under `src/hooks/__test__` with naming `<hookName>.spec.tsx`.
- New component/file => create sibling `__test__/FileName.spec.tsx` (PascalCase original -> camel/pascal mixing OK, match existing style).
- Prefer `.spec.tsx` (or `.spec.ts` for pure utilities).

## Imports & Aliases

Use existing path aliases (e.g. `+hooks`, `+types`, `+utils`, `+mock/MockIndex`). Don’t import via relative deep paths if an alias exists.

## Rendering & Wrappers

Use `MockIndex` for rendering components needing providers (React Query, Router, Stores, Theme, etc.). Example:

```tsx
render(
  <MockIndex>
    <MyComponent propA="x" />
  </MockIndex>
);
```

For hooks, use `renderHook` with `createHookWrapper()` from `+mock/reactQueryHookWrapper` where applicable.

## Query Guidelines (Testing Library)

- Prefer `findByRole`, `findByText`, `findByLabelText`, `findByTestId` (only as last resort).
- Always import and query via `screen` (avoid destructuring returns from `render`) for readability and consistency.
- Prefer `@testing-library/user-event` interactions (`user.type`, `user.click`) over `fireEvent` for realism; use `fireEvent` only for edge cases user-event cannot cover (e.g. low-level events).
- Avoid `container.querySelector` unless unavoidable.
- Wait for async changes using `findBy*` or `waitFor`, not arbitrary `setTimeout`.
- `cleanup` is automatic; never call it manually.

## Async & React Query

When mutation side effects are expected (e.g. invalidation), assert on UI feedback (messages, buttons disabled/enabled). Don’t mock `useMutation` implementation; rely on MSW handlers and network layer mocks.

## Mocking Strategy

- Use MSW (`server`, `http`, `HttpResponse`) for API calls.
- All handlers reside centrally under `+mock/handlers` (no per-test or per-feature ad-hoc handler files).
- All mock data (fixtures, builders) resides in `+mock/mockData` (extend that file or related exported helpers rather than creating new scattered mock files).
- Do NOT mock implementation details of React components, hooks, or utilities under test.
- Only mock external modules for: network failure simulation, permission hooks (`useSetUserAccess`) when access gating is the behavior.
- Provide realistic payloads reflecting `+types` definitions.
- Use `server.use(...)` inside a test only to temporarily override an existing path defined in `+mock/handlers` (e.g. different status code or payload). Never introduce a brand‑new endpoint path solely inside a test—add it to central handlers instead.

## Central Mock Data & Handlers

Centralization rules:

- Handlers: define and export all endpoint handlers from `+mock/handlers` (group logically but keep under that directory).
- Mock data & builders: add to `+mock/mockData` (or exported sub-modules within the same directory if size warrants). Do not create feature-local `__test__/mocks` folders.
- Prefer builders (`buildX(overrides)`) to reduce duplication; export them from `+mock/mockData`.
- When modifying an existing mock shape, update the central source—avoid copy/paste divergence.
- Override behavior in a specific test with `server.use(existingHandlerWithDifferentResponse)` only when the path already exists centrally.
- If a new API path is needed for a feature, add it to `+mock/handlers` first, then write tests.
- Align field names & types strictly with definitions under `+types`; update mocks if the type surface changes.

### Example: Central Handler Override in a Test

```ts
// +mock/mockData (excerpt)
export const baseAccount = { id: 'acc_123', name: 'Primary Account', status: 'active' };
export const buildAccount = (overrides: Partial<typeof baseAccount> = {}) => ({ ...baseAccount, ...overrides });

// +mock/handlers (excerpt)
import { http, HttpResponse } from 'msw';
import { buildAccount } from '+mock/mockData';
export const accountHandlers = [
  http.get('/api/accounts/:id', ({ params }) => HttpResponse.json(buildAccount({ id: params.id as string }))),
];

// In a test file
import { server } from '+mock/mockServers';
import { http, HttpResponse } from 'msw';
import { buildAccount } from '+mock/mockData';

it('renders alternative account name for error scenario', async () => {
  // Override existing path with different payload (path already defined centrally)
  server.use(
    http.get('/api/accounts/:id', ({ params }) =>
      HttpResponse.json(buildAccount({ id: params.id as string, name: 'Overridden Name' }))
    )
  );
  render(<MockIndex><AccountDetails id="acc_123" /></MockIndex>);
  expect(await screen.findByText(/Overridden Name/i)).toBeInTheDocument();
});
```

Key points:

- Central handlers own the route definitions; tests only override responses.
- No new handler files per test.
- Builder reused for consistency.
- Override limited in scope to this test execution.

## State & Stores (Zustand)

Pattern summary:

- A custom `create` wrapper in `+mock/mockStore` captures each store's initial state and automatically resets it after every test (via `afterEach`).
- Production stores should import `create` from 'zustand'; tests that need reset semantics rely on the jest module factory override pointing `zustand` to the mock wrapper (already configured in the test environment).
- Persisted stores (using `persist` + `createJSONStorage`) are safe because the mock reset uses `store.setState(initialState, true)` forcing a full replace.

Guidelines:

1. Prefer interacting with store state via rendered UI (buttons, inputs) rather than calling setters directly—keeps tests user-centric.
2. If direct store manipulation is unavoidable (e.g. seeding complex baseline state):
   - Import the store hook directly (e.g. `import useChargebackStore from '+store/chargebackStore'`).
   - Use `act(() => useChargebackStore.getState().setChargebackData(fixture))` before rendering.
3. Never rely on state leakage between tests; the auto-reset ensures isolation. If a test breaks when run alone, rework it.
4. Avoid asserting against internal middleware artifacts (devtools/persist metadata). Only assert the business-facing slice values.
5. For transient updates that depend on asynchronous queues, wrap mutations in `act` and then `await waitFor` on the UI change.
6. Do not mock individual store modules; central reset logic is sufficient. Mock only external dependencies the stores consume (e.g. network layer) if necessary via MSW.
7. When adding a new store file, ensure its initial state is a plain serializable object so snapshot-like deep equality (during reset) is deterministic.

Example (seeding state explicitly when UI path is too expensive):

```tsx
import { buildChargeback } from '+mock/mockData';
import useChargebackStore from '+store/chargebackStore';

it('lists existing chargeback entries', async () => {
  const seed = buildChargeback({ id: 'cb_1', amount: 500 });
  act(() => {
    useChargebackStore.getState().setChargebackData({
      processor: 'visa',
      currency: 'USD',
      deadline: '2025-12-31',
      chargebacks: [seed]
    });
  });
  render(
    <MockIndex>
      <ChargebackList />
    </MockIndex>
  );
  expect(await screen.findByText(/visa/i)).toBeInTheDocument();
  expect(screen.getByText(/500/)).toBeInTheDocument();
});
```

If you find yourself seeding more than ~10 lines of state repeatedly, introduce a builder in `+mock/mockData` and reuse it.

## Formatting & Numeric Inputs

For currency or other formatted numeric inputs:

- Assert user-visible formatted display (e.g. `1,000.00`) rather than internal raw values.
- Trigger input changes with realistic raw user entry and assert the UI reflects formatted output and side effects (callbacks, button enablement, etc.).

## Multi-Mode / Variant Components

For components that expose multiple modes, tabs, or variants (e.g. channel types, view/edit states):

- Ensure at least one test exercises each distinct mode.
- Cover transitions between modes (state persistence vs intentional reset).
- Assert gating conditions (e.g. consent checkbox enabling a confirm button) where applicable.
- Avoid reintroducing assertions for deprecated logic paths that have been removed from the implementation.

## Accessibility (a11y)

Every new modal, panel, or major component:

```tsx
import { axe } from 'jest-axe';
const { container } = render(...);
const results = await axe(container);
expect(results).toHaveNoViolations();
```

If violations occur, fix the component rather than silencing the rule.

## Edge Cases To Cover

Consider (choose those relevant per component):

- Empty / zero / null value objects.
- Permissions denied (e.g. no Edit button).
- API error states (MSW 500 response) => error banner or message.
- Large numeric boundaries (max values) - ensure formatting still valid.
- Toggling tabs or modes (e.g. web/api) persists state or resets intentionally.

## Test Data Builders

If repetitive object shapes emerge for a feature domain, introduce a small builder in `__test__/builders.ts` within that feature folder instead of duplicating literals.

## Axe Performance

Group multiple related assertions within one test that already executed `axe`—don’t run axe in every micro-test for the same render to keep test time reasonable.

## Hooks Testing Pattern

```tsx
const { result } = renderHook(() => useFeedbackHandler(), { wrapper: createHookWrapper() });
act(() => result.current.feedbackInit({...}));
await waitFor(() => expect(result.current.data.isActive).toBe(true));
```

Never assert on internal refs or closure variables not part of the public return API.

## Failure Messaging

Use clear assertion messages only when custom logic clarifies intent. Avoid unnecessary messages that duplicate expected text.

## Lint & Types

All test code must pass TypeScript and ESLint. Prefer explicit types for complex fixture objects; let inference handle trivial literals.

## Do / Don’t Quick List

Do:

- Use `jest-axe` for new UI surfaces.
- Use MSW for HTTP.
- Assert visible strings & roles.
- Keep tests deterministic.
- Separate happy path & edge cases.
- Use `screen` consistently for queries.
- Use `user-event` over `fireEvent`.
- Focus only on user-facing behavior and interaction with the Component.
- Make sure implementation are simplified
- Reduce the comments you add to your implementation, only where necessary
- Avoid "any" and create actual typescript types for values

Don’t:

- Mock React Query internals.
- Test library implementation details.
- Chain many unrelated asserts in one test.
- Depend on internal store/private function signatures.
- Sprinkle `data-testid` liberally—treat them as last resort when accessible queries fail.
- Manually call `cleanup()`.
- Don't have implementation detail within test

## Adding New Feature Tests Checklist

1. Identify new/changed files (git diff against `staging`).
2. Determine user-facing behaviors introduced.
3. Add/augment tests in sibling `__test__` folders.
4. Add accessibility test.
5. Add edge-case scenarios (error, empty, permission denied, boundary values).
6. Ensure MSW handlers exist or extend existing ones.
7. Run `npm test` (Vitest) & ensure no violations.
8. Refactor fixtures/builders if duplication introduced.
9. Remove obsolete tests invalidated by logic changes.
10. Open PR with concise summary of behavioral coverage.

## Example Minimal Component Test

```tsx
it('enables confirm after consent', async () => {
  render(
    <MockIndex>
      <MyFeatureComponent />
    </MockIndex>
  );
  user.click(screen.getByRole('button', { name: /edit/i }));
  const confirmBtn = screen.getByRole('button', { name: /confirm/i });
  expect(confirmBtn).toBeDisabled();
  user.click(screen.getByTestId('consent-checkbox'));
  await waitFor(() => expect(confirmBtn).toBeEnabled());
});
```

## Maintenance Guidance

- Revisit tests when refactoring to remove obsolete validation or flows.
- Prefer enhancing existing tests over creating near-duplicates.
- Keep this file updated when patterns evolve (e.g., if adding RTL custom render helper, update examples here).

---

Generated: 2025-09-06. Keep current with evolving patterns.
