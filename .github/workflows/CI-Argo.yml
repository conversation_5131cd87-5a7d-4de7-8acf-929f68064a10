name: Admin Dashboard CI/CD

on:
  pull_request:
    branches: [staging, production, preprod]
  push:
    branches: [staging, production, preprod]

jobs:
  build_deploy_staging:
    if: ${{ github.ref == 'refs/heads/staging' }}
    uses: korapay/infra-workflows/.github/workflows/build_deploy.yaml@main
    secrets: inherit
    with:
      node-version: 24.x
      service: business-admin-fe
      envtype: staging
      runner-path: '/home/<USER>/korapay-client/admin'
      cluster: demeter
      argocd-service: businessadminfe-staging
      argocd-service-prefix: 01-nyx-eks
      apptype: frontend

  build_deploy_preprod:
    if: ${{ github.ref == 'refs/heads/preprod' }}
    uses: korapay/infra-workflows/.github/workflows/build_deploy.yaml@main
    secrets: inherit
    with:
      node-version: 24.x
      service: business-admin-fe
      envtype: preprod
      runner-path: '/home/<USER>/korapay-client/admin'
      cluster: demeter
      argocd-service: businessadminfe
      argocd-service-prefix: 01-nyx-eks
      apptype: frontend

  build_deploy_production:
    if: ${{ github.ref == 'refs/heads/production' }}
    uses: korapay/infra-workflows/.github/workflows/build_deploy.yaml@main
    secrets: inherit
    with:
      node-version: 24.x
      service: business-admin-fe
      envtype: production
      runner-path: '/home/<USER>/korapay-client/admin'
      cluster: gaia
      argocd-service: businessadminfe
      argocd-service-prefix: 02-gaia-eks
      apptype: frontend