import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { axe, toHaveNoViolations } from 'jest-axe';
import { http, HttpResponse } from 'msw';
import { Mock, vi } from 'vitest';

import { useSetUserAccess } from '+hooks';
import { buildEditDetailsCardProps, buildProductConfigResponse } from '+mock/factories';
import MockIndex from '+mock/MockIndex';
import { server } from '+mock/mockServers';

import EditDetailsCard from '../EditDetailsCard';

expect.extend(toHaveNoViolations);

vi.mock('+hooks/useSetUserAccess', () => ({
  default: vi.fn()
}));

const MockedEditDetailsCard = ({ children }: { children: React.ReactNode }) => {
  return <MockIndex>{children}</MockIndex>;
};

describe('EditDetailsCard', () => {
  const mockUseSetUserAccess = useSetUserAccess as Mock;
  mockUseSetUserAccess.mockReturnValue({ 'transaction_config_details.update': true });
  it('should be accessible', async () => {
    const { container } = render(
      <MockedEditDetailsCard>
        <EditDetailsCard
          title="channels"
          currency="NGN"
          category="pay-ins"
          paymentMethod="mobile_money"
          merchantId="23"
          content={['web', 'api']}
          type="Pay-ins"
          disableEdit={false}
        />
      </MockedEditDetailsCard>
    );
    // Wait for stable UI text to ensure all async state updates have flushed before axe run (mitigates act warnings)
    await screen.findByText('Channels');
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });
  it('should render correctly', () => {
    render(
      <MockedEditDetailsCard>
        <EditDetailsCard
          title="channels"
          currency="NGN"
          category="pay-ins"
          paymentMethod="mobile_money"
          merchantId="23"
          content={['web', 'api']}
          type="mobile_money"
          disableEdit={false}
        />
      </MockedEditDetailsCard>
    );
    expect(screen.getByText('Channels')).toBeInTheDocument();
    expect(
      screen.getByText(
        'Here you can find the checkout and API products for this merchant’s Mobile Money configuration. You can modify these payment channels configuration here.'
      )
    ).toBeInTheDocument();
    expect(screen.queryByText('Payment via Dashboard:')).not.toBeInTheDocument();
    expect(screen.getByText('Payment via API:')).toBeInTheDocument();
    expect(screen.getAllByText('Enabled')).toHaveLength(1);
    expect(screen.getAllByText('Disabled')).toHaveLength(1);
  });
  it('should render the available channels', () => {
    render(
      <MockedEditDetailsCard>
        <EditDetailsCard
          title="channels"
          currency="NGN"
          category="pay-ins"
          paymentMethod="mobile_money"
          merchantId="23"
          content={['web', 'api', 'modal']}
          type="mobile_money"
          disableEdit={false}
        />
      </MockedEditDetailsCard>
    );

    expect(screen.queryByText('Payment via Dashboard:')).not.toBeInTheDocument();
    expect(screen.getByText('Payment via API:')).toBeInTheDocument();
    expect(screen.getByText('Payment via Checkout:')).toBeInTheDocument();
    expect(screen.getAllByText('Enabled')).toHaveLength(2);
  });

  it('EditDetailsCard should render withdrawal limits summary', async () => {
    render(
      <MockedEditDetailsCard>
        <EditDetailsCard
          title="limits"
          currency="NGN"
          category="payouts"
          paymentMethod="bank_account"
          merchantId="23"
          content={{
            web: {
              daily: { settlement_account: 5000, non_settlement_account: 1000 },
              per_transaction: {
                settlement_account: { min: 3000, max: 6000 },
                non_settlement_account: { min: 2000, max: 4000 }
              }
            },
            api: { per_transaction: { min: 1000, max: 9000 } }
          }}
          type="bank_account"
          disableEdit
        />
      </MockedEditDetailsCard>
    );
    expect(screen.getByText(/Withdrawal Limit/i)).toBeInTheDocument();
    expect(screen.getByText('Edit')).toBeInTheDocument();
    expect(screen.getByText('5,000.00')).toBeInTheDocument();
    expect(screen.getByText('1,000.00')).toBeInTheDocument();
    expect(screen.getByText('3,000.00')).toBeInTheDocument();
    expect(screen.getByText('6,000.00')).toBeInTheDocument();
  });

  it('should show warning when per-transaction limit exceeds transaction maximum for settlement account', async () => {
    const mockConfig = buildProductConfigResponse({
      withdrawalLimitConfig: {
        web: {
          daily: { settlement_account: 5000, non_settlement_account: 6000 },
          per_transaction: {
            settlement_account: { min: 100, max: 9000000 },
            non_settlement_account: { min: 200, max: 4000 }
          }
        },
        api: { per_transaction: { min: 50, max: 500 } }
      }
    });

    server.use(http.get('/admin/settings/merchants/configuration', () => HttpResponse.json(mockConfig)));

    const props = buildEditDetailsCardProps({
      content: {
        web: {
          daily: { settlement_account: 5000, non_settlement_account: 6000 },
          per_transaction: {
            settlement_account: { min: 100, max: 9000000 },
            non_settlement_account: { min: 200, max: 4000 }
          }
        },
        api: { per_transaction: { min: 50, max: 500 } },
        limit: { min: 10_000, max: 100_000 }
      },
      type: 'bank_account',
      disableEdit: true
    });

    render(
      <MockedEditDetailsCard>
        <EditDetailsCard {...props} />
      </MockedEditDetailsCard>
    );

    const user = userEvent.setup();
    await user.click(screen.getByText('Edit'));
    // Await modal heading to ensure state updates settle before interacting (eliminates act warnings)
    await screen.findByText(/Edit Withdrawal Limit/i);

    const consentCheckbox = screen.getByTestId('consent-checkbox');
    await user.click(consentCheckbox);

    expect(screen.getByTestId('second-button')).toBeEnabled();
  });

  it('should show warning when per-transaction limit exceeds daily limit', async () => {
    const mockConfig = buildProductConfigResponse({
      withdrawalLimitConfig: {
        web: {
          daily: { settlement_account: 5000, non_settlement_account: 2000 },
          per_transaction: {
            settlement_account: { min: 100, max: 6000 },
            non_settlement_account: { min: 200, max: 4000 }
          }
        },
        api: { per_transaction: { min: 50, max: 500 } }
      }
    });

    server.use(http.get('/admin/settings/merchants/configuration', () => HttpResponse.json(mockConfig)));

    const props = buildEditDetailsCardProps({
      content: {
        web: {
          daily: { settlement_account: 5000, non_settlement_account: 2000 },
          per_transaction: {
            settlement_account: { min: 100, max: 6000 },
            non_settlement_account: { min: 200, max: 4000 }
          }
        },
        api: { per_transaction: { min: 50, max: 500 } },
        limit: { min: 1000, max: 100_000 }
      },
      type: 'bank_account',
      disableEdit: true
    });

    render(
      <MockedEditDetailsCard>
        <EditDetailsCard {...props} />
      </MockedEditDetailsCard>
    );

    const user = userEvent.setup();
    await user.click(screen.getByText('Edit'));
    await screen.findByText(/Edit Withdrawal Limit/i);

    const consentCheckbox = screen.getByTestId('consent-checkbox');
    await user.click(consentCheckbox);

    expect(screen.getByTestId('second-button')).toBeEnabled();
  });

  it('should show warning when daily limit is set below per-transaction maximum', async () => {
    const mockConfig = buildProductConfigResponse({
      withdrawalLimitConfig: {
        web: {
          daily: { settlement_account: 5000, non_settlement_account: 4000 },
          per_transaction: {
            settlement_account: { min: 100, max: 6000 },
            non_settlement_account: { min: 200, max: 4000 }
          }
        },
        api: { per_transaction: { min: 50, max: 500 } }
      }
    });

    server.use(http.get('/admin/settings/merchants/configuration', () => HttpResponse.json(mockConfig)));

    const props = buildEditDetailsCardProps({
      content: {
        web: {
          daily: { settlement_account: 5000, non_settlement_account: 4000 },
          per_transaction: {
            settlement_account: { min: 100, max: 6000 },
            non_settlement_account: { min: 200, max: 4000 }
          }
        },
        api: { per_transaction: { min: 50, max: 500 } },
        limit: { min: 1000, max: 100_000 }
      },
      type: 'bank_account',
      disableEdit: true
    });

    render(
      <MockedEditDetailsCard>
        <EditDetailsCard {...props} />
      </MockedEditDetailsCard>
    );

    const user = userEvent.setup();
    await user.click(screen.getByText('Edit'));
    await screen.findByText(/Edit Withdrawal Limit/i);

    const consentCheckbox = screen.getByTestId('consent-checkbox');
    await user.click(consentCheckbox);

    expect(screen.getByTestId('second-button')).toBeEnabled();
  });

  it('should enable confirm button when consent is given and validation passes', async () => {
    let payload: unknown;

    server.use(
      http.get('/admin/settings/merchants/configuration', () =>
        HttpResponse.json({
          data: {
            setting: {
              transaction: {
                disbursement: {
                  bank_account: {
                    limits: {
                      web: {
                        daily: { settlement_account: 5000, non_settlement_account: 4000 },
                        per_transaction: {
                          settlement_account: { min: 100, max: 3000 },
                          non_settlement_account: { min: 200, max: 1000 }
                        }
                      },
                      api: { per_transaction: { min: 50, max: 500 } }
                    },
                    transaction_limit: { min: 100, max: 100000 }
                  }
                }
              }
            }
          }
        })
      )
    );
    server.use(
      http.patch('/admin/settings/merchants/configuration', async ({ request }) => {
        payload = await request.json();
        return HttpResponse.json({}, { status: 200 });
      })
    );
    render(
      <MockedEditDetailsCard>
        <EditDetailsCard
          title="limits"
          currency="NGN"
          category="payouts"
          paymentMethod="bank_account"
          merchantId="23"
          content={{
            web: {
              daily: { settlement_account: 5000, non_settlement_account: 4000 },
              per_transaction: {
                settlement_account: { min: 100, max: 3000 },
                non_settlement_account: { min: 200, max: 1000 }
              }
            },
            api: { per_transaction: { min: 50, max: 500 } },
            limit: { min: 1000, max: 100000 }
          }}
          type="bank_account"
          disableEdit
        />
      </MockedEditDetailsCard>
    );

    const user = userEvent.setup();
    await user.click(screen.getByText('Edit'));
    await screen.findByText(/Edit Withdrawal Limit/i);

    // Fill form inputs with proper user interactions
    await user.clear(screen.getByPlaceholderText('Enter daily settlement_account value'));
    await user.type(screen.getByPlaceholderText('Enter daily settlement_account value'), '5000');

    await user.clear(screen.getByPlaceholderText('Enter daily non_settlement_account value'));
    await user.type(screen.getByPlaceholderText('Enter daily non_settlement_account value'), '4000');

    await user.clear(screen.getByPlaceholderText('Enter settlement min value'));
    await user.type(screen.getByPlaceholderText('Enter settlement min value'), '100');

    await user.clear(screen.getByPlaceholderText('Enter settlement max value'));
    await user.type(screen.getByPlaceholderText('Enter settlement max value'), '3000');

    await user.clear(screen.getByPlaceholderText('Enter non-settlement min value'));
    await user.type(screen.getByPlaceholderText('Enter non-settlement min value'), '200');

    await user.clear(screen.getByPlaceholderText('Enter non-settlement max value'));
    await user.type(screen.getByPlaceholderText('Enter non-settlement max value'), '1000');

    const consentCheckbox = screen.getByTestId('consent-checkbox');
    await user.click(consentCheckbox);

    await waitFor(() => expect(screen.getByTestId('second-button')).toBeEnabled());
    await user.click(screen.getByTestId('second-button'));

    await waitFor(() => {
      expect(payload).toMatchObject({
        currency: 'NGN',
        payment_type: 'disbursement',
        payment_method: 'bank_account',
        type: 'single_merchant',
        data: {
          enabled: true,
          limits: {
            web: {
              daily: { settlement_account: 50, non_settlement_account: 40 },
              per_transaction: {
                settlement_account: { min: 1, max: 30 },
                non_settlement_account: { min: 2, max: 10 }
              }
            }
          }
        },
        account_id: '23'
      });
      // Ensure api section is intentionally omitted in optimized payload
      expect((payload as any).data.limits.api).toBeUndefined();
    });

    await waitFor(() => {
      expect(screen.getByText('You have made changes to the Withdrawal limit for NGN Bank Account')).toBeInTheDocument();
    });
  });

  it('should show warning when per-transaction minimum is below allowed minimum for settlement account', async () => {
    server.use(
      http.get('/admin/settings/merchants/configuration', () =>
        HttpResponse.json({
          data: {
            setting: {
              transaction: {
                disbursement: {
                  bank_account: {
                    limits: {
                      web: {
                        daily: { settlement_account: 9000, non_settlement_account: 8000 },
                        per_transaction: {
                          settlement_account: { min: 100, max: 6000 },
                          non_settlement_account: { min: 200, max: 4000 }
                        }
                      },
                      api: { per_transaction: { min: 50, max: 500 } }
                    },
                    transaction_limit: { min: 150, max: 100000 }
                  }
                }
              }
            }
          }
        })
      )
    );
    render(
      <MockedEditDetailsCard>
        <EditDetailsCard
          title="limits"
          currency="NGN"
          category="payouts"
          paymentMethod="bank_account"
          merchantId="23"
          content={{
            web: {
              daily: { settlement_account: 9000, non_settlement_account: 8000 },
              per_transaction: {
                settlement_account: { min: 100, max: 6000 },
                non_settlement_account: { min: 200, max: 4000 }
              }
            },
            api: { per_transaction: { min: 50, max: 500 } },
            limit: { min: 200, max: 100000 }
          }}
          type="bank_account"
          disableEdit
        />
      </MockedEditDetailsCard>
    );

    const user = userEvent.setup();
    await user.click(screen.getByText('Edit'));
    await screen.findByText(/Edit Withdrawal Limit/i);

    const settlementMinInput = screen.getByPlaceholderText('Enter settlement min value');
    await user.clear(settlementMinInput);
    await user.type(settlementMinInput, '100');

    const consentCheckbox = screen.getByTestId('consent-checkbox');
    await user.click(consentCheckbox);

    expect(screen.getByTestId('second-button')).toBeEnabled();
  });

  it('should initialize withdrawal limits to zero for custom merchants', async () => {
    render(
      <MockedEditDetailsCard>
        <EditDetailsCard
          title="limits"
          currency="NGN"
          category="payouts"
          paymentMethod="bank_account"
          merchantId="23"
          content={{
            web: {
              daily: { settlement_account: 0, non_settlement_account: 0 },
              per_transaction: {
                settlement_account: { min: 0, max: 0 },
                non_settlement_account: { min: 0, max: 0 }
              }
            },
            api: { per_transaction: { min: 0, max: 0 } },
            global: { daily: 0 }
          }}
          type="bank_account"
          disableEdit
        />
      </MockedEditDetailsCard>
    );

    const user = userEvent.setup();
    await user.click(screen.getByText('Edit'));
    await screen.findByText(/Edit Withdrawal Limit/i);

    expect(screen.getByPlaceholderText('Enter daily settlement_account value')).toHaveValue('0.00');
    expect(screen.getByPlaceholderText('Enter daily non_settlement_account value')).toHaveValue('0.00');
    expect(screen.getByPlaceholderText('Enter settlement min value')).toHaveValue('0.00');
    expect(screen.getByPlaceholderText('Enter settlement max value')).toHaveValue('0.00');
    expect(screen.getByPlaceholderText('Enter non-settlement min value')).toHaveValue('0.00');
    expect(screen.getByPlaceholderText('Enter non-settlement max value')).toHaveValue('0.00');
  });

  it('should not show Edit button when user lacks update permission', async () => {
    const mockUseSetUserAccess = useSetUserAccess as Mock;
    mockUseSetUserAccess.mockReturnValue({ 'transaction_config_details.update': false });
    render(
      <MockedEditDetailsCard>
        <EditDetailsCard
          title="channels"
          currency="NGN"
          category="pay-ins"
          paymentMethod="mobile_money"
          merchantId="23"
          content={['web', 'api', 'modal']}
          type="mobile_money"
          disableEdit
        />
      </MockedEditDetailsCard>
    );
    expect(screen.queryByText('Edit')).not.toBeInTheDocument();
  });

  describe('Accessibility', () => {
    it('should have no accessibility violations', async () => {
      const props = buildEditDetailsCardProps({
        disableEdit: false
      });

      const { container } = render(
        <MockedEditDetailsCard>
          <EditDetailsCard {...props} />
        </MockedEditDetailsCard>
      );

      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });
  });
});
