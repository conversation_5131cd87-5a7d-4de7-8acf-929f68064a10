import React, { PropsWithChildren } from 'react';
import { act, renderHook, waitFor } from '@testing-library/react';
import createHookWrapper from '+mock/reactQueryHookWrapper';
import { MemoryRouter } from 'react-router-dom';
import { http, HttpResponse } from 'msw';
import { describe, expect, it, vi } from 'vitest';

import { buildConfigPayload, buildTransactionLimit, buildWithdrawalLimitConfig } from '+mock/factories';
import { server } from '+mock/mockServers';
import { ProductConfigSettingsType } from '+types';

import useLimit from '../useLimit';

const overrideSuccess = (payload: { data: Partial<ProductConfigSettingsType> }) => {
  server.use(http.get('/admin/settings/merchants/configuration', () => HttpResponse.json(payload)));
};

vi.mock('react-router-dom', async original => {
  const actual = (await original()) as Record<string, unknown>;
  return { ...actual, useParams: () => ({ currency: 'NGN', merchant_id: '23' }) };
});

const buildWrapper = (initialEntries = ['/']) => {
  const rqWrapper = createHookWrapper();
  return ({ children }: PropsWithChildren<{}>) => <MemoryRouter initialEntries={initialEntries}>{rqWrapper({ children })}</MemoryRouter>;
};

describe('useLimit hook', () => {
  it('should initialize with correct tab name and withdrawal data for global limit type', async () => {
    overrideSuccess(buildConfigPayload() as { data: Partial<ProductConfigSettingsType> });
    const wrapper = buildWrapper();
    const { result } = renderHook(() => useLimit('global'), { wrapper });
    if (!result.current.withdrawalLimitData) {
      act(() => result.current.setWithdrawalLimitData(buildWithdrawalLimitConfig()));
    }
    await waitFor(() => expect(result.current.withdrawalLimitData).toBeTruthy());
    expect(result.current.getActiveTabName()).toMatch(/Web Limit|API Limit|Global Limit/);
    expect(result.current.activeWithdrawalLimitType).toBe('web');
  });

  it('should flag validation error when per-transaction limit exceeds maximum for product type', async () => {
    const configWithHighLimits = buildConfigPayload({
      data: {
        setting: {
          transaction: {
            disbursement: {
              limits: {
                web: {
                  daily: { settlement_account: 100000, non_settlement_account: 80000 },
                  per_transaction: {
                    settlement_account: buildTransactionLimit({ min: 100, max: 6000 }),
                    non_settlement_account: buildTransactionLimit({ min: 200, max: 900 })
                  }
                },
                api: { per_transaction: buildTransactionLimit({ min: 50, max: 500 }) },
                global: { daily: 200000 }
              },
              bank_account: {
                limits: {
                  web: {
                    daily: { settlement_account: 100000, non_settlement_account: 80000 },
                    per_transaction: {
                      settlement_account: buildTransactionLimit({ min: 100, max: 6000 }),
                      non_settlement_account: buildTransactionLimit({ min: 200, max: 900 })
                    }
                  },
                  api: { per_transaction: buildTransactionLimit({ min: 50, max: 500 }) }
                },
                transaction_limit: buildTransactionLimit({ min: 10, max: 5000 })
              }
            }
          }
        }
      }
    });
    overrideSuccess(configWithHighLimits as { data: Partial<ProductConfigSettingsType> });
    const wrapper = buildWrapper(['/?productCategory=bank_account']);
    const { result } = renderHook(() => useLimit('product'), { wrapper });
    act(() =>
      result.current.setWithdrawalLimitData(
        buildWithdrawalLimitConfig({
          web: {
            daily: { settlement_account: 100000, non_settlement_account: 80000 },
            per_transaction: {
              settlement_account: buildTransactionLimit({ min: 100, max: 6000 }),
              non_settlement_account: buildTransactionLimit({ min: 200, max: 900 })
            }
          },
          api: { per_transaction: buildTransactionLimit({ min: 50, max: 500 }) },
          limit: buildTransactionLimit({ min: 10, max: 5000 })
        })
      )
    );
    await waitFor(() => expect(result.current.anyValidationError).toBe(true));
  });

  it('should update active limit type when user changes tabs', async () => {
    overrideSuccess(buildConfigPayload() as { data: Partial<ProductConfigSettingsType> });
    const wrapper = buildWrapper();
    const { result } = renderHook(() => useLimit('global'), { wrapper });
    act(() => result.current.setWithdrawalLimitData(buildWithdrawalLimitConfig()));
    await waitFor(() => expect(result.current.withdrawalLimitData).toBeTruthy());
    act(() => result.current.handleTabChange({ value: 'api' }));
    await waitFor(() => expect(result.current.activeWithdrawalLimitType).toBe('api'));
  });

  it('should handle API errors gracefully when configuration fetch fails', async () => {
    server.use(
      http.get('/admin/settings/merchants/configuration', () => {
        return new Response(null, { status: 500 });
      })
    );

    const wrapper = buildWrapper();
    const { result } = renderHook(() => useLimit('global'), { wrapper });
    await waitFor(() => expect(result.current).toBeDefined());
  });

  it('should handle network failures when configuration API is unavailable', async () => {
    server.use(
      http.get('/admin/settings/merchants/configuration', () => {
        throw new Error('Network error');
      })
    );
    const wrapper = buildWrapper(['/?productCategory=bank_account']);
    const { result } = renderHook(() => useLimit('product'), { wrapper });
    await waitFor(() => expect(result.current).toBeDefined());
  });

  it('should validate settlement account limits correctly with edge values', async () => {
    const edgeConfig = buildConfigPayload({
      data: {
        setting: {
          transaction: {
            disbursement: {
              limits: {
                web: {
                  daily: { settlement_account: 0, non_settlement_account: 1000 },
                  per_transaction: {
                    settlement_account: buildTransactionLimit({ min: 1, max: ********* }),
                    non_settlement_account: buildTransactionLimit({ min: 1, max: 1000 })
                  }
                },
                api: { per_transaction: buildTransactionLimit({ min: 1, max: ********* }) },
                global: { daily: 200000 }
              },
              bank_account: {
                limits: {
                  web: {
                    daily: { settlement_account: 0, non_settlement_account: 1000 },
                    per_transaction: {
                      settlement_account: buildTransactionLimit({ min: 1, max: ********* }),
                      non_settlement_account: buildTransactionLimit({ min: 1, max: 1000 })
                    }
                  },
                  api: { per_transaction: buildTransactionLimit({ min: 1, max: ********* }) }
                },
                transaction_limit: buildTransactionLimit({ min: 1, max: ********* })
              }
            }
          }
        }
      }
    });
    overrideSuccess(edgeConfig as { data: Partial<ProductConfigSettingsType> });
    const wrapper = buildWrapper(['/?productCategory=bank_account']);
    const { result } = renderHook(() => useLimit('product'), { wrapper });
    await waitFor(() => expect(result.current.withdrawalLimitData).toBeTruthy());
    act(() =>
      result.current.setWithdrawalLimitData(
        buildWithdrawalLimitConfig({
          web: {
            daily: { settlement_account: **********, non_settlement_account: 1000 },
            per_transaction: {
              settlement_account: buildTransactionLimit({ min: 1, max: ********* }),
              non_settlement_account: buildTransactionLimit({ min: 1, max: 1000 })
            }
          },
          api: { per_transaction: buildTransactionLimit({ min: 1, max: ********* }) },
          limit: buildTransactionLimit({ min: 1, max: ********* })
        })
      )
    );
    await waitFor(() => {
      expect(result.current.withdrawalLimitData?.web?.daily?.settlement_account).toBe(**********);
      expect(result.current.withdrawalLimitData?.web?.per_transaction?.settlement_account?.max).toBe(*********);
      expect(result.current.withdrawalLimitData?.limit?.max).toBe(*********);
      expect(result.current.anyValidationError).toBe(false);
    });
  });

  it('should reset withdrawalLimitData to original config when resetWithdrawalLimitData is called', async () => {
    const baseConfig = buildConfigPayload({
      data: {
        setting: {
          transaction: {
            disbursement: {
              limits: {
                web: {
                  daily: { settlement_account: 50000, non_settlement_account: 40000 },
                  per_transaction: {
                    settlement_account: buildTransactionLimit({ min: 100, max: 1000 }),
                    non_settlement_account: buildTransactionLimit({ min: 200, max: 900 })
                  }
                },
                api: { per_transaction: buildTransactionLimit({ min: 50, max: 500 }) },
                global: { daily: 200000 }
              },
              bank_account: {
                limits: {
                  web: {
                    daily: { settlement_account: 50000, non_settlement_account: 40000 },
                    per_transaction: {
                      settlement_account: buildTransactionLimit({ min: 100, max: 1000 }),
                      non_settlement_account: buildTransactionLimit({ min: 200, max: 900 })
                    }
                  },
                  api: { per_transaction: buildTransactionLimit({ min: 50, max: 500 }) }
                },
                transaction_limit: buildTransactionLimit({ min: 10, max: 1200 })
              }
            }
          }
        }
      }
    });
    overrideSuccess(baseConfig as { data: Partial<ProductConfigSettingsType> });
    const wrapper = buildWrapper(['/?productCategory=bank_account']);
    const { result } = renderHook(() => useLimit('product'), { wrapper });
    await waitFor(() => expect(result.current.withdrawalLimitData).toBeTruthy());
    const originalMin = result.current.withdrawalLimitData?.web?.per_transaction?.settlement_account?.min;

    act(() =>
      result.current.setWithdrawalLimitData({
        ...(result.current.withdrawalLimitData as any),
        web: {
          daily: result.current.withdrawalLimitData?.web?.daily as { settlement_account: number; non_settlement_account: number },
          per_transaction: {
            settlement_account: { min: 9999, max: 9999 },
            non_settlement_account: { min: 8888, max: 8888 }
          }
        }
      })
    );
    await waitFor(() => expect(result.current.withdrawalLimitData?.web?.per_transaction?.settlement_account?.min).toBe(9999));

    act(() => result.current.resetWithdrawalLimitData());
    await waitFor(() => expect(result.current.withdrawalLimitData?.web?.per_transaction?.settlement_account?.min).toBe(originalMin));
  });
});
