import React from 'react';

import { WithdrawalLimitType, WithdrawalLimitTypeKey } from '+types';
import { capitalize, formatAmount } from '+utils';

import { defaultWithdrawalLimits, limitsLabel } from '../../ProductConfigHelper/withdrawalLimitHelpers';

const WithdrawalLimitDetails = ({
  content,
  selectedWithdrawalType
}: {
  content: WithdrawalLimitType;
  selectedWithdrawalType: WithdrawalLimitTypeKey;
}) => {
  const withdrawalLimit = defaultWithdrawalLimits(content);

  return Object.entries(withdrawalLimit[selectedWithdrawalType]).map(([keys, values]) => {
    return (
      <div key={keys} className="withdrawal-limit-details">
        {selectedWithdrawalType === 'api' && (
          <div key={keys} className="menu">
            <p>{limitsLabel[keys as keyof typeof limitsLabel]}:</p> <span>{formatAmount(Number(values ?? 0))}</span>
          </div>
        )}
        {selectedWithdrawalType === 'web' && (
          <>
            <p className="withdrawal-limit-details__header">{capitalize(keys)}</p>
            {Object.entries(values).map(([key, value]) => {
              return (
                <div key={key} className="menu">
                  <p>{limitsLabel[key as keyof typeof limitsLabel]}:</p> <span>{formatAmount(Number(value ?? 0))}</span>
                </div>
              );
            })}
          </>
        )}
      </div>
    );
  });
};

export default WithdrawalLimitDetails;
