import React, { Fragment, useLayoutEffect, useState } from 'react';

import EmptyStateComponent from '+containers/Dashboard/Shared/EmptyState';
import Typography from '+containers/Dashboard/Shared/Typography';
import { useReducerState, useSearchQuery } from '+hooks';
import {
  availableProductType,
  CurrencyType,
  modalState,
  productCategoriesPropTypeContentWithoutEnabled,
  productCategoriesType,
  ProductConfigType,
  ProductMethodConfigType,
  TransactionLimitType,
  updateProductConfigDataTypeWithoutEnabled,
  WithdrawalLimitType
} from '+types';
import {
  availableCurrency,
  availableProductCategories,
  capitalize,
  capitalizeRemovedash,
  CATEGORIES,
  CONFIG_TITLES,
  currentProductConfigSettings,
  filteredOutObjectProperty,
  PAYMENT_METHODS,
  productMapping
} from '+utils';

import EditDetailsCard from '../../components/EditDetailsCard';
import MerchantCurrencyModal from '../../MerchantsTable/components/MerchantCurrencyModal';

import './index.scss';

const ProductCategories = ({
  config,
  type,
  currency,
  merchantId,
  merchantsStatus
}: {
  config: ProductConfigType;
  type: productCategoriesType;
  currency: CurrencyType;
  merchantId: string;
  merchantsStatus: boolean;
}) => {
  const searchQuery = useSearchQuery<{ productCategory: availableProductType }>();
  const [selectedContent, setSelectedContent] = useState<ProductMethodConfigType | undefined>();
  const [state, setState] = useReducerState<{ activeModal: modalState }>({
    activeModal: null
  });
  const [transactionLimit, setTransactionLimit] = useState<TransactionLimitType | undefined>();
  const [bankAccountChannels, setBankAccountChannels] = useState<string[] | undefined>();
  const itemsToRemove = ['enabled', 'limits'];
  const newConfig = config ? filteredOutObjectProperty(config, itemsToRemove) : {};
  const availableCategories = Object.keys(newConfig);
  const currentProductCategory = searchQuery.value.productCategory;
  const firstAvailableCategory = availableCategories[0] as availableProductType;

  const productCategory =
    currentProductCategory && availableCategories.includes(currentProductCategory)
      ? currentProductCategory
      : (firstAvailableCategory ?? '');

  useLayoutEffect(() => {
    if (config) {
      if ([availableCurrency.USD].includes(currency)) {
        itemsToRemove.push(PAYMENT_METHODS.BANK_ACCOUNT);
      }

      if ((!currentProductCategory || !availableCategories.includes(currentProductCategory)) && firstAvailableCategory) {
        searchQuery.setQuery({ productCategory: firstAvailableCategory });
        return;
      }

      setSelectedContent(newConfig[productCategory]);
      if (
        type === CATEGORIES.PAYOUTS &&
        [PAYMENT_METHODS.BANK_ACCOUNT, PAYMENT_METHODS.MOBILE_MONEY].includes(
          productCategory as typeof PAYMENT_METHODS.BANK_ACCOUNT | typeof PAYMENT_METHODS.MOBILE_MONEY
        )
      ) {
        setTransactionLimit({
          max: newConfig[productCategory]?.transaction_limit?.max,
          min: newConfig[productCategory]?.transaction_limit?.min
        });

        // Extract bank account channels if available
        if (productCategory === PAYMENT_METHODS.BANK_ACCOUNT) {
          setBankAccountChannels(newConfig[productCategory]?.channels);
        }
      }
    }
  }, [config, currentProductCategory]);

  const handleSelectTab = (key: Exclude<availableProductType, 'boolean'>, value: Exclude<ProductMethodConfigType, 'boolean'>) => {
    searchQuery.setQuery({ productCategory: key });
    setSelectedContent(value);
  };

  return (
    <section className="product-cat__container">
      {Object.entries(filteredOutObjectProperty(config, itemsToRemove)).length > 0 ? (
        <>
          <div className="product-cat__container-menu">
            <p>Product categories</p>
            <ul>
              {config &&
                Object.entries(config).map(([key, value]) => {
                  if (['enabled', 'transaction_limit', 'limits'].includes(key)) return null;
                  return (
                    <li className={productCategory === key ? '--active' : ''} key={key}>
                      <span
                        role="button"
                        onClick={() =>
                          handleSelectTab(
                            key as Exclude<availableProductType, 'boolean'>,
                            value as unknown as Exclude<ProductMethodConfigType, 'boolean'>
                          )
                        }
                        onKeyDown={() =>
                          handleSelectTab(
                            key as Exclude<availableProductType, 'boolean'>,
                            value as unknown as Exclude<ProductMethodConfigType, 'boolean'>
                          )
                        }
                        tabIndex={0}
                      >
                        {capitalizeRemovedash(key)}
                      </span>
                    </li>
                  );
                })}
            </ul>
          </div>

          {selectedContent && productCategory ? (
            <div className="product-cat__container-content">
              <Typography variant="h3" className="content-header">{`${capitalizeRemovedash(productCategory)} Configuration`}</Typography>
              <label className="content-action">
                <input
                  checked={selectedContent?.enabled}
                  type="checkbox"
                  onChange={() => setState({ activeModal: selectedContent?.enabled ? 'disable-channel' : 'enable-channel' })}
                  disabled={!merchantsStatus || !config.enabled}
                  style={{ cursor: `${!merchantsStatus || !config.enabled ? 'not-allowed' : 'pointer'}` }}
                />
                <span>{`Allow this merchant to  ${
                  availableProductCategories[type] === availableProductCategories.payouts ? 'make' : 'receive'
                } ${capitalizeRemovedash(productCategory)} ${type} in ${currency}`}</span>
              </label>
              {Object.entries(selectedContent).map(([key, value]) => {
                let newValue = value;
                if (
                  !currentProductConfigSettings.includes(key) ||
                  (['pay-ins'].includes(type) && ['transaction_limit'].includes(key) && ['disbursement_wallet'].includes(productCategory))
                )
                  return null;
                if (
                  type === CATEGORIES.PAYOUTS &&
                  [PAYMENT_METHODS.BANK_ACCOUNT, PAYMENT_METHODS.MOBILE_MONEY].includes(
                    productCategory as typeof PAYMENT_METHODS.BANK_ACCOUNT | typeof PAYMENT_METHODS.MOBILE_MONEY
                  ) &&
                  key === CONFIG_TITLES.LIMITS
                ) {
                  newValue = {
                    ...(value as WithdrawalLimitType),
                    limit: transactionLimit
                  } satisfies WithdrawalLimitType;
                }
                return (
                  <Fragment key={key}>
                    <EditDetailsCard
                      title={key as updateProductConfigDataTypeWithoutEnabled}
                      content={newValue as unknown as productCategoriesPropTypeContentWithoutEnabled}
                      paymentMethod={productCategory}
                      type={capitalize(productCategory)}
                      category={type}
                      merchantId={merchantId}
                      currency={currency}
                      disableEdit={merchantsStatus && config.enabled && selectedContent?.enabled}
                    />{' '}
                  </Fragment>
                );
              })}
            </div>
          ) : (
            <EmptyStateComponent message="Channels Not Available" />
          )}
        </>
      ) : (
        <EmptyStateComponent message="Product Categories Not Available" />
      )}
      {state.activeModal && (
        <MerchantCurrencyModal
          activeModal={state.activeModal}
          setActiveModal={() => setState({ activeModal: null })}
          currency={currency}
          merchantId={merchantId}
          productType={productMapping[type]}
          method={productCategory}
          hideButton
        />
      )}
    </section>
  );
};

export default ProductCategories;
