import React, { useEffect, useState } from 'react';
import { useMutation } from 'react-query';

import APIRequest from '+services/api-services';
import Modal from '+shared/Modal';
import TabSwitch from '+shared/TabSwitch';
import { IError, IModalProps, updateProductConfigData, WithdrawalLimitType } from '+types';
import { logError, productMapping } from '+utils';

import useLimit from '../../../components/hooks/useLimit';
import WithdrawalLimitContent from '../../../components/WithdrawalLimit/WithdrawalLimitContent';
import { invalidateProductConfigQueries } from '../../../ProductConfigHelper';
import { buildLimitsPayload } from '../../../ProductConfigHelper/withdrawalLimitHelpers';

const apiRequest = new APIRequest();

const radioLabel = {
  default: 'for only merchants under the default configuration',
  all_merchants: 'for all merchants',
  custom_merchants: 'for custom merchants'
};

const accessContent = ['default', 'all_merchants', 'custom_merchants'];

const GlobalLimitModal = ({
  isOpen,
  onClose,
  modalType = 'default'
}: {
  isOpen: boolean;
  onClose: () => void;
  modalType?: 'access' | 'default' | null;
}) => {
  const {
    getActiveTabName,
    handleTabChange,
    withdrawalLimitData,
    anyValidationError,
    activeWithdrawalLimitType,
    handleWithdrawalLimitChange,
    feedbackInit,
    queryClient,
    consent,
    setConsent,
    selected,
    setSelected,
    activeCurrency,
    category,
    merchant_id,
    getTabOptions,
    resetWithdrawalLimitData
  } = useLimit('global');
  const [activeModalType, setActiveModalType] = useState(modalType ?? 'default');

  useEffect(() => {
    if (modalType !== null) setActiveModalType(modalType);
  }, [modalType]);

  const updateProductConfiguration = useMutation((value: updateProductConfigData) => apiRequest.updateProductConfiguration(value), {
    onSuccess: () => {
      invalidateProductConfigQueries({
        queryClient,
        currency: activeCurrency,
        category,
        merchantId: merchant_id || undefined,
        paymentMethod: undefined // global modal does not scope to a payment method
      });
      setConsent(false);
      handleTabChange({ value: 'web' });
    },
    onError: error => {
      logError(error);
      feedbackInit({
        message: `${(error as IError)?.response?.data?.message || "There has been an error updating merchant's configuration"}`,
        type: 'danger',
        componentLevel: true
      });
    }
  });

  const allAccessContent = () => {
    return (
      <div className="currency-modal__content">
        <div className="radio_container">
          {accessContent.map(item => {
            return (
              <label key={item}>
                <input checked={item === selected} type="radio" onChange={() => setSelected(item as updateProductConfigData['type'])} />
                {`Update ${radioLabel[item as keyof typeof radioLabel]}`}
              </label>
            );
          })}
        </div>
      </div>
    );
  };
  const modalDetails = () => ({
    access: {
      heading: `Update ${activeCurrency} ${category} withdrawal limit`,
      description: 'Please select an option to continue',
      secondButtonText: 'Continue',
      content: allAccessContent(),
      secondButtonDisable: !selected,
      secondButtonActionIsTerminal: false,
      secondButtonAction: () => {
        setActiveModalType('default');
      },
      size: 'mdBase',
      close: () => {
        onClose();
        setConsent(false);
        resetWithdrawalLimitData();
      }
    },
    default: {
      heading: `Edit Global Withdrawal Limit For ${activeCurrency} Payouts`,
      description: `You can edit the limits for this ${activeCurrency} Payouts by clicking into the input fields.`,
      secondButtonText: 'Confirm & Edit',
      completedDescription: `You have made changes to the Global Withdrawal Limit for ${activeCurrency} Payouts`,
      completedHeading: 'Success',
      close: () => {
        onClose();
        setConsent(false);
        handleTabChange({ value: 'web' });
        resetWithdrawalLimitData();
      },
      content: (
        <div className="currency-modal__content">
          <div className="editable-card__tab mt-2">
            <TabSwitch
              options={getTabOptions()}
              setTab={value => handleTabChange(value as { value: string })}
              activeTab={getActiveTabName()}
              showDropDown={false}
              identifier="label"
            />
          </div>
          <WithdrawalLimitContent
            content={withdrawalLimitData as WithdrawalLimitType}
            handleWithdrawalLimitChange={handleWithdrawalLimitChange}
            merchantConfigType={selected}
            selectedWithdrawalType={activeWithdrawalLimitType}
          />
          <label className="prod-consent-wrapper">
            <input checked={consent} type="checkbox" onChange={() => setConsent(!consent)} data-testid="consent-checkbox" />
            <span>Yes, I understand the implications of this action</span>
          </label>
        </div>
      ),
      isScrollable: true,
      secondButtonDisable: !consent || anyValidationError,
      secondButtonAction: async () => {
        if (anyValidationError) return;
        const limitsPayload: any = buildLimitsPayload(withdrawalLimitData as WithdrawalLimitType, activeWithdrawalLimitType);
        return updateProductConfiguration.mutateAsync({
          currency: activeCurrency,
          payment_type: productMapping[category],
          type: selected ?? 'single_merchant',
          data: {
            enabled: true,
            limits: limitsPayload
          },
          account_id: merchant_id ?? undefined
        });
      }
    }
  });
  return (
    <Modal
      visible={isOpen}
      size="md"
      completedModalSize="base"
      equalFooterBtn
      showCompleteActionText
      secondaryCompletedModal
      {...(modalDetails()[activeModalType] as IModalProps)}
    />
  );
};

export default GlobalLimitModal;
