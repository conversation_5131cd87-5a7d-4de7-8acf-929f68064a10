import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { axe } from 'jest-axe';
import { http, HttpResponse } from 'msw';
import { describe, expect, it, vi } from 'vitest';

import { buildConfigPayload } from '+mock/factories';
import MockIndex from '+mock/MockIndex';
import { server } from '+mock/mockServers';

import GlobalLimitModal from '../index';

server.use(
  http.get(/product-configuration/, () => {
    return HttpResponse.json(buildConfigPayload());
  })
);
const onClose = vi.fn();

const setup = () =>
  render(
    <MockIndex>
      <GlobalLimitModal isOpen onClose={onClose} />
    </MockIndex>
  );
describe('GlobalLimitModal', () => {
  it('renders and passes basic a11y check', async () => {
    const { container } = setup();
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });

  it('consent checkbox toggles', async () => {
    setup();
    const consent = screen.getByTestId('consent-checkbox') as HTMLInputElement;
    expect(consent.checked).toBe(false);
    await userEvent.click(consent);
    expect(consent.checked).toBe(true);
  });

  it('switches tabs when user selects API Limit', async () => {
    setup();
    const apiTab = screen.getByTestId('API Limit-tab');
    await userEvent.click(apiTab);
    await waitFor(
      () => {
        expect(screen.getByPlaceholderText(/Enter min min value/i)).toBeInTheDocument();
      },
      { timeout: 500 }
    );
  });

  it('should reset modified values when modal is closed without saving', async () => {
    const { unmount } = setup();
    await waitFor(() => expect(screen.getByTestId('consent-checkbox')).toBeInTheDocument());
    await userEvent.click(screen.getByTestId('API Limit-tab'));
    const minInput = (await screen.findByPlaceholderText(/Enter min min value/i)) as HTMLInputElement;
    const baselineDigits = minInput.value.replace(/[^0-9]/g, '');
    await userEvent.clear(minInput);
    await userEvent.type(minInput, '777');
    expect(minInput.value.replace(/[^0-9]/g, '')).not.toBe(baselineDigits);
    await userEvent.click(screen.getByTestId('close-button'));
    unmount();
    render(
      <MockIndex>
        <GlobalLimitModal isOpen onClose={onClose} />
      </MockIndex>
    );
    await waitFor(() => expect(screen.getByTestId('API Limit-tab')).toBeInTheDocument());
    await userEvent.click(screen.getByTestId('API Limit-tab'));
    const reopenedMinInput = (await screen.findByPlaceholderText(/Enter min min value/i)) as HTMLInputElement;
    await waitFor(() => expect(reopenedMinInput.value.replace(/[^0-9]/g, '')).toBe(baselineDigits));
  });
});
