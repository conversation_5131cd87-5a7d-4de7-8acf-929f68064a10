import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { axe } from 'jest-axe';
import { Mock, vi } from 'vitest';

import MerchantsTable from '+dashboard/ProductConfig/MerchantsTable';
import { useSetUserAccess } from '+hooks';
import MockIndexWithRoute from '+mock/MockIndexWithRoute';

vi.mock('+hooks/useSetUserAccess', () => ({
  default: vi.fn()
}));

const MockedMerchantsTable = ({ currency }: { currency: string }) => {
  return (
    <MockIndexWithRoute route="/dashboard/product-config/:currency" initialEntries={[`/dashboard/product-config/${currency}`]}>
      <MerchantsTable />
    </MockIndexWithRoute>
  );
};
describe('MerchantsTable', () => {
  const mockUseSetUserAccess = useSetUserAccess as Mock;
  mockUseSetUserAccess.mockReturnValue({ 'transaction_config_details.update': true });
  it('MerchantsTable is accessible', async () => {
    const { container } = render(<MockedMerchantsTable currency="GHS" />);
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });
  it('should render MerchantsTable', async () => {
    render(<MockedMerchantsTable currency="GHS" />);
    await waitFor(() => expect(screen.getByText('Ghanaian Cedi Merchants [GHS]')).toBeInTheDocument());
    await waitFor(() => expect(screen.getByRole('button', { name: /Disable GHS Access for a Merchant/i })).toBeInTheDocument());
    await waitFor(() => expect(screen.getByText('535 merchants have access to GHS currency')).toBeInTheDocument());
    await waitFor(() => expect(screen.getAllByText(/risk level/i)).toHaveLength(6));
    await waitFor(() => expect(screen.getAllByText(/currency status/i)).toHaveLength(6));
    await waitFor(() => expect(screen.getAllByText(/configuration type/i)).toHaveLength(6));
    await waitFor(() => expect(screen.getAllByText(/date added/i)).toHaveLength(6));
  });
  it('should render MerchantsTable data', async () => {
    render(<MockedMerchantsTable currency="GHS" />);

    await waitFor(() => expect(screen.getByText(/wecook/i)).toBeInTheDocument());
    await waitFor(() => expect(screen.getByText(/testering/i)).toBeInTheDocument());
    await waitFor(() => expect(screen.getByText(/keonboarding/i)).toBeInTheDocument());
    await waitFor(() => expect(screen.getByText(/keonboarding/i)).toBeInTheDocument());
    await waitFor(() => expect(screen.getByText(/testnigeria/i)).toBeInTheDocument());
    await waitFor(() => expect(screen.getAllByText(/default/i)).toHaveLength(5));
    await waitFor(() => expect(screen.getAllByText(/enabled/i)).toHaveLength(7));
  });
  it('should display NGN as the currency', async () => {
    render(<MockedMerchantsTable currency="NGN" />);

    await waitFor(() => expect(screen.getByText('535 merchants have access to NGN currency')).toBeInTheDocument());
  });
  it('should not display button for disabling currency access when permission is absent', async () => {
    mockUseSetUserAccess.mockReturnValue({});

    render(<MockedMerchantsTable currency="NGN" />);
    await waitFor(() => expect(screen.getByText(/wecook/i)).toBeInTheDocument());
    await waitFor(() => expect(screen.queryByRole('button', { name: /disable NGN access for a merchant/i })).not.toBeInTheDocument());
  });
});
