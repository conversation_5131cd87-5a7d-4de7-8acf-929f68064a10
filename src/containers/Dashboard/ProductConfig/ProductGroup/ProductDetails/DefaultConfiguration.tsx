import React, { Fragment, useEffect, useState } from 'react';
import { useQuery } from 'react-query';
import { useParams } from 'react-router-dom';

import LoadingPlaceholder from '+dashboard/Shared/LoadingPlaceHolder';
import { useFeedbackHandler } from '+hooks';
import APIRequest from '+services/api-services';
import {
  availableProductType,
  CurrencyType,
  productCategoriesPropTypeContentWithoutEnabled,
  productCategoriesType,
  TransactionLimitType,
  updateProductConfigDataTypeWithoutEnabled,
  WithdrawalLimitType
} from '+types';
import { capitalize, currentProductConfigSettings, productMapping } from '+utils';

import EditDetailsCard from '../../components/EditDetailsCard';

import '../index.scss';

const api = new APIRequest();

const DefaultConfiguration = () => {
  const { feedbackInit } = useFeedbackHandler();
  const [transactionLimit, setTransactionLimit] = useState<TransactionLimitType | undefined>();
  const { currency, product, feature } = useParams<{
    currency: CurrencyType;
    product: productCategoriesType;
    feature: availableProductType;
  }>();
  const params = {
    payment_type: productMapping[product],
    payment_method: feature
  };

  const {
    data: configData,
    refetch: refetchConfig,
    isLoading
  } = useQuery([`${currency}_PRODUCT_CONFIG`, currency, product, feature], () => api.getProductConfiguration(currency, params), {
    refetchOnMount: 'always',
    onError: () => {
      feedbackInit({
        message: `There has been an error in getting this merchant's details`,
        type: 'danger',
        action: {
          action: () => refetchConfig(),
          name: 'Try again'
        }
      });
    }
  });
  const isBankAccount = product === 'payouts' && feature === 'bank_account';
  useEffect(() => {
    if (isBankAccount) {
      setTransactionLimit({
        max: configData?.data?.setting?.transaction_limit?.max,
        min: configData?.data?.setting?.transaction_limit?.min
      });
    }
  }, [product, feature, configData]);

  return (
    <div className="default-config --container">
      {isLoading ? (
        <LoadingPlaceholder type="text" background="#f5f6f6" content={2} section={3} />
      ) : (
        configData &&
        Object.entries(configData?.data?.setting).map(([key, value]) => {
          let newValue = value;
          if (
            !currentProductConfigSettings.includes(key) ||
            (['pay-ins'].includes(product) && ['transaction_limit'].includes(key) && ['disbursement_wallet'].includes(feature))
          )
            return null;
          if (isBankAccount && key === 'limits') {
            //Add transaction limit to withdrawal limit
            newValue = {
              ...(value as WithdrawalLimitType),
              limit: transactionLimit
            } satisfies WithdrawalLimitType;
          }
          return (
            <Fragment key={key}>
              <EditDetailsCard
                title={key as updateProductConfigDataTypeWithoutEnabled}
                content={newValue as productCategoriesPropTypeContentWithoutEnabled}
                paymentMethod={feature}
                currency={currency}
                category={product}
                type={capitalize(product)}
                disableEdit={configData?.data?.setting?.enabled}
              />
            </Fragment>
          );
        })
      )}
    </div>
  );
};

export default DefaultConfiguration;
