import useProductConfigCardIssuanceStore from '+store/productConfigCardIssuanceStore';
import { AccessRequestMerchantResponse, CurrencyType } from '+types';
import { formatAmount, formatWithCommas } from '+utils';

import { monthlyPaymentValuesMap, pciDSSLevelMap, riskLevelMap } from './approvedMerchantDetailsHelpers';

export const generateMerchantRequestDetailsFrom = (data?: AccessRequestMerchantResponse) => {
  if (!data) return [];
  return [
    {
      label: 'Merchant name',
      value: data.name
    },
    {
      label: 'Risk level',
      value: data.risk_level ? riskLevelMap[data.risk_level] : 'Not available'
    },
    {
      label: 'PCI DSS level',
      value: data.pci_dss_level ? pciDSSLevelMap[data.pci_dss_level] : 'Not available'
    },
    {
      label: 'Preferred plan',
      value: data.subscription_plan ? `${data.subscription_plan} Plan` : 'Not available'
    },
    {
      label: 'Monthly payment value',
      value: data.monthly_payment_value ? monthlyPaymentValuesMap[data.monthly_payment_value] : 'Not available'
    }
  ];
};

export const generateMerchantRiskLevelSpendingLimitDetailsFrom = (currency: CurrencyType, data?: AccessRequestMerchantResponse) => {
  if (!data?.risk_level) return [];
  const productConfigStore = useProductConfigCardIssuanceStore.getState();
  const merchantSpendingLimits = productConfigStore.defaultConfig.customer.risk_level?.[data.risk_level].spending_limit;

  return [
    {
      label: `Maximum limit per transaction (${currency})`,
      value: merchantSpendingLimits?.per_transaction_max ? formatAmount(merchantSpendingLimits?.per_transaction_max) : 'Not Available'
    },
    {
      label: `Daily transaction cap (${currency})`,
      value: merchantSpendingLimits?.daily_max ? formatAmount(merchantSpendingLimits?.daily_max) : 'Not Available'
    },
    {
      label: `Monthly transaction cap (${currency})`,
      value: merchantSpendingLimits?.monthly_max ? formatAmount(merchantSpendingLimits?.monthly_max) : 'Not Available'
    }
  ];
};

export const generateMerchantRiskLevelFundingLimitDetailsFrom = (currency: CurrencyType, data?: AccessRequestMerchantResponse) => {
  if (!data?.risk_level) return [];
  const productConfigStore = useProductConfigCardIssuanceStore.getState();
  const merchantFundingLimits = productConfigStore.defaultConfig.customer.risk_level?.[data.risk_level].funding_limit;
  return [
    {
      label: `Maximum daily funding (${currency})`,
      value: merchantFundingLimits?.daily_max ? formatAmount(merchantFundingLimits?.daily_max) : 'Not Available'
    },
    {
      label: `Maximum monthly funding (${currency})`,
      value: merchantFundingLimits?.monthly_max ? formatAmount(merchantFundingLimits?.monthly_max) : 'Not Available'
    },
    {
      label: `Maximum quarterly funding (${currency})`,
      value: merchantFundingLimits?.quarterly_max ? formatAmount(merchantFundingLimits?.quarterly_max) : 'Not Available'
    }
  ];
};

export const generateMerchantPCIDSSLimitDetailsFrom = (data?: AccessRequestMerchantResponse) => {
  if (!data?.pci_dss_level) return [];

  const productConfigStore = useProductConfigCardIssuanceStore.getState();
  const merchantPCIDSSLimits = productConfigStore.defaultConfig.customer.pcidss_level?.[data.pci_dss_level];

  return [
    {
      label: 'PCI DSS certification level',
      value: pciDSSLevelMap[data?.pci_dss_level] || 'Not Available'
    },
    {
      label: 'Transaction count',
      value: merchantPCIDSSLimits?.yearly_transaction_count
        ? formatWithCommas(merchantPCIDSSLimits?.yearly_transaction_count)
        : 'Not Available'
    },
    {
      label: 'Number of issuable cards',
      value: merchantPCIDSSLimits?.yearly_issued_cards ? formatWithCommas(merchantPCIDSSLimits?.yearly_issued_cards) : 'Not Available'
    }
  ];
};

export const declineReasonOptions = [
  { label: '--Select a reason--', value: '' },
  { label: 'PCI DSS Non-Compliance', value: 'PCI DSS Non-Compliance' },
  { label: 'Risk or Fraud Concerns ', value: 'Risk or Fraud Concerns ' },
  { label: 'Internal Business Decision', value: 'Internal Business Decision' },
  { label: 'Others', value: 'others' }
];
