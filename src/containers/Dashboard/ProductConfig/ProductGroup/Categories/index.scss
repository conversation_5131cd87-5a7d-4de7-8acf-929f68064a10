@import '+styles/kpy-custom/_custom';
@import '+styles/kpy-custom/variables';

.categories-container {
  display: flex;
  flex-direction: column;

  & > * + * {
    margin-top: 35px;
  }

  &__first {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #dde2ec;

    &.no-bottom-border {
      border-bottom: none;
    }

    .description {
      font-family: 'Averta PE';

      & > * + * {
        margin-top: 10px;
      }

      & > span {
        display: flex;
        align-items: center;
        column-gap: 1rem;

        .status {
          max-width: 107px;
          padding: 2px 8px;
          border-radius: 0.3rem;
        }
      }

      p {
        margin-bottom: 0;
        font-weight: 500;
        color: #a9afbc;
        max-width: 500px;
      }
    }

    .controls {
      display: flex;
      align-items: center;
      column-gap: 0.8rem;

      p {
        margin-bottom: 0;
        font-weight: 500;
        color: #a9afbc;
      }

      .modal-body {
        padding: 0 1rem 1rem 1rem;
      }

      .--enable-btn {
        background: #eaf2fe;
        color: #3e4b5b;
        border: none;
        font-weight: 600;

        &.--more-btn {
          background-color: #eaf2fe;
        }
      }

      .ellipsis__nav {
        box-shadow: 0px 2px 20px 0px #0f182117;
        padding: 0.5rem 0.8rem;
        top: 25rem;
        right: 3rem;

        .ellipsis__item {
          font-weight: 600;

          .active {
            color: #2376f3;
          }

          .disabled {
            color: #f32345;
          }
        }
      }

      &.method {
        .ellipsis__nav {
          top: 15rem;
          right: 3rem;
        }
      }
    }
  }
}
