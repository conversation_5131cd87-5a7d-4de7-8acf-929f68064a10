import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { describe, expect, it, vi } from 'vitest';

import MockIndex from '+mock/MockIndex';
import { CurrencyType } from '+types';

import Categories from '../index';

vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useParams: () => ({ product: 'payouts' })
  };
});

const setup = (currency = 'USD' as CurrencyType) =>
  render(
    <MockIndex>
      <Categories currency={currency} />
    </MockIndex>
  );

describe('Categories Component', () => {
  it('should render GlobalLimitModal for payouts product regardless of limits data', async () => {
    setup();

    await waitFor(() => {
      expect(screen.getByText(/Default Configuration/i)).toBeInTheDocument();
    });

    expect(screen.getByText(/Default Configuration/i)).toBeInTheDocument();
  });

  it('should not render GlobalLimitModal when product is not payouts', async () => {
    vi.doMock('react-router-dom', async () => {
      const actual = await vi.importActual('react-router-dom');
      return {
        ...actual,
        useParams: () => ({ product: 'pay-ins' })
      };
    });

    setup();

    await waitFor(() => {
      expect(screen.getByText(/Default Configuration/i)).toBeInTheDocument();
    });

    expect(screen.getByText(/Default Configuration/i)).toBeInTheDocument();
  });
});
