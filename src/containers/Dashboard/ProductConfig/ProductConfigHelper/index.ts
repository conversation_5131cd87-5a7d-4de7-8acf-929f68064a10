import { QueryClient } from 'react-query';

import { EditDetailsCardPropsType } from '+types';
import { capitalizeRemovedash } from '+utils';

interface InvalidateArgs {
  queryClient: QueryClient;
  currency: string;
  category: string;
  merchantId?: string | null;
  paymentMethod?: string | null;
}

const customiseHeaderLabels = ['vba_count'] as const;

const customiseEditableCardHeader = (key: string): string => {
  switch (key) {
    case 'vba_count':
      return 'VBA Count';
    default:
      return '';
  }
};
export const conditionToDisplayBanner = (modeType: string, contentType: EditDetailsCardPropsType['content']): boolean => {
  if (modeType === 'vba_count' && typeof contentType === 'object' && 'low_balance_warning' in contentType) {
    return contentType.low_balance_warning;
  }
  return false;
};
export const getHeaderLabel = (title: string): string => {
  if (customiseHeaderLabels.includes(title as (typeof customiseHeaderLabels)[number])) {
    return customiseEditableCardHeader(title);
  }
  return capitalizeRemovedash(title);
};

export const invalidateProductConfigQueries = ({ queryClient, currency, category, merchantId, paymentMethod }: InvalidateArgs) => {
  const lowerCategory = category?.toLowerCase?.() || category;

  const keys: any[] = [
    [`${currency}_PRODUCT_CONFIG`, merchantId, currency, category],
    [`${currency}_PRODUCT_CONFIG`, currency, lowerCategory],
    [`${currency}_ALL_PRODUCT_CONFIG`, currency, lowerCategory],
    [`${currency}_PRODUCT_CONFIG`, currency],
    [`${currency}_ALL_PRODUCT_CONFIG_SETTING`, currency],
    `${currency}_MERCHANTS`
  ];

  if (paymentMethod) {
    keys.splice(3, 0, [`${currency}_PRODUCT_CONFIG`, currency, lowerCategory, paymentMethod]);
  }

  keys.forEach(k => queryClient.invalidateQueries(k));
};
