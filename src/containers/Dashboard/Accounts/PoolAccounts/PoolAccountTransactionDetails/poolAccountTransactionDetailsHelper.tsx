import React, { Dispatch, SetStateAction } from 'react';

import Icon from '+containers/Dashboard/Shared/Icons';
import ToolTip from '+containers/Dashboard/Shared/Tooltip';
import Copyable from '+shared/Copyable';
import { IPayinDetails, PoolAccountTransactionDetailsType } from '+types';
import {
  capitalize,
  capitalizeFirst,
  capitalizeRemovedash,
  formatAmount,
  getDate,
  getTime,
  isAllowed,
  switchBank,
  switchCurrency,
  switchStatus,
  truncateString
} from '+utils';

import infoIcon from '+assets/img/dashboard/information-button.svg';

export const getBankImageByName = (name: string): string => {
  return switchBank[name as keyof typeof switchBank] || switchBank.default;
};

export const generateHeaderSummary = (data?: PoolAccountTransactionDetailsType | IPayinDetails['data']) => {
  const poolAccountData = data as PoolAccountTransactionDetailsType;
  const payinData = data as IPayinDetails['data'];

  const netAmount = poolAccountData?.net_amount || payinData?.amount_paid || '';
  const fee = +(data?.fee || 0) + +(payinData?.vat || 0);

  return [
    {
      label: 'Net Amount',
      value: netAmount ? `${formatAmount(netAmount)} ${data?.currency}` : 'Not Available'
    },
    {
      label: (
        <div className="summary-label-item">
          <span>Fee</span>
          <ToolTip type="net_amount" image={infoIcon} message={<>This is the fees + VAT for this transaction</>} />
        </div>
      ),
      value: `${formatAmount(fee ?? '0')} ${(poolAccountData || payinData)?.currency}`
    },
    {
      label: 'Date / Time',
      value: `${getDate(poolAccountData?.transaction_date)}, ${getTime(poolAccountData?.transaction_date)}`
    },
    {
      label: 'Transaction Reference',
      value: (
        <>
          <div>
            <Copyable
              text={poolAccountData?.transaction_reference ?? payinData?.reference ?? ''}
              textModifier={text => truncateString(text.toUpperCase(), 18)}
            />
          </div>
        </>
      )
    }
  ];
};

export const generateMoreTransactionDetails = (data?: PoolAccountTransactionDetailsType | IPayinDetails['data']) => {
  const poolAccountData = data as PoolAccountTransactionDetailsType;
  const payinData = data as IPayinDetails['data'];

  return [
    {
      label: 'Status',
      value: (
        <>
          <span className={`status-pill smaller ${switchStatus(poolAccountData?.status.toLowerCase() ?? '')}`} />
          <span>{capitalize(poolAccountData?.status || 'N/A')}</span>
        </>
      )
    },

    {
      label: 'Amount Paid',
      value: (
        <>
          {formatAmount(poolAccountData?.amount_paid || '')} {poolAccountData?.currency}
        </>
      )
    },

    {
      label: 'Currency',
      value: payinData?.currency ? switchCurrency[payinData?.currency as keyof typeof switchCurrency] : 'Not Available'
    },

    ...(payinData?.channel
      ? [
          {
            label: 'Transaction Type',
            value: payinData?.channel === 'web' ? 'Dashboard' : 'Pay-in'
          }
        ]
      : []),

    ...(payinData?.processor
      ? [
          {
            label: 'Processor',
            value: capitalizeRemovedash(payinData?.processor) || 'Not available'
          }
        ]
      : []),

    ...(payinData?.processor_reference
      ? [
          {
            label: 'Processor ID',
            value: payinData?.processor_reference || 'Not available'
          }
        ]
      : []),

    ...(payinData?.payment?.account?.name
      ? [
          {
            label: 'Account Name',
            value: payinData?.payment?.account?.name || 'Not Available'
          }
        ]
      : []),

    ...(payinData?.merchant_bears_cost
      ? [
          {
            label: 'Fee Bearer',
            value: payinData?.merchant_bears_cost ? 'Merchant' : 'Customer'
          }
        ]
      : []),

    ...(poolAccountData?.created_at
      ? [
          {
            label: 'Date Created',
            value: (
              <>
                {getDate(poolAccountData?.created_at)} {getTime(poolAccountData?.created_at)}
              </>
            )
          }
        ]
      : []),

    ...(!['success', 'settled'].includes(data?.status.toLowerCase()!)
      ? [
          {
            label: 'Date Completed',
            value: (
              <>
                {getDate(poolAccountData?.updated_at)} {getTime(poolAccountData?.updated_at)}
              </>
            )
          }
        ]
      : []),

    ...(data?.status.toLowerCase() === 'settled'
      ? [
          {
            label: 'Settlement Time',
            value: (
              <>
                {getDate(poolAccountData?.updated_at)} {getTime(poolAccountData?.updated_at)}
              </>
            )
          }
        ]
      : [])
  ];
};

export const generateSourceDetails = (data?: Record<string, string>) => {
  return Object.entries(data ?? {}).map(([title, content]) => {
    return { label: capitalizeRemovedash(title), value: capitalizeFirst(content.trim()) };
  });
};

export const settleTransactionModalContent = ({
  modal,
  setModal,
  data,
  updateTransactionFn,
  newStatus
}: {
  modal: string;
  setModal: Dispatch<SetStateAction<string>>;
  data?: PoolAccountTransactionDetailsType;
  updateTransactionFn: () => void;
  newStatus: string;
}) => {
  const isSettle = newStatus === 'settled';

  switch (modal) {
    case 'confirm-status-update':
      return {
        secondButtonActionIsTerminal: false,
        heading: `Are you sure you want to ${isSettle ? 'settle' : 'reject'} this transaction for this merchant?`,
        description: isSettle
          ? 'Kindly ensure the details of this transaction are correct to enable easy reconciliation.'
          : 'Rejecting means the merchant will not be settled for this transaction.',
        size: 'md' as const,
        secondButtonColor: isSettle ? '' : '#F32345',
        secondButtonText: `Yes, ${isSettle ? 'Settle' : 'Reject'}`,
        secondButtonAction: updateTransactionFn
      };
    case 'settle-update-success':
      return {
        showButtons: false,
        size: 'sm' as const,

        content: (
          <div className="add-transaction-success-modal">
            <Icon name="circledSuccessWithLightGreenBg" width={80} height={80} />
            <h4>Done!</h4>
            <p className="text">You have successfully {isSettle ? 'settled' : 'rejected'} transaction for this merchant</p>
            <button onClick={() => setModal('')} type="button" className="dismiss-btn">
              Dismiss
            </button>
          </div>
        )
      };
  }
};

export const actionButtonsFn = ({
  userAccess,
  data,
  setModal,
  showDropdown,
  setShowDropdown,
  setNewStatus
}: {
  userAccess: { [key: string]: boolean };
  data?: PoolAccountTransactionDetailsType;
  setModal: Dispatch<SetStateAction<string>>;
  setShowDropdown: Dispatch<SetStateAction<boolean>>;
  showDropdown: boolean;
  setNewStatus: Dispatch<SetStateAction<string>>;
}) => {
  const status = data?.status.toLowerCase();

  const clickItemFn = (value: string) => {
    setNewStatus(value);
    setModal('confirm-status-update');
  };

  return [
    ...(status === 'success' && isAllowed(userAccess, ['pool_account_transactions.update'])
      ? [
          {
            children: (
              <div className="pool-details-dropdown-wrapper">
                <div className="text">
                  <span>Settle Transaction</span>{' '}
                  <Icon stroke="currentColor" className="arrow-down" name="arrowDown" width={12} strokeWidth={1.5} />
                </div>
                {showDropdown && (
                  <ul className="element-box ellipsis__nav user-ellipsis">
                    <li onClick={() => clickItemFn('settled')} role="presentation" className="ellipsis__item">
                      <span>Settle</span>
                    </li>
                    <li onClick={() => clickItemFn('rejected')} role="presentation" className="reject ellipsis__item">
                      <span>Reject</span>
                    </li>
                  </ul>
                )}
              </div>
            ),
            variant: 'text-button' as const,
            onClick: () => setShowDropdown(!showDropdown)
          }
        ]
      : [])
  ];
};
