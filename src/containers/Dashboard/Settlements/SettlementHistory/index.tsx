/* eslint-disable no-nested-ternary */
import React, { useEffect, useReducer, useState } from 'react';
import { useQuery, useQueryClient } from 'react-query';

import { useFeedbackHandler, useLargeExportDownloader, useSearchQuery, useSetUserAccess } from '+hooks';
import APIRequest from '+services/api-services';
import CurrencyPicker from '+shared/CurrencyPicker';
import LargeExportModal from '+shared/LargeExportModal';
import Table from '+shared/Table';
import { default as useBulkActionStore, default as useStore } from '+store';
import {
  BulkActionType,
  CurrencyType,
  FileFormatType,
  moduleType,
  SettlementControlsStateType,
  SettlementTabDetailType,
  SettlementType
} from '+types';
import { APIDownload, capitalize, filteredOutObjectProperty, getDate, getDateRange, isAllowed, logError, queriesParams } from '+utils';

import { summaryInfo, switchTabData } from './data';

import './index.scss';

const api = new APIRequest();
const range = getDateRange(4);
type TabType = 'pending' | 'ready' | 'approved' | 'settled';
function SettlementHistory() {
  const { feedbackInit } = useFeedbackHandler();
  const userAccess = useSetUserAccess();
  const bulkInfo = useBulkActionStore(state => state.bulkInfo);
  const completedAction = useBulkActionStore(state => state.completedAction);
  const setCompletedAction = useBulkActionStore(state => state.setCompletedAction);
  const { profile } = useStore(state => state);
  const [showLargeExportModal, setLargeExportModal] = useState(false);
  const searchQuery = useSearchQuery();
  const queryClient = useQueryClient();
  const activeTab = (searchQuery.value.tab ?? 'pending') as TabType;
  const limit = searchQuery.value.limit ?? '10';
  const activeCurrency = (searchQuery.value.currency ?? 'NGN') as CurrencyType;
  const dateFrom = searchQuery.value.dateFrom ?? range.pastDate;
  const dateTo = searchQuery.value.dateTo ?? range.today;
  const paginationType = searchQuery.value.pageType || 'cursor';
  const settlementReferences = searchQuery.value.reference || [];
  const batchQueryRefs = Array.isArray(settlementReferences) ? settlementReferences : [settlementReferences];

  const valuesToBeRemoved = [
    queriesParams.tab,
    queriesParams.page,
    queriesParams.limit,
    queriesParams.status,
    queriesParams.dateFrom,
    queriesParams.dateTo,
    queriesParams.totalItems,
    queriesParams.previousLimit,
    queriesParams.reference
  ];
  const sortingParams = {
    status: [activeTab],
    paginationType: paginationType,
    settlementReferences: typeof settlementReferences === 'string' ? [settlementReferences] : settlementReferences,
    ...filteredOutObjectProperty(searchQuery.value, valuesToBeRemoved),
    ...(batchQueryRefs.length === 0 || (batchQueryRefs.length === 1 && !batchQueryRefs[0]) ? { dateFrom, dateTo } : {})
  };

  const [controls, setControls] = useReducer(
    (prev: SettlementControlsStateType, next: Partial<SettlementControlsStateType>) => ({ ...prev, ...next }),
    {
      tableData: '',
      showLargeExportModal: false
    }
  );

  const mapTypesToActions = {
    pending: 'settlement_approval',
    ready: 'settlement_approval',
    approved: 'settlement_processing'
  };

  const tabs = ['pending', 'ready', 'approved', 'settled'] as const;
  const buffer: unknown[] = [];
  const anyLoading = {} as { [key in TabType]: boolean };
  const getTabDetails = (tab: TabType): SettlementTabDetailType | undefined =>
    buffer.find((data): data is SettlementTabDetailType => (data as SettlementTabDetailType).type === tab);
  const { getDownload } = useLargeExportDownloader('Settlements');

  const { data: summaryData } = useQuery(
    ['SETTLEMENT_SUMMARY', activeCurrency],
    () => api.getSettlementSummary(activeCurrency as CurrencyType),
    {
      onError: () => {
        feedbackInit({
          message: 'There has been an error getting merchant settlement summary',
          type: 'danger'
        });
      },
      retry: false
    }
  );

  const { data: settlementCount, isFetching: isLoadingCount } = useQuery(
    [`${activeTab.toUpperCase()}_SETTLEMENTS_COUNT`, limit, sortingParams, activeCurrency],
    () => api.getSettlementSummaryCount({ limit, status: [activeTab], sortingParams, currency: activeCurrency }),
    {
      onError: () => {
        feedbackInit({
          message: 'There has been an error getting your settlements count.',
          type: 'danger'
        });
      }
    }
  );
  useEffect(() => {
    getDownload();
  }, []);

  useEffect(() => {
    setControls({
      tableData: switchTabData(activeTab, activeCurrency)
    });
  }, [activeTab, activeCurrency]);

  const { data, isFetching } = useQuery(
    [`${activeTab.toUpperCase()}_SETTLEMENTS`, limit, sortingParams, activeCurrency],
    () => api.getSettlements(limit, [activeTab], sortingParams, activeCurrency),
    {
      refetchOnMount: 'always',
      onError: () => {
        feedbackInit({
          message: `There has been an error fetching the ${activeTab} settlements.`,
          type: 'danger'
        });
      }
    }
  );

  anyLoading[activeTab] = isFetching;
  buffer.push({ type: activeTab, ...data?.data });

  const exportSettlements = async (
    format: FileFormatType,
    close: () => void,
    fieldsToExport: string | string[],
    settlementReferences: string[] | undefined
  ) => {
    try {
      const res = await api.exportSettlementsByTab(sortingParams, format, activeCurrency, activeTab, fieldsToExport, settlementReferences);
      if (res.status === 202) {
        setLargeExportModal(true);
      } else {
        const type = format === 'csv' ? 'csv' : 'xlsx';
        APIDownload(res, `Settlements ${(getTabDetails(activeTab) as { type: string })?.type} at ${getDate(Date.now())}`, type);
        feedbackInit({
          title: 'Export Successful',
          message: (
            <>
              {' '}
              - Successfully exported{' '}
              <strong>{settlementReferences?.length ?? getTabDetails(activeTab)?.paging?.total_items} transactions.</strong>
            </>
          ),
          type: 'success'
        });
      }
      close();
    } catch (error) {
      logError(error);
      feedbackInit({
        title: 'Export Failed',
        message: `There has been an error exporting your settlements`,
        type: 'danger',
        componentLevel: true
      });
      throw error;
    }
  };

  useEffect(() => {
    if (completedAction && Object.values(mapTypesToActions).includes(completedAction)) {
      queryClient.invalidateQueries({
        queryKey: [
          `${(Object?.keys(mapTypesToActions) ?? [])
            .find(key => mapTypesToActions[key as keyof typeof mapTypesToActions] === completedAction)
            ?.toUpperCase()}_SETTLEMENTS`
        ]
      });
      setCompletedAction('');
    }
  }, [completedAction]);

  const summary = summaryData?.data || {};
  const tableDataKeys =
    controls.tableData && typeof controls.tableData !== 'string' && typeof controls.tableData.fields === 'function'
      ? Object.keys(controls.tableData.fields({} as any).data || {})
      : [];

  const getTableData = (): Record<string, unknown>[] => {
    const tabDetail = getTabDetails(activeTab);
    if (tabDetail && 'data' in tabDetail) {
      const tabData = tabDetail.data;
      if (Array.isArray(tabData) && tabData.every(item => typeof item === 'object' && item !== null && !Array.isArray(item))) {
        return tabData as Record<string, unknown>[];
      }
    }
    return [];
  };
  const getBulkState = (): BulkActionType<Record<string, unknown>> | undefined => {
    const found = bulkInfo?.data?.find(
      (action: any) =>
        action.type === mapTypesToActions[activeTab as keyof typeof mapTypesToActions] &&
        action.status === 'in_progress' &&
        action.data &&
        typeof action.data === 'object' &&
        action.data !== null &&
        !Array.isArray(action.data)
    );
    if (
      found &&
      Array.isArray(found.references) &&
      found.references.every(
        (ref: any) =>
          typeof ref === 'string' ||
          typeof ref === 'number' ||
          (typeof ref === 'object' && ref !== null) ||
          ref === null ||
          typeof ref === 'undefined'
      )
    ) {
      return found as BulkActionType<Record<string, unknown>>;
    }
    return undefined;
  };

  return (
    <>
      <LargeExportModal close={() => setLargeExportModal(false)} email={profile.email as string} visible={showLargeExportModal} />
      <section className="os-tabs-w">
        <div className="os-tabs-controls os-tabs-complex settlement-tab">
          <ul className="nav nav-tabs settlement-nav">
            {tabs.map(tab => (
              <li className="nav-item" key={tab}>
                <button
                  onClick={() => {
                    searchQuery.setQuery(
                      { tab, currency: activeCurrency, page: '1', startingAfter: '', endingBefore: '', totalItems: '' },
                      true
                    );
                  }}
                  type="button"
                  tabIndex={0}
                  className={`nav-link ${activeTab === tab && 'active'}`}
                >
                  {capitalize(tab)}
                </button>
              </li>
            ))}
          </ul>
          <CurrencyPicker
            onChange={value => {
              searchQuery.setQuery(
                { currency: value, tab: activeTab, page: '1', startingAfter: '', endingBefore: '', totalItems: '' },
                true
              );
            }}
            className="settlement-history__currency-switch selectpicker"
            activeCurrency={activeCurrency}
            label={<strong>Currency:</strong>}
            bordered
            id="settlement-history__currency-switch"
          />
        </div>
      </section>

      <section className="history_summary_details settlement-summary">
        <section className="history_summary_types" style={{ justifyContent: 'unset', gap: '2rem' }}>
          <div style={{ minWidth: '30%' }}>
            <p>{summaryInfo(activeTab, activeCurrency, summary)?.label}</p>
            <p>{summaryInfo(activeTab, activeCurrency, summary)?.value}</p>
            <p>{summaryInfo(activeTab, activeCurrency, summary)?.description}</p>
          </div>
        </section>
      </section>

      <section className="element-box-tp mt-5">
        <Table
          hasBulkAction
          bulkAction={mapTypesToActions[activeTab as keyof typeof mapTypesToActions]}
          bulkState={getBulkState()}
          header={null}
          className={
            controls.tableData && typeof controls.tableData !== 'string' && controls.tableData.className ? controls.tableData.className : ''
          }
          batchSearch={{
            showBatchViewer: true,
            showBatchQuery: true,
            fieldsToDisplayBatchQuery: ['reference'],
            idCountLimit: 20,
            viewerText: 'View Settlement IDs'
          }}
          tableHeadings={tableDataKeys}
          loading={anyLoading[activeTab]}
          data={getTableData()}
          renderFields
          hasPagination
          annotation={
            controls.tableData && typeof controls.tableData !== 'string' && controls.tableData.annotations
              ? controls.tableData.annotations
              : undefined
          }
          rowKey={
            controls.tableData && typeof controls.tableData !== 'string' && controls.tableData.rowKey
              ? controls.tableData.rowKey
              : undefined
          }
          rowURL={
            controls.tableData && typeof controls.tableData !== 'string' && controls.tableData.rowURL
              ? controls.tableData.rowURL
              : undefined
          }
          checkBoxKey={
            controls.tableData && typeof controls.tableData !== 'string' && controls.tableData.rowKey
              ? controls.tableData.rowKey
              : undefined
          }
          emptyStateHeading={
            controls.tableData && typeof controls.tableData !== 'string' && controls.tableData.emptyStateHeading
              ? controls.tableData.emptyStateHeading
              : ''
          }
          tableWrapperClassName="table-responsive"
          emptyStateMessage={
            controls.tableData && typeof controls.tableData !== 'string' && controls.tableData.emptyStateMessage
              ? controls.tableData.emptyStateMessage
              : ''
          }
          type={
            controls.tableData && typeof controls.tableData !== 'string' && controls.tableData.type
              ? (controls.tableData.type as moduleType)
              : undefined
          }
          subType={`${activeTab}_settlements`}
          showDateFilter={false}
          storedState={{
            activeCurrency
          }}
          filterType="settlements"
          filterExportAction={exportSettlements}
          filterActiveCurrency={activeCurrency}
          filterDefaultStatus={activeTab}
          filterKeywordPlaceholder="Search by Settlement IDs or Merchant Name ..."
          filterShowExport={isAllowed(userAccess, ['settlements.export']) as boolean}
          cursors={
            getTabDetails(activeTab)?.paging as {
              next_cursor: number;
              previous_cursor?: number;
              total_items: number | null;
              page_size: number;
            }
          }
          isLoadingCount={isLoadingCount}
          totalCount={settlementCount?.data?.count ?? 0}
        >
          {iter =>
            controls.tableData &&
            typeof controls.tableData !== 'string' &&
            'fields' in controls.tableData &&
            typeof controls.tableData.fields === 'function'
              ? controls.tableData.fields(iter as SettlementType)
              : { data: {} }
          }
        </Table>
      </section>
    </>
  );
}

export default SettlementHistory;
