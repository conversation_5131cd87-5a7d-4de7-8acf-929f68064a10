import React, { useState } from 'react';
import ReactDOM from 'react-dom';

import useTooltip from '+hooks/useTooltip';

import info from '+assets/img/dashboard/tooltip.svg';

import './index.scss';

type TooltipPortalProps = {
  children: React.ReactNode;
  parent?: HTMLElement | null;
};

function TooltipPortal({ children, parent }: TooltipPortalProps) {
  const [container] = useState(() => {
    const el = document.createElement('div');
    el.className = 'tooltip-portal-container';
    return el;
  });

  React.useEffect(() => {
    const mountPoint = parent || document.body;
    mountPoint.appendChild(container);
    return () => {
      mountPoint.removeChild(container);
    };
  }, [container, parent]);

  return ReactDOM.createPortal(children, container);
}

type ToolTipProps = {
  message: React.ReactNode;
  children?: React.ReactNode;
  position?: 'top' | 'bottom' | 'left' | 'right';
  classname?: string;
  image?: string;
  type?: string;
  wrapperComponent?: React.ElementType;
  hasFullWidth?: boolean;
  centered?: boolean;
  disabled?: boolean;
  noPadding?: boolean;
  className?: string;
  contentClassName?: string;
};

function ToolTip({
  message,
  children,
  position = 'right',
  classname = '',
  image = info,
  type,
  wrapperComponent: Wrapper = 'div',
  hasFullWidth,
  centered,
  disabled,
  noPadding,
  className,
  contentClassName
}: ToolTipProps) {
  const wrapperClass = `text-tooltip-w ${hasFullWidth ? 'w-100 pr-2 no-transform' : ''} ${className || classname}`.trim();

  const { visible, tooltipRef, triggerRef, eventHandlers, position: actualPosition, portalParent } = useTooltip({ position });
  const handlers = disabled ? {} : eventHandlers;

  return (
    <>
      <Wrapper className={wrapperClass} ref={triggerRef as any} tabIndex={0} {...(handlers as any)}>
        {children ? children : <img src={image} alt="info icon" className="text-tooltip--image" />}
      </Wrapper>

      <TooltipPortal parent={portalParent}>
        <div
          ref={tooltipRef}
          className={`text-tooltip--content ${type || ''} ${visible ? 'visible' : ''} ${centered ? 'centered' : ''} ${contentClassName || ''}`}
          role="tooltip"
          aria-hidden={!visible}
          data-position={actualPosition}
          id={type ? `${type}-info` : undefined}
          aria-label="text-tooltip-message"
          style={noPadding ? { padding: 0 } : undefined}
        >
          {message}
        </div>
      </TooltipPortal>
    </>
  );
}

export default ToolTip;
