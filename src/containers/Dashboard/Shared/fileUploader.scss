.file-input-wrapper {
  .file-input {
    background-color: #F1F6FA;
    padding: 15px;
    border: 2px dashed #DDE2EC;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    * {
      color: #9a9a9a;
      font-weight: 400;
    }

    .icon {
      width: 20px;
      margin-right: 10px;
    }
    .file-name {
      color: #2376F3;
      text-decoration: underline;
      flex: 1;
    }
    .cancel-icon {
      width: 16px;
      cursor: pointer;
    }
  }
  &.uploaded .file-input {
    transition: background 200ms ease-in;
    justify-content: flex-start;
    background-color: #E4FFF1;
    cursor: default;
  }
  .info-text {
    color: #414F5F;
    opacity: 0.7;
  }
  .bottom-info-row {
    display: flex;
    justify-content: space-between;
    transition: all 300ms ease-in-out;
    max-height: 200px;
    overflow: hidden;

    &.hidden {
      max-height: 0;
    }
  }
}
