import React from 'react';

import { TIcon, TIconNames, TRenderSvg } from './IconNames';

const icons: Record<TIconNames, TRenderSvg> = {
  circledSuccessWithLightGreenBg: ({ fill = '#24B314', ...props }) => (
    <svg width="60" height="61" viewBox="0 0 60 61" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
      <rect y="0.503906" width="60" height="60" rx="30" fill={fill} fillOpacity="0.2" />
      <path d="M26.8843 40.0039L18.5 30.8564L21.6845 27.5995L26.8331 33.2167L38.2666 21.0039L41.5 24.1548L26.8843 40.0039Z" fill={fill} />
    </svg>
  ),
  doubleCaretLeft: ({ strokeWidth = 1.66667, stroke = '#A9AFBC', ...props }) => (
    <svg width="13" height="12" viewBox="0 0 13 12" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
      <path d="M12 1L6.92277 6L12 11" stroke={stroke} strokeWidth={strokeWidth} strokeLinecap="round" strokeLinejoin="round" />
      <path d="M6.07812 1L1.0009 6L6.07812 11" stroke={stroke} strokeWidth={strokeWidth} strokeLinecap="round" strokeLinejoin="round" />
    </svg>
  ),
  doubleCaretRight: ({ strokeWidth = 1.66667, stroke = '#A9AFBC', ...props }) => (
    <svg width="13" height="12" viewBox="0 0 13 12" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
      <path d="M1 11L6.07723 6L1 1" stroke={stroke} strokeWidth={strokeWidth} strokeLinecap="round" strokeLinejoin="round" />
      <path d="M6.92188 11L11.9991 6L6.92188 1" stroke={stroke} strokeWidth={strokeWidth} strokeLinecap="round" strokeLinejoin="round" />
    </svg>
  ),
  caretLeft: ({ strokeWidth = 2, stroke = '#A9AFBC', ...props }) => (
    <svg width="8" height="12" viewBox="0 0 8 14" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
      <path d="M7 1L0.999999 7L7 13" stroke={stroke} strokeWidth={strokeWidth} strokeLinecap="round" strokeLinejoin="round" />
    </svg>
  ),
  cancel: ({ height = 18, width = 18, fill = 'none', stroke = '#F32345', ...props }) => (
    <svg width={width} height={height} viewBox="0 0 24 24" fill={fill} xmlns="http://www.w3.org/2000/svg" {...props}>
      <path d="M18 6L6 18" stroke={stroke} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M6 6L18 18" stroke={stroke} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    </svg>
  ),
  circledQuestion: ({ name, fill = '#AABDCE', ...props }) => (
    <svg data-testid={name} width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M9.50927 0.862692C4.18674 0.93393 -0.069948 5.3069 0.000871004 10.6296C0.0721091 15.9501 4.44466 20.208 9.76698 20.1372C15.0885 20.0655 19.346 15.6926 19.2752 10.3703C19.2039 5.0494 14.8312 0.791664 9.50927 0.862692ZM9.48078 16.3666L9.42756 16.3658C8.6079 16.3415 8.03003 15.7376 8.05329 14.9301C8.07613 14.1364 8.66803 13.5603 9.46066 13.5603L9.50822 13.5611C10.3507 13.586 10.9221 14.1838 10.8984 15.0146C10.8749 15.8105 10.2923 16.3666 9.48078 16.3666ZM11.7778 10.5538C12.3119 10.1377 12.7357 9.79763 12.9285 9.52378C13.2989 9.01631 13.4867 8.45793 13.4867 7.86351C13.4867 6.80081 13.1041 5.98618 12.3498 5.44225C11.6053 4.90545 10.6598 4.63328 9.53965 4.63328C8.68396 4.63328 7.95062 4.82563 7.36123 5.20507C6.42319 5.80096 5.91699 6.82218 5.85727 8.24044L5.85308 8.34039H8.16853V8.24464C8.16853 7.85953 8.28272 7.4826 8.50858 7.12368C8.72817 6.77462 9.09525 6.60448 9.63142 6.60448C10.172 6.60448 10.5458 6.74486 10.7425 7.02206C10.9472 7.30932 11.0507 7.63199 11.0507 7.98084C11.0507 8.23227 10.9464 8.4881 10.7107 8.81307C10.6099 8.96016 10.4745 9.09782 10.3141 9.21871C10.2665 9.2493 9.14617 9.97069 8.63828 10.567C8.31457 10.9467 8.29529 11.4061 8.26784 12.1872L8.26135 12.3773H10.507L10.5079 12.2828C10.5124 11.9476 10.5358 11.8751 10.5825 11.7303C10.5876 11.7143 10.5931 11.6975 10.5988 11.6794C10.6719 11.447 10.8668 11.2109 11.1897 10.9599L11.7778 10.5538Z"
        fill={fill}
      />
    </svg>
  ),
  arrowRight: ({ name, strokeWidth = 2, width = 20, height = 20, stroke = '#94A7B7', ...props }) => (
    <svg data-testid={name} width={width} height={height} viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
      <path d="M4.16406 10H15.8307" stroke={stroke} strokeWidth={strokeWidth} strokeLinecap="round" strokeLinejoin="round" />
      <path
        d="M10 4.16602L15.8333 9.99935L10 15.8327"
        stroke={stroke}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  ),
  caretDownSolid: ({ name, ...props }) => (
    <svg data-testid={name} width="16" height="10" viewBox="0 0 16 10" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
      <path
        d="M14.9866 1.5545L9.05706 8.984C8.49456 9.6635 7.62756 9.6635 7.08831 8.984L1.15881 1.5545C0.596308 0.851 0.877558 0.3125 1.74456 0.3125L14.4008 0.312502C15.2918 0.312503 15.5491 0.851753 14.9866 1.5545Z"
        fill="#414F5F"
      />
    </svg>
  ),
  caretUpSolid: ({ name, ...props }) => (
    <svg data-testid={name} width="16" height="10" viewBox="0 0 16 10" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
      <path
        d="M1.01344 8.4455L6.94294 1.016C7.50544 0.336501 8.37244 0.336501 8.91169 1.016L14.8412 8.4455C15.4037 9.149 15.1224 9.6875 14.2554 9.6875L1.59919 9.6875C0.708193 9.6875 0.450943 9.14825 1.01344 8.4455Z"
        fill="#414F5F"
      />
    </svg>
  ),
  twoPerson: ({ width = 24, stroke = '#414042', height = 24, strokeWidth = 2, fill = 'none', name }) => (
    <svg data-testid={name} width={width} height={height} viewBox="0 0 24 24" fill={fill} xmlns="">
      <path d="" stroke={stroke} strokeWidth={strokeWidth} strokeLinecap="round" strokeLinejoin="round" />
    </svg>
  ),
  check: ({ stroke = 'currentColor', fill = 'none', height = 13, width = 18, strokeWidth = 2, name, ...props }) => (
    <svg data-testid={name} width={width} height={height} viewBox="0 0 18 13" fill={fill} xmlns="http://www.w3.org/2000/svg" {...props}>
      <path d="M17 1L6 12L1 7" stroke={stroke} strokeWidth={strokeWidth} strokeLinecap="round" strokeLinejoin="round" />
    </svg>
  ),
  caretRight: ({ stroke = 'currentColor', fill = 'none', height = 12, width = 8, strokeWidth = 2, name, className }) => (
    <svg
      data-testid={name}
      width={width}
      className={className}
      height={height}
      viewBox="0 0 8 14"
      fill={fill}
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M1 13L7 7L0.999999 1" stroke={stroke} strokeWidth={strokeWidth} strokeLinecap="round" strokeLinejoin="round" />
    </svg>
  ),
  checkRounded: ({ fill = '#24B314', height = 20, width = 20, name }) => (
    <svg data-testid={name} width={width} height={height} viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M10 0C8.02219 0 6.08879 0.58649 4.4443 1.6853C2.79981 2.78412 1.51809 4.3459 0.761209 6.17316C0.00433284 8.00043 -0.1937 10.0111 0.192152 11.9509C0.578004 13.8907 1.53041 15.6725 2.92894 17.0711C4.32746 18.4696 6.10929 19.422 8.0491 19.8079C9.98891 20.1937 11.9996 19.9957 13.8268 19.2388C15.6541 18.4819 17.2159 17.2002 18.3147 15.5557C19.4135 13.9112 20 11.9778 20 10C20 8.68678 19.7413 7.38642 19.2388 6.17316C18.7362 4.95991 17.9997 3.85752 17.0711 2.92893C16.1425 2.00035 15.0401 1.26375 13.8268 0.761205C12.6136 0.258658 11.3132 0 10 0ZM15.676 8.237L9.676 13.737C9.48604 13.9112 9.23615 14.0053 8.97848 13.9997C8.7208 13.9941 8.47524 13.8893 8.293 13.707L5.293 10.707C5.19749 10.6148 5.12131 10.5044 5.0689 10.3824C5.01649 10.2604 4.98891 10.1292 4.98775 9.9964C4.9866 9.86362 5.0119 9.73194 5.06218 9.60905C5.11246 9.48615 5.18672 9.3745 5.28061 9.2806C5.3745 9.18671 5.48615 9.11246 5.60905 9.06218C5.73195 9.0119 5.86363 8.98659 5.99641 8.98775C6.12918 8.9889 6.2604 9.01649 6.38241 9.0689C6.50441 9.12131 6.61476 9.19749 6.707 9.293L9.03 11.616L14.324 6.763C14.5195 6.58371 14.7781 6.48942 15.0431 6.50086C15.3081 6.5123 15.5577 6.62853 15.737 6.824C15.9163 7.01946 16.0106 7.27814 15.9991 7.54313C15.9877 7.80812 15.8715 8.05771 15.676 8.237Z"
        fill={fill}
      />
    </svg>
  ),
  caretDown: ({ stroke = 'currentColor', strokeWidth = 2, name, ...props }) => (
    <svg data-testid={name} width={20} height={20} viewBox="0 0 20 20" fill="none" {...props}>
      <path d="M5 7.5L10 12.5L15 7.5" stroke={stroke} strokeWidth={strokeWidth} strokeLinecap="round" strokeLinejoin="round" />
    </svg>
  ),
  lock: ({ fill = '#414F5F', height = 18, width = 14, name }) => (
    <svg data-testid={name} width={width} height={height} viewBox="0 0 14 18" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M11.1654 6.49935V4.83268C11.1654 2.49935 9.33203 0.666016 6.9987 0.666016C4.66536 0.666016 2.83203 2.49935 2.83203 4.83268V6.49935C1.41536 6.49935 0.332031 7.58268 0.332031 8.99935V14.8327C0.332031 16.2493 1.41536 17.3327 2.83203 17.3327H11.1654C12.582 17.3327 13.6654 16.2493 13.6654 14.8327V8.99935C13.6654 7.58268 12.582 6.49935 11.1654 6.49935ZM4.4987 4.83268C4.4987 3.41602 5.58203 2.33268 6.9987 2.33268C8.41536 2.33268 9.4987 3.41602 9.4987 4.83268V6.49935H4.4987V4.83268ZM7.91536 11.916L7.83203 11.9993V13.166C7.83203 13.666 7.4987 13.9993 6.9987 13.9993C6.4987 13.9993 6.16536 13.666 6.16536 13.166V11.9993C5.66536 11.4993 5.58203 10.7493 6.08203 10.2493C6.58203 9.74935 7.33203 9.66601 7.83203 10.166C8.33203 10.5827 8.41537 11.416 7.91536 11.916Z"
        fill={fill}
      />
    </svg>
  ),
  eye: ({ style = {}, width = 18, stroke = '#2376F3', height = 16, strokeWidth = 2, fill = 'none', name }) => (
    <svg style={style} data-testid={name} width={width} height={height} viewBox="0 0 20 20" fill={fill} xmlns="http://www.w3.org/2000/svg">
      <g clipPath="url(#clip0_12242_86917)">
        <path
          d="M0.832031 9.99992C0.832031 9.99992 4.16536 3.33325 9.9987 3.33325C15.832 3.33325 19.1654 9.99992 19.1654 9.99992C19.1654 9.99992 15.832 16.6666 9.9987 16.6666C4.16536 16.6666 0.832031 9.99992 0.832031 9.99992Z"
          stroke={stroke}
          strokeWidth={strokeWidth}
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M10 12.5C11.3807 12.5 12.5 11.3807 12.5 10C12.5 8.61929 11.3807 7.5 10 7.5C8.61929 7.5 7.5 8.61929 7.5 10C7.5 11.3807 8.61929 12.5 10 12.5Z"
          stroke={stroke}
          strokeWidth={strokeWidth}
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
    </svg>
  ),
  picture: ({ style = {}, width = 16, height = 17, fill = '#94A7B7' }) => (
    <svg style={style} width={width} height={height} viewBox="0 0 24 26" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M6.205 3.25H17.795C18.909 3.25 19.314 3.37567 19.721 3.61183C20.128 3.848 20.448 4.19467 20.666 4.63558C20.884 5.0765 21 5.51417 21 6.72208V14.8579L16.609 10.4672C16.4497 10.3079 16.2603 10.1879 16.0541 10.1156C15.8478 10.0432 15.6296 10.0201 15.4143 10.0479C15.199 10.0757 14.9918 10.1537 14.8071 10.2766C14.6223 10.3995 14.4643 10.5643 14.344 10.7597L11.214 15.8459L8.911 14.2426C8.60396 14.0289 8.23555 13.9438 7.87475 14.0032C7.51396 14.0626 7.18553 14.2625 6.951 14.5654L3.005 19.6625C3.00172 19.5343 3.00005 19.4061 3 19.2779V6.72208C3 5.51417 3.116 5.0765 3.334 4.63558C3.552 4.19467 3.872 3.848 4.279 3.61183C4.686 3.37567 5.09 3.25 6.205 3.25ZM15.682 12.4908L21 17.8068V19.2768C21 20.4837 20.884 20.9224 20.666 21.3633C20.4529 21.7995 20.1236 22.1562 19.721 22.3871C19.314 22.6233 18.91 22.7489 17.795 22.7489H6.205C5.09 22.7489 4.686 22.6233 4.279 22.3871C4.1031 22.2857 3.94008 22.1601 3.794 22.0133L8.2 16.3226L10.546 17.9563C10.877 18.1867 11.2783 18.267 11.6635 18.1798C12.0486 18.0927 12.3868 17.8451 12.605 17.4904L15.682 12.4908ZM7.988 6.5C6.878 6.5 6 7.40133 6 8.65367C6 9.90708 6.879 10.8333 7.988 10.8333C9.121 10.8333 10 9.90708 10 8.65367C10 7.40133 9.121 6.5 7.988 6.5Z"
        fill={fill}
      />
    </svg>
  ),
  invalid: ({ stroke = '#0c0b3e', fill = 'none', height = 18, width = 18, strokeWidth = 2, name, ...props }) => (
    <svg data-testid={name} width={width} height={height} viewBox="0 0 18 18" fill={fill} {...props}>
      <path d="M13.5 4.5L4.5 13.5" stroke={stroke} strokeWidth={strokeWidth} strokeLinecap="round" strokeLinejoin="round" />
      <path d="M4.5 4.5L13.5 13.5" stroke={stroke} strokeWidth={strokeWidth} strokeLinecap="round" strokeLinejoin="round" />
    </svg>
  ),
  dash: ({ stroke = '#A9AFBC', fill = 'none', height = 18, width = 18, strokeWidth = 2, name }) => (
    <svg data-testid={name} width={width} height={height} viewBox="0 0 18 18" fill={fill}>
      <path d="M3.75 9H14.25" stroke={stroke} strokeWidth={strokeWidth} strokeLinecap="round" strokeLinejoin="round" />
    </svg>
  ),
  unknown: ({ stroke = '#AABDCE', fill = 'none', height = 20, width = 20, strokeWidth = 2, name }) => (
    <svg data-testid={name} width={width} height={height} viewBox="0 0 20 20" fill={fill} xmlns="http://www.w3.org/2000/svg">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M9.8663 0.000908804C4.34393 0.0748216 -0.0725743 4.61198 0.000903706 10.1346C0.0748165 15.6548 4.61154 20.0726 10.1337 19.9991C15.655 19.9247 20.0723 15.3876 19.9989 9.86544C19.925 4.34481 15.388 -0.0727866 9.8663 0.000908804ZM9.83686 16.087L9.78165 16.0861C8.93121 16.0609 8.33165 15.4344 8.35578 14.5966C8.37948 13.7731 8.99361 13.1753 9.81599 13.1753L9.86534 13.1761C10.7395 13.202 11.3323 13.8222 11.3077 14.6842C11.2834 15.51 10.6788 16.087 9.83686 16.087ZM12.2199 10.0559C12.7741 9.62415 13.2138 9.27133 13.4138 8.9872C13.7982 8.46068 13.993 7.88133 13.993 7.2646C13.993 6.16199 13.596 5.31678 12.8134 4.75243C12.041 4.19548 11.0599 3.91309 9.89777 3.91309C9.00995 3.91309 8.24908 4.11265 7.63756 4.50635C6.6643 5.1246 6.13909 6.18417 6.07713 7.65568L6.07278 7.75938H8.47516V7.66003C8.47516 7.26047 8.59364 6.86938 8.82799 6.49699C9.05581 6.13482 9.43668 5.9583 9.99298 5.9583C10.5539 5.9583 10.9417 6.10395 11.1458 6.39156C11.3582 6.6896 11.4656 7.02438 11.4656 7.38633C11.4656 7.6472 11.3573 7.91264 11.1128 8.24981C11.0082 8.40242 10.8678 8.54524 10.7012 8.67068C10.6519 8.70242 9.48951 9.45089 8.96255 10.0696C8.62669 10.4635 8.60669 10.9402 8.57821 11.7507L8.57147 11.9478H10.9015L10.9023 11.8498C10.907 11.502 10.9313 11.4268 10.9798 11.2765C10.9851 11.26 10.9907 11.2425 10.9967 11.2237C11.0725 10.9826 11.2747 10.7376 11.6097 10.4772L12.2199 10.0559Z"
        fill={stroke}
      />
    </svg>
  ),
  caution: ({ stroke = '#FA9500', fill = 'none', height = 18, width = 18, strokeWidth = 2, name }) => (
    <svg data-testid={name} width={width} height={height} viewBox="0 0 18 18" fill={fill} xmlns="http://www.w3.org/2000/svg">
      <g clipPath="url(#clip0_16774_1909)">
        <path
          d="M17.8467 15.1832L9.97174 1.68311C9.77015 1.33759 9.40017 1.125 8.99998 1.125C8.5998 1.125 8.22985 1.33759 8.02823 1.68311L0.15323 15.1832C-0.0497265 15.5312 -0.0511328 15.961 0.149398 16.3104C0.350176 16.6598 0.722059 16.8751 1.12498 16.8751H16.875C17.2779 16.8751 17.6498 16.6598 17.8506 16.3104C18.0511 15.961 18.0497 15.5312 17.8467 15.1832ZM8.99998 14.625C8.37927 14.625 7.87498 14.1212 7.87498 13.5C7.87498 12.8787 8.37927 12.375 8.99998 12.375C9.62183 12.375 10.125 12.8787 10.125 13.5C10.125 14.1212 9.62183 14.625 8.99998 14.625ZM10.125 10.125C10.125 10.7463 9.62127 11.25 8.99998 11.25C8.3787 11.25 7.87498 10.7463 7.87498 10.125V6.75004C7.87498 6.12875 8.3787 5.62504 8.99998 5.62504C9.62127 5.62504 10.125 6.12875 10.125 6.75004V10.125Z"
          fill={stroke}
        />
      </g>
      <defs>
        <clipPath id="clip0_16774_1909">
          <rect width="18" height="18" fill="white" />
        </clipPath>
      </defs>
    </svg>
  ),
  caretUp: ({ fill = 'none', height = 7, width = 13, name }) => (
    <svg data-testid={name} width={width} height={height} viewBox="0 0 13 7" fill={fill} xmlns="http://www.w3.org/2000/svg">
      <path
        d="M12.2071 6.65685C12.5976 6.26633 12.5976 5.63317 12.2071 5.24264L7.25736 0.292893C6.86684 -0.0976313 6.23367 -0.097631 5.84315 0.292893L0.893398 5.24264C0.502873 5.63316 0.502873 6.26633 0.893398 6.65685C1.28392 7.04738 1.91709 7.04738 2.30761 6.65685L6.55025 2.41421L10.7929 6.65685C11.1834 7.04738 11.8166 7.04738 12.2071 6.65685Z"
        fill="#94A7B7"
      />
    </svg>
  ),
  warningTriangle: ({ fill = '#FF9247', height = 25, width = 24, name, ...props }) => (
    <svg data-testid={name} width={width} height={height} viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
      <g clipPath="url(#clip0_16090_85716)">
        <path
          d="M23.7957 20.2599L13.2957 2.25977C13.0269 1.79908 12.5336 1.51562 12 1.51562C11.4664 1.51562 10.9731 1.79908 10.7043 2.25977L0.204307 20.2599C-0.066302 20.7238 -0.0681771 21.297 0.199198 21.7628C0.466901 22.2287 0.962745 22.5158 1.49998 22.5158H22.5C23.0373 22.5158 23.5331 22.2287 23.8008 21.7628C24.0681 21.297 24.0663 20.7238 23.7957 20.2599ZM12 19.5156C11.1724 19.5156 10.5 18.844 10.5 18.0156C10.5 17.1872 11.1724 16.5156 12 16.5156C12.8291 16.5156 13.5 17.1872 13.5 18.0156C13.5 18.8439 12.8291 19.5156 12 19.5156ZM13.5 13.5157C13.5 14.344 12.8284 15.0157 12 15.0157C11.1716 15.0157 10.5 14.344 10.5 13.5157V9.01567C10.5 8.1873 11.1716 7.51567 12 7.51567C12.8284 7.51567 13.5 8.1873 13.5 9.01567V13.5157Z"
          fill={fill}
        />
      </g>
    </svg>
  ),
  disable: ({ height = 16, width = 16, name }) => (
    <svg data-testid={name} width={width} height={height} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M12 21C16.9706 21 21 16.9706 21 12C21 7.02944 16.9706 3 12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21Z"
        stroke="white"
        strokeWidth="2"
      />
      <path d="M18 18L6 6" stroke="white" strokeWidth="2" />
    </svg>
  ),
  setting: ({ width = 24, height = 24, name }) => (
    <svg data-testid={name} width={width} height={height} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M12 15C13.6569 15 15 13.6569 15 12C15 10.3431 13.6569 9 12 9C10.3431 9 9 10.3431 9 12C9 13.6569 10.3431 15 12 15Z"
        stroke="white"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M19.4 15C19.2669 15.3016 19.2272 15.6362 19.286 15.9606C19.3448 16.285 19.4995 16.5843 19.73 16.82L19.79 16.88C19.976 17.0657 20.1235 17.2863 20.2241 17.5291C20.3248 17.7719 20.3766 18.0322 20.3766 18.295C20.3766 18.5578 20.3248 18.8181 20.2241 19.0609C20.1235 19.3037 19.976 19.5243 19.79 19.71C19.6043 19.896 19.3837 20.0435 19.1409 20.1441C18.8981 20.2448 18.6378 20.2966 18.375 20.2966C18.1122 20.2966 17.8519 20.2448 17.6091 20.1441C17.3663 20.0435 17.1457 19.896 16.96 19.71L16.9 19.65C16.6643 19.4195 16.365 19.2648 16.0406 19.206C15.7162 19.1472 15.3816 19.1869 15.08 19.32C14.7842 19.4468 14.532 19.6572 14.3543 19.9255C14.1766 20.1938 14.0813 20.5082 14.08 20.83V21C14.08 21.5304 13.8693 22.0391 13.4942 22.4142C13.1191 22.7893 12.6104 23 12.08 23C11.5496 23 11.0409 22.7893 10.6658 22.4142C10.2907 22.0391 10.08 21.5304 10.08 21V20.91C10.0723 20.579 9.96512 20.258 9.77251 19.9887C9.5799 19.7194 9.31074 19.5143 9 19.4C8.69838 19.2669 8.36381 19.2272 8.03941 19.286C7.71502 19.3448 7.41568 19.4995 7.18 19.73L7.12 19.79C6.93425 19.976 6.71368 20.1235 6.47088 20.2241C6.22808 20.3248 5.96783 20.3766 5.705 20.3766C5.44217 20.3766 5.18192 20.3248 4.93912 20.2241C4.69632 20.1235 4.47575 19.976 4.29 19.79C4.10405 19.6043 3.95653 19.3837 3.85588 19.1409C3.75523 18.8981 3.70343 18.6378 3.70343 18.375C3.70343 18.1122 3.75523 17.8519 3.85588 17.6091C3.95653 17.3663 4.10405 17.1457 4.29 16.96L4.35 16.9C4.58054 16.6643 4.73519 16.365 4.794 16.0406C4.85282 15.7162 4.81312 15.3816 4.68 15.08C4.55324 14.7842 4.34276 14.532 4.07447 14.3543C3.80618 14.1766 3.49179 14.0813 3.17 14.08H3C2.46957 14.08 1.96086 13.8693 1.58579 13.4942C1.21071 13.1191 1 12.6104 1 12.08C1 11.5496 1.21071 11.0409 1.58579 10.6658C1.96086 10.2907 2.46957 10.08 3 10.08H3.09C3.42099 10.0723 3.742 9.96512 4.0113 9.77251C4.28059 9.5799 4.48572 9.31074 4.6 9C4.73312 8.69838 4.77282 8.36381 4.714 8.03941C4.65519 7.71502 4.50054 7.41568 4.27 7.18L4.21 7.12C4.02405 6.93425 3.87653 6.71368 3.77588 6.47088C3.67523 6.22808 3.62343 5.96783 3.62343 5.705C3.62343 5.44217 3.67523 5.18192 3.77588 4.93912C3.87653 4.69632 4.02405 4.47575 4.21 4.29C4.39575 4.10405 4.61632 3.95653 4.85912 3.85588C5.10192 3.75523 5.36217 3.70343 5.625 3.70343C5.88783 3.70343 6.14808 3.75523 6.39088 3.85588C6.63368 3.95653 6.85425 4.10405 7.04 4.29L7.1 4.35C7.33568 4.58054 7.63502 4.73519 7.95941 4.794C8.28381 4.85282 8.61838 4.81312 8.92 4.68H9C9.29577 4.55324 9.54802 4.34276 9.72569 4.07447C9.90337 3.80618 9.99872 3.49179 10 3.17V3C10 2.46957 10.2107 1.96086 10.5858 1.58579C10.9609 1.21071 11.4696 1 12 1C12.5304 1 13.0391 1.21071 13.4142 1.58579C13.7893 1.96086 14 2.46957 14 3V3.09C14.0013 3.41179 14.0966 3.72618 14.2743 3.99447C14.452 4.26276 14.7042 4.47324 15 4.6C15.3016 4.73312 15.6362 4.77282 15.9606 4.714C16.285 4.65519 16.5843 4.50054 16.82 4.27L16.88 4.21C17.0657 4.02405 17.2863 3.87653 17.5291 3.77588C17.7719 3.67523 18.0322 3.62343 18.295 3.62343C18.5578 3.62343 18.8181 3.67523 19.0609 3.77588C19.3037 3.87653 19.5243 4.02405 19.71 4.21C19.896 4.39575 20.0435 4.61632 20.1441 4.85912C20.2448 5.10192 20.2966 5.36217 20.2966 5.625C20.2966 5.88783 20.2448 6.14808 20.1441 6.39088C20.0435 6.63368 19.896 6.85425 19.71 7.04L19.65 7.1C19.4195 7.33568 19.2648 7.63502 19.206 7.95941C19.1472 8.28381 19.1869 8.61838 19.32 8.92V9C19.4468 9.29577 19.6572 9.54802 19.9255 9.72569C20.1938 9.90337 20.5082 9.99872 20.83 10H21C21.5304 10 22.0391 10.2107 22.4142 10.5858C22.7893 10.9609 23 11.4696 23 12C23 12.5304 22.7893 13.0391 22.4142 13.4142C22.0391 13.7893 21.5304 14 21 14H20.91C20.5882 14.0013 20.2738 14.0966 20.0055 14.2743C19.7372 14.452 19.5268 14.7042 19.4 15V15Z"
        stroke="white"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  ),
  editPen: ({ height = 24, width = 24, name, stroke = '#2376F3' }) => (
    <svg data-testid={name} width={width} height={height} viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M12 20.5H21" stroke={stroke} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      <path
        d="M16.5 4.00023C16.8978 3.6024 17.4374 3.37891 18 3.37891C18.2786 3.37891 18.5544 3.43378 18.8118 3.54038C19.0692 3.64699 19.303 3.80324 19.5 4.00023C19.697 4.19721 19.8532 4.43106 19.9598 4.68843C20.0665 4.9458 20.1213 5.22165 20.1213 5.50023C20.1213 5.7788 20.0665 6.05465 19.9598 6.31202C19.8532 6.56939 19.697 6.80324 19.5 7.00023L7 19.5002L3 20.5002L4 16.5002L16.5 4.00023Z"
        stroke={stroke}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  ),
  chevronLeft: ({ height = 14, width = 8, name, fill = '#A9AFBC' }) => (
    <svg data-testid={name} width={width} height={height} viewBox="0 0 8 14" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M7 1L1 7L7 13" stroke={fill} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    </svg>
  ),
  chevronRight: ({ height = 14, width = 8, name, fill = '#A9AFBC' }) => (
    <svg data-testid={name} width={width} height={height} viewBox="0 0 8 14" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M1 13L7 7L0.999999 1" stroke={fill} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    </svg>
  ),
  office: ({ height = 16, width = 16, name, ...props }) => (
    <svg data-testid={name} width={width} height={height} {...props} viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g clipPath="url(#clip0_20669_35615)">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M10.8307 2.5C11.7094 2.5 12.4292 3.1799 12.4928 4.04228L12.4974 4.16667V15.8333H13.3307V7.91667C13.3307 7.71212 13.4782 7.54199 13.6725 7.50671L13.7474 7.5H14.9974C15.8761 7.5 16.5959 8.1799 16.6595 9.04228L16.6641 9.16667V15.8333H17.4974C17.9576 15.8333 18.3307 16.2064 18.3307 16.6667C18.3307 17.094 18.009 17.4463 17.5946 17.4944L17.4974 17.5H2.4974C2.03716 17.5 1.66406 17.1269 1.66406 16.6667C1.66406 16.2393 1.98577 15.8871 2.40021 15.8389L2.4974 15.8333H3.33073V4.16667C3.33073 3.28803 4.01063 2.5682 4.87301 2.50457L4.9974 2.5H10.8307ZM9.16406 12.5H6.66406V14.1667H9.16406V12.5ZM9.16406 9.16667H6.66406V10.8333H9.16406V9.16667ZM9.16406 5.83333H6.66406V7.5H9.16406V5.83333Z"
          fill="#A9AFBC"
        />
      </g>
      <defs>
        <clipPath id="clip0_20669_35615">
          <rect data-testid={name} width={width} height={height} fill="white" />
        </clipPath>
      </defs>
    </svg>
  ),
  cross: ({ width = 24, height = 24, stroke = '#2376F3' }) => (
    <svg xmlns="http://www.w3.org/2000/svg" width={width} height={height} viewBox="0 0 24 24" fill="none" aria-hidden>
      <path d="M12 5V19" stroke={stroke} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M5 12H19" stroke={stroke} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    </svg>
  ),
  validForm: ({ width = 20, height = 20, className = '' }) => (
    <svg width={width} height={height} viewBox="0 0 20 20" className={className} fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M10 20C4.47715 20 0 15.5228 0 10C0 4.47715 4.47715 0 10 0C15.5228 0 20 4.47715 20 10C20 15.5228 15.5228 20 10 20ZM6 8L4 10L9 15L16 8L14 6L9 11L6 8Z"
        fill="#48CEB0"
      />
    </svg>
  ),
  emptyForm: ({ width = 20, height = 20, className = '' }) => (
    <svg width={width} height={height} className={className} viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <circle cx="10" cy="10" r="10" fill="#DDE2EC" />
    </svg>
  ),
  invalidForm: ({ width = 20, height = 20, className = '' }) => (
    <svg width={width} height={height} className={className} viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M10 20C4.47715 20 0 15.5228 0 10C0 4.47715 4.47715 0 10 0C15.5228 0 20 4.47715 20 10C20 15.5228 15.5228 20 10 20Z"
        fill="#F32345"
      />
      <path
        d="M8.5 14.5C8.5 15.3284 9.17238 16 10 16C10.8291 16 11.5 15.3283 11.5 14.5C11.5 13.6716 10.8291 13 10 13C9.17238 13 8.5 13.6716 8.5 14.5Z"
        fill="white"
      />
      <path
        d="M10 11.5001C10.8284 11.5001 11.5 10.8284 11.5 10V5.50001C11.5 4.67163 10.8284 4 10 4C9.17163 4 8.5 4.67163 8.5 5.50001V10C8.5 10.8284 9.17163 11.5001 10 11.5001Z"
        fill="white"
      />
    </svg>
  ),
  disabled: ({ style = {}, stroke = '#33363F', strokeWidth = 2, width = 20, height = 20, name }) => (
    <svg data-testid={name} style={style} width={width} height={height} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <circle cx="12" cy="12" r="9" stroke={stroke} strokeWidth={strokeWidth} />
      <path d="M18 18L6 6" stroke={stroke} strokeWidth="2" />
    </svg>
  ),
  arrowUpRight: ({ stroke = '#2376F3', style = {}, strokeWidth = 2, height = 12, width = 12 }) => (
    <svg style={style} width={width} height={height} viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M1 13L13 1" stroke={stroke} strokeWidth={strokeWidth} strokeLinecap="round" strokeLinejoin="round" />
      <path d="M4 1H13V10" stroke={stroke} strokeWidth={strokeWidth} strokeLinecap="round" strokeLinejoin="round" />
    </svg>
  ),
  circledInfo: ({ width = 16, height = 16, style = {}, fill = '#AABDCE' }) => (
    <svg style={style} width={width} height={height} viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M10 0.300049C4.47707 0.300049 0 4.77728 0 10.3C0 15.8231 4.47707 20.3 10 20.3C15.5229 20.3 20 15.8231 20 10.3C20 4.77728 15.5229 0.300049 10 0.300049ZM10 5.30005C10.6904 5.30005 11.25 5.85974 11.25 6.55005C11.25 7.24067 10.6904 7.80005 10 7.80005C9.30961 7.80005 8.75 7.24067 8.75 6.55005C8.75 5.85974 9.30961 5.30005 10 5.30005ZM11.875 15.3H8.125C7.77984 15.3 7.5 15.0205 7.5 14.675C7.5 14.3299 7.77984 14.05 8.125 14.05H8.75V10.3H8.125C7.77984 10.3 7.5 10.0205 7.5 9.67505C7.5 9.32989 7.77984 9.05005 8.125 9.05005H10.625C10.9702 9.05005 11.25 9.32989 11.25 9.67505V14.05H11.875C12.2202 14.05 12.5 14.3299 12.5 14.675C12.5 15.0205 12.2202 15.3 11.875 15.3Z"
        fill={fill}
      />
    </svg>
  ),
  warningOrange: ({ height = 18, width = 17, name, fill = '#FF9247', ...props }) => (
    <svg data-testid={name} width={width} height={height} viewBox="0 0 17 18" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
      <path
        d="M8.5 0.5C13.1944 0.5 17 4.30558 17 9C17 13.6944 13.1944 17.5 8.5 17.5C3.80558 17.5 0 13.6944 0 9C0 4.30558 3.80558 0.5 8.5 0.5ZM6.72918 3.51043L7.61457 10.7709L9.38543 10.7708L10.2708 3.51043H6.72918ZM8.5 11.8333C7.7176 11.8333 7.08332 12.4676 7.08332 13.25C7.08332 14.0324 7.7176 14.6667 8.5 14.6667C9.2824 14.6667 9.91668 14.0324 9.91668 13.25C9.91668 12.4676 9.2824 11.8333 8.5 11.8333Z"
        fill={fill}
      />
    </svg>
  ),
  file: ({ style = {}, width = 16, height = 16, fill = '#94A7B7' }) => (
    <svg style={style} width={width} height={height} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M21 9V20.993C21.0009 21.1243 20.976 21.2545 20.9266 21.3762C20.8772 21.4979 20.8043 21.6087 20.7121 21.7022C20.6199 21.7957 20.5101 21.8701 20.3892 21.9212C20.2682 21.9723 20.1383 21.9991 20.007 22H3.993C3.72981 22 3.47739 21.8955 3.2912 21.7095C3.105 21.5235 3.00027 21.2712 3 21.008V2.992C3 2.455 3.447 2 3.998 2H14V8C14 8.26522 14.1054 8.51957 14.2929 8.70711C14.4804 8.89464 14.7348 9 15 9H21ZM21 7H16V2.003L21 7Z"
        fill={fill}
      />
    </svg>
  ),
  close: ({ stroke = '#94A7B7', height = 20, width = 20, strokeWidth = 2, name }) => (
    <svg data-testid={name} width={width} height={height} viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M15 5L5 15" stroke={stroke} strokeWidth={strokeWidth} strokeLinecap="round" strokeLinejoin="round" />
      <path d="M5 5L15 15" stroke={stroke} strokeWidth={strokeWidth} strokeLinecap="round" strokeLinejoin="round" />
    </svg>
  ),
  infoRounded: ({ fill = '#F32345', height = 20, width = 20, name }) => (
    <svg data-testid={name} width={width} height={height} viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M10 0C4.47707 0 0 4.47723 0 10C0 15.5231 4.47707 20 10 20C15.5229 20 20 15.5231 20 10C20 4.47723 15.5229 0 10 0ZM10 5C10.6904 5 11.25 5.55969 11.25 6.25C11.25 6.94063 10.6904 7.5 10 7.5C9.30961 7.5 8.75 6.94063 8.75 6.25C8.75 5.55969 9.30961 5 10 5ZM11.875 15H8.125C7.77984 15 7.5 14.7205 7.5 14.375C7.5 14.0298 7.77984 13.75 8.125 13.75H8.75V10H8.125C7.77984 10 7.5 9.72047 7.5 9.375C7.5 9.02984 7.77984 8.75 8.125 8.75H10.625C10.9702 8.75 11.25 9.02984 11.25 9.375V13.75H11.875C12.2202 13.75 12.5 14.0298 12.5 14.375C12.5 14.7205 12.2202 15 11.875 15Z"
        fill={fill}
      />
    </svg>
  ),
  merchantIcon: ({ fill = '#48CEB0', height = 64, width = 64, name }) => (
    <svg data-testid={name} width={width} height={height} viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect width="64" height="64" rx="32" fill={fill} />
      <path
        d="M37.8018 21.6001C37.8018 25.2453 34.8468 28.2003 31.2017 28.2003C27.5565 28.2003 24.6016 25.2453 24.6016 21.6001C24.6016 17.955 27.5565 15 31.2017 15C34.8468 15 37.8018 17.955 37.8018 21.6001Z"
        fill="white"
      />
      <path
        d="M37.2694 33.9791C36.4888 33.9885 35.7613 34.017 35.1272 34.1021C34.0664 34.2449 32.9054 34.5858 31.9459 35.5451C30.9866 36.5044 30.6457 37.6656 30.5031 38.7264C30.3749 39.6796 30.3751 40.8444 30.3753 42.0827V42.3665C30.3751 43.6049 30.3749 44.7696 30.5031 45.7229C30.6031 46.4672 30.8008 47.2609 31.2414 47.9997C31.2277 47.9997 31.214 47.9997 31.2003 47.9997C18 47.9997 18 44.6754 18 40.5746C18 36.4737 23.91 33.1494 31.2003 33.1494C33.3886 33.1494 35.4524 33.4489 37.2694 33.9791Z"
        fill="white"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M38.6257 47.9995C35.9033 47.9995 34.542 47.9995 33.6964 47.1537C32.8506 46.308 32.8506 44.9467 32.8506 42.2243C32.8506 39.502 32.8506 38.1407 33.6964 37.295C34.542 36.4492 35.9033 36.4492 38.6257 36.4492C41.3481 36.4492 42.7094 36.4492 43.555 37.295C44.4008 38.1407 44.4008 39.502 44.4008 42.2243C44.4008 44.9467 44.4008 46.308 43.555 47.1537C42.7094 47.9995 41.3481 47.9995 38.6257 47.9995ZM41.873 40.9799C42.2489 40.604 42.2489 39.9947 41.873 39.6188C41.4971 39.2427 40.8877 39.2427 40.5119 39.6188L37.3423 42.7882L36.7396 42.1854C36.3637 41.8095 35.7543 41.8095 35.3784 42.1854C35.0026 42.5613 35.0026 43.1708 35.3784 43.5467L36.6617 44.8299C37.0376 45.206 37.6471 45.206 38.023 44.8299L41.873 40.9799Z"
        fill="white"
      />
    </svg>
  ),
  download: ({ width = '20', height = '21', fill = 'none', ...props }) => (
    <svg width={width} height={height} viewBox="0 0 20 21" fill={fill} xmlns="http://www.w3.org/2000/svg" {...props}>
      <path
        d="M2.92892 18.0613C4.81769 19.9501 7.32893 20.9902 10 20.9902C12.6711 20.9902 15.1823 19.9501 17.0711 18.0613C18.9598 16.1725 20 13.6613 20 10.9902C20 8.31916 18.9598 5.80792 17.0711 3.91916C15.1823 2.03039 12.6711 0.990235 10 0.990235C7.32889 0.990235 4.81768 2.03039 2.92892 3.91916C1.04015 5.80792 -1.10774e-06 8.31912 -8.74228e-07 10.9902C-6.40712e-07 13.6613 1.04015 16.1726 2.92892 18.0613ZM5.58033 11.8913C5.67999 11.6508 5.9147 11.4939 6.17509 11.4939L7.82626 11.4939L7.82626 6.01779C7.82626 5.66226 8.11449 5.37402 8.47002 5.37402L11.53 5.37402C11.8855 5.37402 12.1737 5.66226 12.1737 6.01779L12.1737 11.4939L13.825 11.4939C14.0853 11.4939 14.3201 11.6507 14.4197 11.8913C14.5193 12.1319 14.4643 12.4088 14.2801 12.5929L10.4552 16.4179C10.3345 16.5386 10.1708 16.6064 10 16.6064C9.82932 16.6064 9.66554 16.5386 9.54486 16.4179L5.71995 12.5929C5.53578 12.4088 5.48068 12.1319 5.58033 11.8913Z"
        fill="#2376F3"
      />
    </svg>
  ),
  settings: ({ height = 14, width = 14, name }) => (
    <svg data-testid={name} width={width} height={height} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g clipPath="url(#clip0_2492_1054)">
        <path
          d="M12 15C13.6569 15 15 13.6569 15 12C15 10.3431 13.6569 9 12 9C10.3431 9 9 10.3431 9 12C9 13.6569 10.3431 15 12 15Z"
          stroke="#2376F3"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M19.4 15C19.2669 15.3016 19.2272 15.6362 19.286 15.9606C19.3448 16.285 19.4995 16.5843 19.73 16.82L19.79 16.88C19.976 17.0657 20.1235 17.2863 20.2241 17.5291C20.3248 17.7719 20.3766 18.0322 20.3766 18.295C20.3766 18.5578 20.3248 18.8181 20.2241 19.0609C20.1235 19.3037 19.976 19.5243 19.79 19.71C19.6043 19.896 19.3837 20.0435 19.1409 20.1441C18.8981 20.2448 18.6378 20.2966 18.375 20.2966C18.1122 20.2966 17.8519 20.2448 17.6091 20.1441C17.3663 20.0435 17.1457 19.896 16.96 19.71L16.9 19.65C16.6643 19.4195 16.365 19.2648 16.0406 19.206C15.7162 19.1472 15.3816 19.1869 15.08 19.32C14.7842 19.4468 14.532 19.6572 14.3543 19.9255C14.1766 20.1938 14.0813 20.5082 14.08 20.83V21C14.08 21.5304 13.8693 22.0391 13.4942 22.4142C13.1191 22.7893 12.6104 23 12.08 23C11.5496 23 11.0409 22.7893 10.6658 22.4142C10.2907 22.0391 10.08 21.5304 10.08 21V20.91C10.0723 20.579 9.96512 20.258 9.77251 19.9887C9.5799 19.7194 9.31074 19.5143 9 19.4C8.69838 19.2669 8.36381 19.2272 8.03941 19.286C7.71502 19.3448 7.41568 19.4995 7.18 19.73L7.12 19.79C6.93425 19.976 6.71368 20.1235 6.47088 20.2241C6.22808 20.3248 5.96783 20.3766 5.705 20.3766C5.44217 20.3766 5.18192 20.3248 4.93912 20.2241C4.69632 20.1235 4.47575 19.976 4.29 19.79C4.10405 19.6043 3.95653 19.3837 3.85588 19.1409C3.75523 18.8981 3.70343 18.6378 3.70343 18.375C3.70343 18.1122 3.75523 17.8519 3.85588 17.6091C3.95653 17.3663 4.10405 17.1457 4.29 16.96L4.35 16.9C4.58054 16.6643 4.73519 16.365 4.794 16.0406C4.85282 15.7162 4.81312 15.3816 4.68 15.08C4.55324 14.7842 4.34276 14.532 4.07447 14.3543C3.80618 14.1766 3.49179 14.0813 3.17 14.08H3C2.46957 14.08 1.96086 13.8693 1.58579 13.4942C1.21071 13.1191 1 12.6104 1 12.08C1 11.5496 1.21071 11.0409 1.58579 10.6658C1.96086 10.2907 2.46957 10.08 3 10.08H3.09C3.42099 10.0723 3.742 9.96512 4.0113 9.77251C4.28059 9.5799 4.48572 9.31074 4.6 9C4.73312 8.69838 4.77282 8.36381 4.714 8.03941C4.65519 7.71502 4.50054 7.41568 4.27 7.18L4.21 7.12C4.02405 6.93425 3.87653 6.71368 3.77588 6.47088C3.67523 6.22808 3.62343 5.96783 3.62343 5.705C3.62343 5.44217 3.67523 5.18192 3.77588 4.93912C3.87653 4.69632 4.02405 4.47575 4.21 4.29C4.39575 4.10405 4.61632 3.95653 4.85912 3.85588C5.10192 3.75523 5.36217 3.70343 5.625 3.70343C5.88783 3.70343 6.14808 3.75523 6.39088 3.85588C6.63368 3.95653 6.85425 4.10405 7.04 4.29L7.1 4.35C7.33568 4.58054 7.63502 4.73519 7.95941 4.794C8.28381 4.85282 8.61838 4.81312 8.92 4.68H9C9.29577 4.55324 9.54802 4.34276 9.72569 4.07447C9.90337 3.80618 9.99872 3.49179 10 3.17V3C10 2.46957 10.2107 1.96086 10.5858 1.58579C10.9609 1.21071 11.4696 1 12 1C12.5304 1 13.0391 1.21071 13.4142 1.58579C13.7893 1.96086 14 2.46957 14 3V3.09C14.0013 3.41179 14.0966 3.72618 14.2743 3.99447C14.452 4.26276 14.7042 4.47324 15 4.6C15.3016 4.73312 15.6362 4.77282 15.9606 4.714C16.285 4.65519 16.5843 4.50054 16.82 4.27L16.88 4.21C17.0657 4.02405 17.2863 3.87653 17.5291 3.77588C17.7719 3.67523 18.0322 3.62343 18.295 3.62343C18.5578 3.62343 18.8181 3.67523 19.0609 3.77588C19.3037 3.87653 19.5243 4.02405 19.71 4.21C19.896 4.39575 20.0435 4.61632 20.1441 4.85912C20.2448 5.10192 20.2966 5.36217 20.2966 5.625C20.2966 5.88783 20.2448 6.14808 20.1441 6.39088C20.0435 6.63368 19.896 6.85425 19.71 7.04L19.65 7.1C19.4195 7.33568 19.2648 7.63502 19.206 7.95941C19.1472 8.28381 19.1869 8.61838 19.32 8.92V9C19.4468 9.29577 19.6572 9.54802 19.9255 9.72569C20.1938 9.90337 20.5082 9.99872 20.83 10H21C21.5304 10 22.0391 10.2107 22.4142 10.5858C22.7893 10.9609 23 11.4696 23 12C23 12.5304 22.7893 13.0391 22.4142 13.4142C22.0391 13.7893 21.5304 14 21 14H20.91C20.5882 14.0013 20.2738 14.0966 20.0055 14.2743C19.7372 14.452 19.5268 14.7042 19.4 15V15Z"
          stroke="#2376F3"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_2492_1054">
          <rect width="24" height="24" fill="white" />
        </clipPath>
      </defs>
    </svg>
  ),
  lowRisk: ({ height = 16, width = 16, name }) => (
    <svg data-testid={name} width={width} height={height} viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M8 0C12.4183 0 16 3.58166 16 8C16 12.4183 12.4183 16 8 16C3.58166 16 0 12.4183 0 8C0 3.58166 3.58166 0 8 0ZM4.26716 8.19681L7.89391 12.0091C7.92163 12.038 7.95994 12.0544 8 12.0544C8.04006 12.0544 8.07838 12.0381 8.10609 12.0091L11.7331 8.19681C12.0839 7.82834 12.0696 7.24569 11.7009 6.89512C11.3324 6.54456 10.7492 6.55919 10.3992 6.92737L8.92091 8.48131V4.80016C8.92091 4.29134 8.50884 3.87928 8.00003 3.87928C7.49122 3.87928 7.07941 4.29134 7.07941 4.80016V8.48134L5.60084 6.92741C5.25106 6.55922 4.66763 6.54459 4.29944 6.89516C3.93072 7.24569 3.91606 7.82838 4.26716 8.19681Z"
        fill="#24B314"
      />
    </svg>
  ),
  tipsIcon: ({ fill = '#AABDCE', height = 20, width = 24, name }) => (
    <svg data-testid={name} width={width} height={height} viewBox="0 0 20 20" fill={fill} xmlns="http://www.w3.org/2000/svg">
      <path
        d="M10 0C4.47707 0 0 4.47723 0 10C0 15.5231 4.47707 20 10 20C15.5229 20 20 15.5231 20 10C20 4.47723 15.5229 0 10 0ZM10 5C10.6904 5 11.25 5.55969 11.25 6.25C11.25 6.94063 10.6904 7.5 10 7.5C9.30961 7.5 8.75 6.94063 8.75 6.25C8.75 5.55969 9.30961 5 10 5ZM11.875 15H8.125C7.77984 15 7.5 14.7205 7.5 14.375C7.5 14.0298 7.77984 13.75 8.125 13.75H8.75V10H8.125C7.77984 10 7.5 9.72047 7.5 9.375C7.5 9.02984 7.77984 8.75 8.125 8.75H10.625C10.9702 8.75 11.25 9.02984 11.25 9.375V13.75H11.875C12.2202 13.75 12.5 14.0298 12.5 14.375C12.5 14.7205 12.2202 15 11.875 15Z"
        fill={fill}
      />
    </svg>
  ),
  trashIcon: ({ height = 24, width = 24, name, fill = '#F32345', ...props }) => (
    <svg data-testid={name} width={width} height={height} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
      <path d="M3 6H5H21" stroke={fill} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      <path
        d="M20 6C20 5.44772 19.5523 5 19 5C18.4477 5 18 5.44772 18 6H20ZM6 6C6 5.44772 5.55228 5 5 5C4.44772 5 4 5.44772 4 6H6ZM7 6C7 6.55228 7.44772 7 8 7C8.55228 7 9 6.55228 9 6H7ZM15 6C15 6.55228 15.4477 7 16 7C16.5523 7 17 6.55228 17 6H15ZM18 6V20H20V6H18ZM18 20C18 20.5523 17.5523 21 17 21V23C18.6569 23 20 21.6569 20 20H18ZM17 21H7V23H17V21ZM7 21C6.44772 21 6 20.5523 6 20H4C4 21.6569 5.34315 23 7 23V21ZM6 20V6H4V20H6ZM9 6V4H7V6H9ZM9 4C9 3.44772 9.44772 3 10 3V1C8.34315 1 7 2.34315 7 4H9ZM10 3H14V1H10V3ZM14 3C14.5523 3 15 3.44772 15 4H17C17 2.34315 15.6569 1 14 1V3ZM15 4V6H17V4H15Z"
        fill={fill}
      />
      <path d="M10 11V17" stroke={fill} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M14 11V17" stroke={fill} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    </svg>
  ),
  complete: ({ fill = '#01B352', height = 100, width = 101, name }) => (
    <svg data-testid={name} width={width} height={height} viewBox="0 0 101 100" fill={fill} xmlns="http://www.w3.org/2000/svg">
      <path
        d="M100.5 50C100.5 22.3854 78.1146 0 50.5 0C22.8861 0 0.5 22.3854 0.5 50C0.5 77.6146 22.8861 100 50.5 100C78.1146 100 100.5 77.6146 100.5 50Z"
        fill={fill}
      />
      <path d="M47.3843 59.5L39 50.3525L42.1845 47.0956L47.3331 52.7128L58.7666 40.5L62 43.6509L47.3843 59.5Z" fill="white" />
    </svg>
  ),
  errorInfoIcon: ({ fill = '#F32345', height = 100, width = 101, name }) => (
    <svg data-testid={name} width={width} height={height} viewBox="0 0 20 20" fill={fill} xmlns="http://www.w3.org/2000/svg">
      <path
        d="M10 0C4.47707 0 0 4.47723 0 10C0 15.5231 4.47707 20 10 20C15.5229 20 20 15.5231 20 10C20 4.47723 15.5229 0 10 0ZM10 5C10.6904 5 11.25 5.55969 11.25 6.25C11.25 6.94063 10.6904 7.5 10 7.5C9.30961 7.5 8.75 6.94063 8.75 6.25C8.75 5.55969 9.30961 5 10 5ZM11.875 15H8.125C7.77984 15 7.5 14.7205 7.5 14.375C7.5 14.0298 7.77984 13.75 8.125 13.75H8.75V10H8.125C7.77984 10 7.5 9.72047 7.5 9.375C7.5 9.02984 7.77984 8.75 8.125 8.75H10.625C10.9702 8.75 11.25 9.02984 11.25 9.375V13.75H11.875C12.2202 13.75 12.5 14.0298 12.5 14.375C12.5 14.7205 12.2202 15 11.875 15Z"
        fill={fill}
      />
    </svg>
  ),
  processIcon: ({ fill = '#DDE2EC', height = 24, width = 24, name }) => (
    <svg data-testid={name} width={width} height={height} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M21.2761 19.6127C23.0374 17.4667 24 14.7763 24 12H21C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02946 7.02944 3.00002 12 3.00002C14.4853 3.00002 16.7353 4.00738 18.364 5.63606L20.4853 3.51474C18.5222 1.55162 15.9391 0.329923 13.1762 0.0578019C10.4133 -0.214319 7.64154 0.479975 5.33316 2.02238C3.02478 3.56479 1.32263 5.85989 0.516719 8.5166C-0.289188 11.1733 -0.148981 14.0273 0.913449 16.5922C1.97588 19.1572 3.8948 21.2744 6.34324 22.5831C8.79169 23.8918 11.6182 24.3111 14.3411 23.7694C17.064 23.2278 19.5149 21.7588 21.2761 19.6127Z"
        fill={fill}
      />
    </svg>
  ),
  upwardTrend: ({ fill = '#414F5F', height = 24, width = 24, name }) => (
    <svg data-testid={name} width={width} height={height} viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g clipPath="url(#clip0_44761_17991)">
        <path
          d="M17.25 4.5L10.125 11.625L6.375 7.875L0.75 13.5"
          stroke={fill}
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path d="M12.75 4.5H17.25V9" stroke={fill} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      </g>
      <defs>
        <clipPath id="clip0_44761_17991">
          <rect width="18" height="18" fill="white" />
        </clipPath>
      </defs>
    </svg>
  ),
  loading: ({ height = 14, width = 14, name, fill = '#A9AFBC' }) => (
    <svg data-testid={name} width={width} height={height} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M21.2761 19.6127C23.0374 17.4667 24 14.7763 24 12H21C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02946 7.02944 3.00002 12 3.00002C14.4853 3.00002 16.7353 4.00738 18.364 5.63606L20.4853 3.51474C18.5222 1.55162 15.9391 0.329923 13.1762 0.0578019C10.4133 -0.214319 7.64154 0.479975 5.33316 2.02238C3.02478 3.56479 1.32263 5.85989 0.516719 8.5166C-0.289188 11.1733 -0.148981 14.0273 0.913449 16.5922C1.97588 19.1572 3.8948 21.2744 6.34324 22.5831C8.79169 23.8918 11.6182 24.3111 14.3411 23.7694C17.064 23.2278 19.5149 21.7588 21.2761 19.6127Z"
        fill={fill}
      />
    </svg>
  ),
  fileIcon: ({ fill = 'none', height = 25, width = 24, name, stroke = '#2376F3', strokeWidth = '2' }) => (
    <svg data-testid={name} width={width} height={height} viewBox="0 0 24 25" fill={fill} xmlns="http://www.w3.org/2000/svg">
      <path
        d="M13 2.5H6C5.46957 2.5 4.96086 2.71071 4.58579 3.08579C4.21071 3.46086 4 3.96957 4 4.5V20.5C4 21.0304 4.21071 21.5391 4.58579 21.9142C4.96086 22.2893 5.46957 22.5 6 22.5H18C18.5304 22.5 19.0391 22.2893 19.4142 21.9142C19.7893 21.5391 20 21.0304 20 20.5V9.5L13 2.5Z"
        stroke={stroke}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path d="M13 2.5V9.5H20" stroke={stroke} strokeWidth={strokeWidth} strokeLinecap="round" strokeLinejoin="round" />
    </svg>
  ),
  calendarIcon: ({ fill = 'white', height = 17, width = 16, name, stroke = '#A9AFBC', strokeWidth = '2' }) => (
    <svg data-testid={name} width={width} height={height} viewBox="0 0 16 17" fill={fill} xmlns="http://www.w3.org/2000/svg">
      <g clipPath="url(#clip0_14928_1954)">
        <path
          d="M12.6667 3.56677H3.33333C2.59695 3.56677 2 4.16373 2 4.90011V14.2334C2 14.9698 2.59695 15.5668 3.33333 15.5668H12.6667C13.403 15.5668 14 14.9698 14 14.2334V4.90011C14 4.16373 13.403 3.56677 12.6667 3.56677Z"
          stroke="#A9AFBC"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path d="M10.6667 2.23352V4.90019" stroke={stroke} strokeWidth={strokeWidth} strokeLinecap="round" strokeLinejoin="round" />
        <path d="M5.33334 2.23352V4.90019" stroke={stroke} strokeWidth={strokeWidth} strokeLinecap="round" strokeLinejoin="round" />
        <path d="M2 7.56677H14" stroke={stroke} strokeWidth={strokeWidth} strokeLinecap="round" strokeLinejoin="round" />
      </g>
      <defs>
        <clipPath id="clip0_14928_1954">
          <rect width="16" height="16" fill="white" transform="translate(0 0.900146)" />
        </clipPath>
      </defs>
    </svg>
  ),
  success: ({ height = 14, width = 14, name }) => (
    <svg data-testid={name} width={width} height={height} viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M10 1.875C5.5125 1.875 1.875 5.5125 1.875 10C1.875 14.4875 5.5125 18.125 10 18.125C14.4875 18.125 18.125 14.4875 18.125 10C18.125 5.5125 14.4875 1.875 10 1.875ZM14.5362 7.69187L8.6075 13.62C8.45875 13.7681 8.21813 13.7681 8.06938 13.62L7.95 13.5006L7.94938 13.5012L4.6875 10.2163C4.53875 10.0675 4.53875 9.82625 4.6875 9.6775L5.49625 8.86938C5.645 8.72062 5.88625 8.72062 6.035 8.86938L8.34063 11.1919L13.1881 6.34438C13.3369 6.19562 13.5781 6.19562 13.7269 6.34438L14.5356 7.15312C14.685 7.30125 14.685 7.5425 14.5362 7.69187Z"
        fill="#24B314"
      />
    </svg>
  ),
  midRisk: ({ height = 16, width = 16, name }) => (
    <svg data-testid={name} width={width} height={height} viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M16 8C16 3.58166 12.4183 0 8 0C3.58166 0 0 3.58166 0 8C0 12.4183 3.58166 16 8 16C12.4183 16 16 12.4183 16 8ZM4 8C4 8.55229 4.44772 9 5 9H11C11.5523 9 12 8.55228 12 8C12 7.44771 11.5523 7 11 7L5 7C4.44772 7 4 7.44772 4 8Z"
        fill="#FA9500"
      />
    </svg>
  ),
  arrowDown: ({ height = 9, width = 9, name, strokeWidth = 2, stroke = '#414F5F', ...props }) => (
    <svg data-testid={name} width={width} height={height} viewBox="0 0 14 9" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
      <path d="M1 1.5L7 7.5L13 1.5" stroke={stroke} strokeWidth={strokeWidth} strokeLinecap="round" strokeLinejoin="round" />
    </svg>
  ),
  avatar: ({ height = 64, width = 64, name }) => (
    <svg data-testid={name} width={width} height={height} viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect width="64" height="64" rx="32" fill="url(#paint0_linear_23239_24807)" />
      <path
        d="M37.8346 32.0003H40.3346V38.667C40.3346 39.109 40.159 39.5329 39.8465 39.8455C39.5339 40.1581 39.11 40.3337 38.668 40.3337H37.8346V32.0003ZM36.168 25.3337V40.3337H25.3346C24.8926 40.3337 24.4687 40.1581 24.1561 39.8455C23.8436 39.5329 23.668 39.109 23.668 38.667V25.3337C23.668 24.8916 23.8436 24.4677 24.1561 24.1551C24.4687 23.8426 24.8926 23.667 25.3346 23.667H34.5013C34.9433 23.667 35.3673 23.8426 35.6798 24.1551C35.9924 24.4677 36.168 24.8916 36.168 25.3337ZM30.3346 35.3337H26.168V37.0003H30.3346V35.3337ZM33.668 31.167H26.168V32.8337H33.668V31.167ZM33.668 27.0003H26.168V28.667H33.668V27.0003Z"
        fill="white"
      />
      <defs>
        <linearGradient id="paint0_linear_23239_24807" x1="-3.63216e-07" y1="24" x2="59.5" y2="43" gradientUnits="userSpaceOnUse">
          <stop stopColor="#6FAAE5" />
          <stop offset="1" stopColor="#C599D8" />
        </linearGradient>
      </defs>
    </svg>
  ),
  receipt: ({ height = 31, width = 34 }) => (
    <svg width={width} height={height} viewBox="0 0 44 41" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect x="0.5" width="43" height="40.7949" rx="20.3974" fill="#DDE2EC" />
      <rect
        x="10.9727"
        y="14.8848"
        width="22.0513"
        height="15.4359"
        rx="1.10256"
        stroke="white"
        strokeWidth="2.20513"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M26.4065 30.3208V12.6797C26.4065 11.4619 25.4192 10.4746 24.2013 10.4746H19.7911C18.5732 10.4746 17.5859 11.4619 17.5859 12.6797V30.3208"
        stroke="white"
        strokeWidth="2.20513"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  ),
  failed: ({ height = 14, width = 14, name }) => (
    <svg data-testid={name} width={width} height={height} viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M10 18.125C5.5125 18.125 1.875 14.4875 1.875 10C1.875 5.5125 5.5125 1.875 10 1.875C14.4875 1.875 18.125 5.5125 18.125 10C18.125 14.4875 14.4875 18.125 10 18.125ZM13.7256 7.63062C13.8781 7.47812 13.8781 7.23 13.7256 7.0775L12.8956 6.24813C12.7431 6.09562 12.495 6.09562 12.3425 6.24813L9.99188 8.59875L7.64125 6.24813C7.48875 6.09562 7.24062 6.09562 7.08812 6.24813L6.25812 7.0775C6.10562 7.23 6.10562 7.47812 6.25812 7.63062L8.60938 9.98187L6.25812 12.3319C6.10562 12.4844 6.10562 12.7325 6.25812 12.885L7.08812 13.715C7.24062 13.8675 7.48875 13.8675 7.64125 13.715L9.99188 11.3637L12.3425 13.715C12.495 13.8675 12.7431 13.8675 12.8956 13.715L13.7256 12.885C13.8781 12.7325 13.8781 12.4844 13.7256 12.3319L11.3744 9.98187L13.7256 7.63062Z"
        fill="#F32345"
      />
    </svg>
  ),
  infoSolid: ({ style = {}, width = 20, height = 20, fill = '#94A7B7', className }) => (
    <svg
      style={style}
      width={width}
      height={height}
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d="M10 0C4.47707 0 0 4.47723 0 10C0 15.5231 4.47707 20 10 20C15.5229 20 20 15.5231 20 10C20 4.47723 15.5229 0 10 0ZM10 5C10.6904 5 11.25 5.55969 11.25 6.25C11.25 6.94063 10.6904 7.5 10 7.5C9.30961 7.5 8.75 6.94063 8.75 6.25C8.75 5.55969 9.30961 5 10 5ZM11.875 15H8.125C7.77984 15 7.5 14.7205 7.5 14.375C7.5 14.0298 7.77984 13.75 8.125 13.75H8.75V10H8.125C7.77984 10 7.5 9.72047 7.5 9.375C7.5 9.02984 7.77984 8.75 8.125 8.75H10.625C10.9702 8.75 11.25 9.02984 11.25 9.375V13.75H11.875C12.2202 13.75 12.5 14.0298 12.5 14.375C12.5 14.7205 12.2202 15 11.875 15Z"
        fill={fill}
      />
    </svg>
  ),
  info: ({ width = 20, height = 20, stroke = '#AABDCE', strokeWidth = 2, ...props }) => (
    <svg width={width} height={height} viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
      <path
        d="M8.50008 15.5834C12.4121 15.5834 15.5834 12.412 15.5834 8.50002C15.5834 4.588 12.4121 1.41669 8.50008 1.41669C4.58806 1.41669 1.41675 4.588 1.41675 8.50002C1.41675 12.412 4.58806 15.5834 8.50008 15.5834Z"
        stroke={stroke}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path d="M8.5 11.3333V8.5" stroke={stroke} strokeWidth={strokeWidth} strokeLinecap="round" strokeLinejoin="round" />
      <path d="M8.5 5.66669H8.51" stroke={stroke} strokeWidth={strokeWidth} strokeLinecap="round" strokeLinejoin="round" />
    </svg>
  ),
  pending: ({ height = 18, width = 18, name }) => (
    <svg data-testid={name} width={width} height={height} viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g clipPath="url(#clip0_21035_13156)">
        <path
          d="M9 16.5C13.1421 16.5 16.5 13.1421 16.5 9C16.5 4.85786 13.1421 1.5 9 1.5C4.85786 1.5 1.5 4.85786 1.5 9C1.5 13.1421 4.85786 16.5 9 16.5Z"
          stroke="#FFB447"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path d="M9 4.5V9L12 10.5" stroke="#FFB447" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      </g>
      <defs>
        <clipPath id="clip0_21035_13156">
          <rect width="18" height="18" fill="white" />
        </clipPath>
      </defs>
    </svg>
  ),
  briefCasePurple: ({ style = {}, width = 24, height = 24, fill = '#94A7B7' }) => (
    <svg style={style} width={width} height={height} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <circle cx="12" cy="12" r="12" fill="#E6E1F7" />
      <g clipPath="url(#clip0_21644_73698)">
        <path
          d="M11.3072 14.1444C11.3072 14.2949 11.4292 14.4171 11.5799 14.4171H12.9435C13.0941 14.4171 13.2162 14.2949 13.2162 14.1444V13.1863L18.2617 12.04V16.6075C18.2617 16.9088 18.0175 17.153 17.7163 17.153H6.80717C6.50593 17.153 6.26172 16.9088 6.26172 16.6075V12.0447L11.3072 13.187V14.1444Z"
          fill="#5C33F3"
        />
        <path
          d="M6.80717 8.97168H17.7163C18.0175 8.97168 18.2617 9.2159 18.2617 9.51713V11.3415L13.2163 12.4877V11.9631C13.2163 11.8125 13.0941 11.6904 12.9435 11.6904H11.5799C11.4292 11.6904 11.3072 11.8125 11.3072 11.9631V12.4884L6.26172 11.346V9.51713C6.26172 9.2159 6.50595 8.97168 6.80717 8.97168Z"
          fill="#5C33F3"
        />
        <path
          d="M14.3663 7.70547C14.3663 7.29802 14.0349 6.96651 13.6275 6.96651H10.896C10.4886 6.96651 10.1571 7.298 10.1571 7.70547V8.42588H9.51575V7.70547C9.51575 6.94429 10.1349 6.3252 10.896 6.3252H13.6275C14.3886 6.3252 15.0078 6.94429 15.0078 7.70547V8.42588H14.3664L14.3663 7.70547Z"
          fill="#5C33F3"
        />
      </g>
      <defs>
        <clipPath id="clip0_21644_73698">
          <rect width="12" height="12" fill="white" transform="matrix(-1 0 0 1 18.2617 5.73926)" />
        </clipPath>
      </defs>
    </svg>
  ),
  circledCheck: ({ height = 18, width = 19, name, fill = '#24B314' }) => (
    <svg width={width} height={height} viewBox="0 0 18 19" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M9 1.30078C7.31886 1.30078 5.67547 1.7993 4.27766 2.73329C2.87984 3.66728 1.79037 4.9948 1.14703 6.54797C0.503683 8.10114 0.335355 9.81021 0.663329 11.459C0.991303 13.1079 1.80085 14.6224 2.9896 15.8112C4.17834 16.9999 5.6929 17.8095 7.34173 18.1375C8.99057 18.4654 10.6996 18.2971 12.2528 17.6538C13.806 17.0104 15.1335 15.9209 16.0675 14.5231C17.0015 13.1253 17.5 11.4819 17.5 9.80078C17.4976 7.54717 16.6013 5.38654 15.0078 3.793C13.4142 2.19946 11.2536 1.30316 9 1.30078ZM12.7318 8.30184L8.15491 12.8788C8.09418 12.9396 8.02207 12.9878 7.94269 13.0207C7.86332 13.0536 7.77824 13.0705 7.69231 13.0705C7.60638 13.0705 7.5213 13.0536 7.44193 13.0207C7.36255 12.9878 7.29044 12.9396 7.22971 12.8788L5.26818 10.9172C5.14549 10.7945 5.07656 10.6281 5.07656 10.4546C5.07656 10.2811 5.14549 10.1147 5.26818 9.99203C5.39086 9.86934 5.55726 9.80042 5.73077 9.80042C5.90428 9.80042 6.07068 9.86934 6.19337 9.99203L7.69231 11.4918L11.8066 7.37665C11.8674 7.3159 11.9395 7.26771 12.0189 7.23483C12.0982 7.20195 12.1833 7.18503 12.2692 7.18503C12.3551 7.18503 12.4402 7.20195 12.5196 7.23483C12.599 7.26771 12.6711 7.3159 12.7318 7.37665C12.7926 7.43739 12.8408 7.50951 12.8736 7.58889C12.9065 7.66826 12.9234 7.75333 12.9234 7.83924C12.9234 7.92515 12.9065 8.01022 12.8736 8.0896C12.8408 8.16897 12.7926 8.24109 12.7318 8.30184Z"
        fill={fill}
      />
    </svg>
  ),
  circledClose: ({ height = 18, width = 19, name, fill = '#F32345' }) => (
    <svg width={width} height={height} viewBox="0 0 18 19" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M9 1.30078C7.31886 1.30078 5.67547 1.7993 4.27766 2.73329C2.87984 3.66728 1.79037 4.9948 1.14703 6.54797C0.503683 8.10114 0.335355 9.81021 0.663329 11.459C0.991303 13.1079 1.80085 14.6224 2.9896 15.8112C4.17834 16.9999 5.6929 17.8095 7.34173 18.1375C8.99057 18.4654 10.6996 18.2971 12.2528 17.6538C13.806 17.0104 15.1335 15.9209 16.0675 14.5231C17.0015 13.1253 17.5 11.4819 17.5 9.80078C17.4976 7.54717 16.6013 5.38654 15.0078 3.793C13.4142 2.19946 11.2536 1.30316 9 1.30078ZM12.078 11.9536C12.1387 12.0143 12.1869 12.0864 12.2198 12.1658C12.2527 12.2452 12.2696 12.3303 12.2696 12.4162C12.2696 12.5021 12.2527 12.5871 12.2198 12.6665C12.1869 12.7459 12.1387 12.818 12.078 12.8788C12.0172 12.9395 11.9451 12.9877 11.8657 13.0206C11.7864 13.0535 11.7013 13.0704 11.6154 13.0704C11.5295 13.0704 11.4444 13.0535 11.365 13.0206C11.2857 12.9877 11.2135 12.9395 11.1528 12.8788L9 10.7252L6.84721 12.8788C6.78647 12.9395 6.71435 12.9877 6.63497 13.0206C6.5556 13.0535 6.47053 13.0704 6.38462 13.0704C6.29871 13.0704 6.21363 13.0535 6.13426 13.0206C6.05489 12.9877 5.98277 12.9395 5.92202 12.8788C5.86127 12.818 5.81308 12.7459 5.78021 12.6665C5.74733 12.5871 5.73041 12.5021 5.73041 12.4162C5.73041 12.3303 5.74733 12.2452 5.78021 12.1658C5.81308 12.0864 5.86127 12.0143 5.92202 11.9536L8.07563 9.80078L5.92202 7.64799C5.79933 7.5253 5.73041 7.3589 5.73041 7.1854C5.73041 7.01189 5.79933 6.84549 5.92202 6.7228C6.04471 6.60011 6.21111 6.53119 6.38462 6.53119C6.55813 6.53119 6.72453 6.60011 6.84721 6.7228L9 8.8764L11.1528 6.7228C11.2135 6.66205 11.2857 6.61386 11.365 6.58098C11.4444 6.54811 11.5295 6.53119 11.6154 6.53119C11.7013 6.53119 11.7864 6.54811 11.8657 6.58098C11.9451 6.61386 12.0172 6.66205 12.078 6.7228C12.1387 6.78355 12.1869 6.85567 12.2198 6.93504C12.2527 7.01441 12.2696 7.09948 12.2696 7.1854C12.2696 7.27131 12.2527 7.35638 12.2198 7.43575C12.1869 7.51512 12.1387 7.58724 12.078 7.64799L9.92438 9.80078L12.078 11.9536Z"
        fill={fill}
      />
    </svg>
  ),

  save: ({ stroke = 'white', strokeWidth = '2', ...props }) => (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
      <path
        d="M19 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V5C3 4.46957 3.21071 3.96086 3.58579 3.58579C3.96086 3.21071 4.46957 3 5 3H16L21 8V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21Z"
        stroke={stroke}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path d="M17 21V13H7V21" stroke={stroke} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M7 3V8H15" stroke={stroke} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    </svg>
  ),
  lightBulb: ({ name, ...props }) => (
    <svg data-testid={name} width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
      <g clipPath="url(#clip0_26048_90926)">
        <path
          d="M12.6059 14.7826H7.3885C7.02847 14.7826 6.73632 14.4907 6.73632 14.1304C6.73632 13.2313 6.35374 11.9686 5.71389 11.4122C3.96077 9.88899 3.16589 7.60445 3.58757 5.30145C4.14889 2.23254 6.84206 0.00296875 9.99167 0C9.99335 0 9.9955 0 9.99761 0C11.7384 0 13.3753 0.677422 14.607 1.90813C15.84 3.14031 16.5189 4.77879 16.5189 6.52172C16.5189 8.39547 15.7016 10.1794 14.2767 11.4158C13.6389 11.969 13.2581 13.2305 13.2581 14.1304C13.2581 14.4907 12.9659 14.7826 12.6059 14.7826Z"
          fill="#FFD15D"
        />
        <path
          d="M9.99594 14.7835C9.6359 14.7835 9.34375 14.4916 9.34375 14.1313V8.91391C9.34375 8.55363 9.6359 8.26172 9.99594 8.26172C10.356 8.26172 10.6481 8.55363 10.6481 8.91391V14.1313C10.6481 14.4916 10.356 14.7835 9.99594 14.7835Z"
          fill="#FF9F19"
        />
        <path
          d="M6.15322 6.42332C6.11416 6.42332 6.07467 6.41972 6.03517 6.4125C5.68064 6.34777 5.44584 6.00785 5.5108 5.65355C5.84705 3.81531 7.32338 2.35808 9.18435 2.02753C9.54017 1.96531 9.87771 2.20117 9.94057 2.5557C10.0038 2.91023 9.76736 3.24882 9.41279 3.31191C8.0851 3.54757 7.03252 4.58293 6.79392 5.88836C6.73623 6.203 6.46189 6.42332 6.15322 6.42332Z"
          fill="#F2FAFF"
        />
        <path
          d="M11.3 16.0879H8.69125C8.33039 16.0879 8.03906 16.3792 8.03906 16.7401V18.0444C8.03906 19.1227 8.9173 20.0009 9.99559 20.0009C11.0043 20.0009 11.8347 19.2357 11.9434 18.2531C11.9477 18.1836 11.9521 18.114 11.9521 18.0444V16.7401C11.9521 16.3792 11.6608 16.0879 11.3 16.0879Z"
          fill="#485566"
        />
        <path
          d="M12.6082 14.7826C12.9683 14.7826 13.2604 14.4907 13.2604 14.1304C13.2604 13.2305 13.6413 11.969 14.279 11.4158C15.7039 10.1793 16.5213 8.39543 16.5213 6.52172C16.5213 4.77875 15.8424 3.14027 14.6093 1.90813C13.3776 0.677422 11.7408 0 10 0V14.7826H12.6082Z"
          fill="#F9B54C"
        />
        <path
          d="M11.9565 16.7381V18.0425C11.9565 18.112 11.9522 18.1816 11.9478 18.2512C11.8391 19.2338 11.0087 19.999 10 19.999V16.0859H11.3043C11.6652 16.0859 11.9565 16.3772 11.9565 16.7381Z"
          fill="#36404D"
        />
        <path
          d="M10 14.7834C10.3599 14.7832 10.6518 14.4914 10.6518 14.1313V8.91387C10.6518 8.55375 10.3599 8.26195 10 8.26172V14.7834Z"
          fill="#F28618"
        />
        <path
          d="M11.2996 17.3917H8.6909C7.61035 17.3917 6.73438 16.5158 6.73438 15.4352V14.1309H13.2561V15.4352C13.2561 16.5158 12.3802 17.3917 11.2996 17.3917Z"
          fill="#576573"
        />
        <path d="M13.2604 15.4352V14.1309H10V17.3917H11.3039C12.3845 17.3917 13.2604 16.5158 13.2604 15.4352Z" fill="#485566" />
        <path
          d="M11.9434 7.40453C11.8391 6.42191 11.0043 5.65234 9.99559 5.65234C8.91734 5.65234 8.03906 6.53059 8.03906 7.60887C8.03906 8.68715 8.9173 9.56539 9.99559 9.56539C11.0739 9.56539 11.9521 8.68715 11.9521 7.60887C11.9521 7.53934 11.9477 7.4741 11.9434 7.40453ZM9.99555 8.26105C9.63469 8.26105 9.34336 7.96976 9.34336 7.60887C9.34336 7.24801 9.63465 6.95668 9.99555 6.95668C10.3564 6.95668 10.6477 7.24797 10.6477 7.60887C10.6477 7.96976 10.3564 8.26105 9.99555 8.26105Z"
          fill="#FF9F19"
        />
        <path
          d="M11.9565 7.60887C11.9565 8.68711 11.0783 9.56539 10 9.56539V8.26105C10.3609 8.26105 10.6522 7.96977 10.6522 7.60887C10.6522 7.24801 10.3609 6.95668 10 6.95668V5.65234C11.0087 5.65234 11.8435 6.42191 11.9478 7.40453C11.9522 7.47406 11.9565 7.5393 11.9565 7.60887Z"
          fill="#F28618"
        />
      </g>
      <defs>
        <clipPath id="clip0_21644_73614">
          <rect width="20" height="20" fill="white" transform="translate(0 0.5)" />
        </clipPath>
      </defs>
    </svg>
  ),
  arrowDownSvg: ({ style = {}, width = 28, height = 24, fill = 'none' }) => (
    <svg style={style} width={width} height={height} viewBox="0 0 14 11" fill={fill} xmlns="http://www.w3.org/2000/svg">
      <path
        d="M6.4522 10.5457C6.51336 10.6345 6.5952 10.7071 6.69066 10.7573C6.78613 10.8075 6.89236 10.8337 7.0002 10.8337C7.10805 10.8337 7.21428 10.8075 7.30974 10.7573C7.40521 10.7071 7.48704 10.6345 7.5482 10.5457L13.5482 1.87901C13.6177 1.77905 13.6584 1.66196 13.666 1.54048C13.6735 1.41899 13.6477 1.29776 13.5912 1.18994C13.5347 1.08212 13.4498 0.99184 13.3456 0.928913C13.2414 0.865987 13.1219 0.832817 13.0002 0.833009H1.0002C0.878764 0.83351 0.759761 0.867106 0.655989 0.930184C0.552217 0.993261 0.467604 1.08343 0.411248 1.191C0.354892 1.29857 0.328925 1.41947 0.336141 1.5407C0.343357 1.66192 0.383483 1.77888 0.452202 1.87901L6.4522 10.5457Z"
        fill="black"
      />
    </svg>
  ),
  infoQuestionMark: ({ style = {}, width = 18, height = 18, fill = 'none', className }) => (
    <svg
      style={style}
      width={width}
      height={height}
      viewBox="0 0 18 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d="M8.99983 17.3337C4.39734 17.3337 0.666504 13.6028 0.666504 9.00032C0.666504 4.39782 4.39734 0.666992 8.99983 0.666992C13.6023 0.666992 17.3332 4.39782 17.3332 9.00032C17.3332 13.6028 13.6023 17.3337 8.99983 17.3337ZM5.59984 6.69532V6.78032C5.59984 6.89083 5.64373 6.99681 5.72187 7.07495C5.80001 7.15309 5.90599 7.19699 6.0165 7.19699H6.84733C6.90194 7.19699 6.95602 7.18623 7.00647 7.16534C7.05692 7.14444 7.10276 7.11381 7.14137 7.07519C7.17999 7.03658 7.21062 6.99074 7.23151 6.94029C7.25241 6.88984 7.26317 6.83576 7.26317 6.78116C7.26317 5.70616 8.09483 5.13199 9.21317 5.13199C10.3032 5.13199 11.0198 5.70616 11.0198 6.52366C11.0198 7.29699 10.6182 7.65616 9.61483 8.11532L9.32817 8.24366C8.48233 8.61699 8.1665 9.17532 8.1665 10.1512V10.2503C8.1665 10.3608 8.2104 10.4668 8.28854 10.5449C8.36668 10.6231 8.47266 10.667 8.58317 10.667H9.414C9.46861 10.667 9.52268 10.6562 9.57313 10.6353C9.62358 10.6144 9.66943 10.5838 9.70804 10.5452C9.74665 10.5066 9.77728 10.4607 9.79818 10.4103C9.81908 10.3598 9.82983 10.3058 9.82983 10.2512C9.82983 9.82116 9.94483 9.64865 10.2882 9.49116L10.5757 9.36199C11.7798 8.81699 12.6832 8.10032 12.6832 6.53699V6.45116C12.6832 4.80282 11.2498 3.58366 9.21317 3.58366C7.14817 3.58366 5.59984 4.77366 5.59984 6.69532ZM7.74983 13.1595C7.74983 13.882 8.299 14.417 8.99233 14.417C9.70067 14.417 10.2498 13.882 10.2498 13.1595C10.2498 12.437 9.70067 11.917 8.99233 11.917C8.299 11.917 7.74983 12.437 7.74983 13.1595Z"
        fill="#94A7B7"
      />
    </svg>
  ),
  warningThreeStroke: ({
    height = 27,
    width = 28,
    name,
    fill = 'white',
    stroke = '#FFB447',
    strokeWidth = '2',
    strokeLinecap = 'round' as const,
    strokeLinejoin = 'round' as const
  }) => (
    <svg data-testid={name} width={width} height={height} viewBox="0 0 28 27" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g clipPath="url(#clip0_24879_112550)">
        <path
          d="M13.5781 26C20.4472 26 26.0156 20.4315 26.0156 13.5625C26.0156 6.69346 20.4472 1.125 13.5781 1.125C6.70908 1.125 1.14062 6.69346 1.14062 13.5625C1.14062 20.4315 6.70908 26 13.5781 26Z"
          stroke={stroke}
          strokeWidth={strokeWidth}
          strokeLinecap={strokeLinecap}
          strokeLinejoin={strokeLinejoin}
        />
        <path
          d="M13.5781 18.5375V13.5625"
          stroke={stroke}
          strokeWidth={strokeWidth}
          strokeLinecap={strokeLinecap}
          strokeLinejoin={strokeLinejoin}
        />
        <path
          d="M13.5781 8.5874H13.5905"
          stroke={stroke}
          strokeWidth={strokeWidth}
          strokeLinecap={strokeLinecap}
          strokeLinejoin={strokeLinejoin}
        />
      </g>{' '}
      <defs>
        <clipPath id="clip0_24879_112550">
          <rect width="28" height="27" fill={fill} />
        </clipPath>
      </defs>
    </svg>
  ),
  outlineCopy: ({ height = 16, width = 16, name, stroke = '#2376F3', strokeWidth = '2' }) => (
    <svg data-testid={name} width={width} height={height} viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M8.66699 1.33203H4.00033C3.6467 1.33203 3.30756 1.47251 3.05752 1.72256C2.80747 1.9726 2.66699 2.31174 2.66699 2.66536V13.332C2.66699 13.6857 2.80747 14.0248 3.05752 14.2748C3.30756 14.5249 3.6467 14.6654 4.00033 14.6654H12.0003C12.3539 14.6654 12.6931 14.5249 12.9431 14.2748C13.1932 14.0248 13.3337 13.6857 13.3337 13.332V5.9987L8.66699 1.33203Z"
        stroke={stroke}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path d="M8.66699 1.33203V5.9987H13.3337" stroke={stroke} strokeWidth={strokeWidth} strokeLinecap="round" strokeLinejoin="round" />
    </svg>
  ),
  outlineDownload: ({ height = 16, width = 16, name, stroke = '#2376F3', strokeWidth = '2' }) => (
    <svg data-testid={name} width={width} height={height} viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M14 10V12.6667C14 13.0203 13.8595 13.3594 13.6095 13.6095C13.3594 13.8595 13.0203 14 12.6667 14H3.33333C2.97971 14 2.64057 13.8595 2.39052 13.6095C2.14048 13.3594 2 13.0203 2 12.6667V10"
        stroke={stroke}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M4.66699 6.66797L8.00033 10.0013L11.3337 6.66797"
        stroke={stroke}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path d="M8 10V2" stroke={stroke} strokeWidth={strokeWidth} strokeLinecap="round" strokeLinejoin="round" />
    </svg>
  ),
  medal: ({ height = 16, width = 16, fill = '#2376F3', name = '' }) => (
    <svg data-testid={name} width={width} height={height} viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g clipPath="url(#clip0_27873_78349)">
        <path
          d="M7.81106 12.0273C7.44503 11.959 7.16209 11.6943 6.92806 11.4757C6.80434 11.3607 6.63506 11.2024 6.54775 11.1794C6.53638 11.176 6.51537 11.1727 6.47875 11.1727C6.36375 11.1727 6.20141 11.21 6.04441 11.2457C5.83806 11.293 5.62413 11.3419 5.40572 11.3419C5.19981 11.3419 5.01803 11.2966 4.86303 11.2073C4.81413 11.1787 4.76878 11.147 4.72772 11.1133L3.14844 14.524L5.11137 14.4653L6.33606 16.0006L7.99306 12.4211L8.17575 12.0273C8.11703 12.039 8.05606 12.045 7.99306 12.045C7.93031 12.0449 7.86934 12.0389 7.81106 12.0273Z"
          fill={fill}
        />
        <path
          d="M11.1274 11.2056C10.9725 11.2956 10.7898 11.3409 10.5848 11.3409C10.5848 11.3409 10.5848 11.3409 10.5841 11.3409C10.3655 11.3409 10.1524 11.292 9.94609 11.2446C9.78909 11.2089 9.62675 11.1716 9.51084 11.1716C9.47509 11.1716 9.45344 11.175 9.44184 11.1783C9.35475 11.2013 9.18547 11.3596 9.06241 11.4747C9.00416 11.5289 8.94319 11.5859 8.87909 11.6419L8.25781 12.9836L9.65378 15.9995L10.8791 14.4642L12.8421 14.5229L11.2621 11.1113C11.2208 11.146 11.1761 11.177 11.1274 11.2056Z"
          fill={fill}
        />
        <path
          d="M7.99794 2.53711C6.20425 2.53711 4.75 3.99136 4.75 5.78505C4.75 7.57873 6.20422 9.03298 7.99794 9.03298C9.79159 9.03298 11.2458 7.57873 11.2458 5.78505C11.2458 3.99136 9.79159 2.53711 7.99794 2.53711Z"
          fill={fill}
        />
        <path
          d="M12.9324 7.10819C13.0528 6.65819 13.7817 6.27319 13.7817 5.78525C13.7817 5.29753 13.0528 4.91253 12.9324 4.46256C12.808 3.99694 13.243 3.29928 13.0075 2.89197C12.7681 2.47859 11.9445 2.50831 11.6088 2.17266C11.2732 1.83766 11.3031 1.01362 10.8895 0.774031C10.4824 0.538656 9.78453 0.973656 9.31916 0.849281C8.86981 0.729375 8.48416 0 7.99619 0C7.50913 0 7.12347 0.729375 6.67347 0.849281C6.20853 0.973656 5.51087 0.538656 5.10291 0.774031C4.69022 1.01366 4.71922 1.83766 4.38425 2.17266C4.04884 2.50831 3.22456 2.47859 2.98563 2.89197C2.74959 3.29928 3.18525 3.99694 3.06088 4.46256C2.94028 4.91253 2.21094 5.29753 2.21094 5.78525C2.21094 6.27319 2.94028 6.65819 3.06088 7.10819C3.18525 7.57378 2.74959 8.27144 2.98563 8.67878C3.22456 9.09216 4.04884 9.06222 4.38425 9.39784C4.53856 9.55216 4.61519 9.80941 4.69353 10.0651C4.75988 10.2831 4.82753 10.5001 4.94391 10.6524C4.98856 10.7114 5.04081 10.7607 5.10291 10.7965C5.51022 11.0321 6.20853 10.5971 6.67347 10.7215C6.96109 10.798 7.22216 11.1228 7.50022 11.3481C7.65722 11.4752 7.82047 11.5707 7.99619 11.5707C8.17216 11.5707 8.33519 11.4752 8.49284 11.3481C8.76572 11.1275 9.02209 10.8108 9.30213 10.7268C9.3075 10.7248 9.31375 10.7228 9.31912 10.7215C9.7845 10.5971 10.4824 11.0321 10.8895 10.7965C10.9518 10.7608 11.0048 10.7108 11.0494 10.6518C11.3024 10.3195 11.324 9.68216 11.6088 9.39787C11.9444 9.06222 12.768 9.09216 13.0074 8.67881C13.243 8.27144 12.808 7.57378 12.9324 7.10819ZM7.99619 9.64216C7.92116 9.64216 7.84681 9.64016 7.77309 9.63547C6.84522 9.58278 6.00553 9.20247 5.36819 8.60753C4.61159 7.90319 4.13928 6.89984 4.13928 5.78525C4.13928 3.65525 5.86619 1.92834 7.99619 1.92834C10.1264 1.92834 11.8531 3.65528 11.8531 5.78525C11.8531 7.91547 10.1264 9.64216 7.99619 9.64216Z"
          fill={fill}
        />
      </g>
      <defs>
        <clipPath id="clip0_27873_78349">
          <rect width={width} height={height} fill="white" />
        </clipPath>
      </defs>
    </svg>
  ),
  unlockedPadlock: ({ width = 24, height = 24, fill = 'none', name = 'unlockedPadlock' }) => (
    <svg data-testid={name} width={width} height={height} viewBox="0 0 24 24" fill={fill} xmlns="http://www.w3.org/2000/svg">
      <path
        d="M19 11H5C3.89543 11 3 11.8954 3 13V20C3 21.1046 3.89543 22 5 22H19C20.1046 22 21 21.1046 21 20V13C21 11.8954 20.1046 11 19 11Z"
        stroke="white"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M7 10.9999V6.99991C6.99876 5.75996 7.45828 4.56378 8.28938 3.64358C9.12047 2.72338 10.2638 2.14481 11.4975 2.0202C12.7312 1.89558 13.9671 2.23381 14.9655 2.96922C15.9638 3.70463 16.6533 4.78476 16.9 5.99991"
        stroke="white"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  ),
  lockedPadlock: ({ width = 24, height = 24, fill = 'none', name = 'lockedPadlock' }) => (
    <svg data-testid={name} width={width} height={height} viewBox="0 0 24 24" fill={fill} xmlns="http://www.w3.org/2000/svg">
      <path
        d="M19 11H5C3.89543 11 3 11.8954 3 13V20C3 21.1046 3.89543 22 5 22H19C20.1046 22 21 21.1046 21 20V13C21 11.8954 20.1046 11 19 11Z"
        stroke="white"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M7 11V7C7 5.67392 7.52678 4.40215 8.46447 3.46447C9.40215 2.52678 10.6739 2 12 2C13.3261 2 14.5979 2.52678 15.5355 3.46447C16.4732 4.40215 17 5.67392 17 7V11"
        stroke="white"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  ),
  envelope: ({ width = 20, height = 17, fill = 'none', name = 'envelope' }) => (
    <svg data-testid={name} width={width} height={height} viewBox="0 0 20 17" fill={fill} xmlns="http://www.w3.org/2000/svg">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M3.96802 0.799805H16.032C16.4706 0.799795 16.8491 0.799785 17.1624 0.821155C17.4922 0.843665 17.8221 0.893165 18.1481 1.02817C18.8831 1.33265 19.4672 1.91668 19.7716 2.65176C19.9066 2.97769 19.9561 3.30759 19.9787 3.63742C20 3.95068 20 4.32916 20 4.76781V12.8318C20 13.2704 20 13.6489 19.9787 13.9622C19.9561 14.292 19.9066 14.6219 19.7716 14.9479C19.4672 15.6829 18.8831 16.267 18.1481 16.5714C17.8221 16.7064 17.4922 16.7559 17.1624 16.7785C16.8491 16.7998 16.4706 16.7998 16.032 16.7998H3.96801C3.52936 16.7998 3.15088 16.7998 2.83762 16.7785C2.50779 16.7559 2.17788 16.7064 1.85195 16.5714C1.11687 16.267 0.53284 15.6829 0.22836 14.9479C0.0933604 14.6219 0.0438603 14.292 0.0213503 13.9622C-1.96602e-05 13.6489 -9.56733e-06 13.2704 4.32668e-07 12.8318V4.76782C-9.56733e-06 4.32917 -1.96602e-05 3.95068 0.0213503 3.63742C0.0438603 3.30759 0.0933604 2.97769 0.22836 2.65176C0.53284 1.91668 1.11687 1.33265 1.85195 1.02817C2.17788 0.893165 2.50779 0.843665 2.83762 0.821155C3.15088 0.799785 3.52937 0.799795 3.96802 0.799805ZM2.31745 3.07757C2.68114 2.66195 3.3129 2.61983 3.72854 2.98351L9.3415 7.8948C9.7185 8.22471 10.2815 8.22471 10.6585 7.8948L16.2715 2.98351C16.6871 2.61983 17.3189 2.66195 17.6825 3.07757C18.0462 3.49322 18.0041 4.12497 17.5885 4.48865L11.9755 9.4C10.8444 10.3897 9.1556 10.3897 8.0245 9.4L2.41153 4.48865C1.99589 4.12497 1.95377 3.49322 2.31745 3.07757Z"
        fill="#FFB447"
      />
    </svg>
  ),
  circledCaution: ({ height = 20, width = 20, fill = '#FA9500', name = '' }) => (
    <svg data-testid={name} width={width} height={height} viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g clipPath="url(#clip0_32488_131852)">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M0 10C0 4.47715 4.47715 0 10 0C15.5228 0 20 4.47715 20 10C20 15.5228 15.5228 20 10 20C4.47715 20 0 15.5228 0 10ZM8.40909 9.09091C8.40909 8.58882 8.81609 8.18182 9.31818 8.18182H10.6818C11.1839 8.18182 11.5909 8.58882 11.5909 9.09091V15.4545C11.5909 15.9566 11.1839 16.3636 10.6818 16.3636H9.31818C8.81609 16.3636 8.40909 15.9566 8.40909 15.4545V9.09091ZM11.8182 5.45455C11.8182 4.45039 11.0042 3.63636 10 3.63636C8.99582 3.63636 8.18182 4.45039 8.18182 5.45455C8.18182 6.4587 8.99582 7.27273 10 7.27273C11.0042 7.27273 11.8182 6.4587 11.8182 5.45455Z"
          fill={fill}
        />
      </g>
      <defs>
        <clipPath id="clip0_32488_131852">
          <rect width="20" height="20" fill="white" />
        </clipPath>
      </defs>
    </svg>
  ),
  editPen2: ({ height = 16, width = 16, ...props }) => (
    <svg xmlns="http://www.w3.org/2000/svg" width={width} height={height} fill="none" {...props}>
      <path
        stroke="#2376F3"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={1.5}
        d="M11.332 2.002a1.886 1.886 0 0 1 2.667 2.666l-9 9-3.667 1 1-3.666 9-9Z"
      />
    </svg>
  ),
  clock: props => (
    <svg viewBox="0 0 18 18" xmlns="http://www.w3.org/2000/svg" width={18} height={18} fill="none" {...props}>
      <g stroke="#FA9500" strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} clipPath="url(#a)">
        <path d="M9 16.5a7.5 7.5 0 1 0 0-15 7.5 7.5 0 0 0 0 15Z" />
        <path d="M9 4.5V9l3 1.5" />
      </g>
      <defs>
        <clipPath id="a">
          <path fill="#fff" d="M0 0h18v18H0z" />
        </clipPath>
      </defs>
    </svg>
  ),
  merchantIconCircled: () => (
    <svg width="30" height="30" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect width="40" height="40" rx="20" fill="#DDE2EC" />
      <g clipPath="url(#clip0_40198_196266)">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M20.9515 11.458C21.9518 11.458 22.7713 12.232 22.8438 13.2138L22.849 13.3554V26.6375H23.7977V17.6247C23.7977 17.3918 23.9655 17.1981 24.1868 17.158L24.272 17.1503H25.6951C26.6954 17.1503 27.5149 17.9244 27.5873 18.9061L27.5925 19.0477V26.6375H28.5413C29.0652 26.6375 29.49 27.0622 29.49 27.5862C29.49 28.0728 29.1238 28.4737 28.6519 28.5285L28.5413 28.5349H11.4643C10.9404 28.5349 10.5156 28.1102 10.5156 27.5862C10.5156 27.0997 10.8819 26.6987 11.3537 26.6439L11.4643 26.6375H12.4131V13.3554C12.4131 12.3552 13.1871 11.5356 14.1689 11.4632L14.3105 11.458H20.9515ZM19.0541 22.8426H16.2079V24.7401H19.0541V22.8426ZM19.0541 19.0477H16.2079V20.9452H19.0541V19.0477ZM19.0541 15.2529H16.2079V17.1503H19.0541V15.2529Z"
          fill="white"
        />
      </g>
      <defs>
        <clipPath id="clip0_40198_196266">
          <rect width="22.7692" height="22.7692" fill="white" transform="translate(8.61719 8.61426)" />
        </clipPath>
      </defs>
    </svg>
  ),
  hamburgerArrowUp: ({ height = 24, width = 24, fill = '#2376F3' }) => (
    <svg data-testid={name} width={width} height={height} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M2.25 6C2.25 5.58579 2.58579 5.25 3 5.25H20C20.4142 5.25 20.75 5.58579 20.75 6C20.75 6.41421 20.4142 6.75 20 6.75H3C2.58579 6.75 2.25 6.41421 2.25 6ZM2.25 11C2.25 10.5858 2.58579 10.25 3 10.25H11C11.4142 10.25 11.75 10.5858 11.75 11C11.75 11.4142 11.4142 11.75 11 11.75H3C2.58579 11.75 2.25 11.4142 2.25 11ZM2.25 16C2.25 15.5858 2.58579 15.25 3 15.25H12C12.4142 15.25 12.75 15.5858 12.75 16C12.75 16.4142 12.4142 16.75 12 16.75H3C2.58579 16.75 2.25 16.4142 2.25 16Z"
        fill={fill}
      />
      <path
        d="M16.9697 8.46967C17.2626 8.17678 17.7374 8.17678 18.0303 8.46967L20.5303 10.9697C20.8232 11.2626 20.8232 11.7374 20.5303 12.0303C20.2374 12.3232 19.7626 12.3232 19.4697 12.0303L18.25 10.8107V17C18.25 17.4142 17.9142 17.75 17.5 17.75C17.0858 17.75 16.75 17.4142 16.75 17V10.8107L15.5303 12.0303C15.2374 12.3232 14.7626 12.3232 14.4697 12.0303C14.1768 11.7374 14.1768 11.2626 14.4697 10.9697L16.9697 8.46967Z"
        fill={fill}
      />
    </svg>
  )
};

const Icon = (props: TIcon & { name: TIconNames }) => icons[props.name]?.(props) ?? null;
export default Icon;

