/* eslint-disable react/jsx-props-no-spreading */
import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { axe } from 'jest-axe';
import { vi } from 'vitest';

import FileUploader from '../FileUploader';

const initialProps = {
  text: 'Browse file to upload',
  maxFileSizeMb: 10,
  infoText: `Max file size 10 MB`,
  uploaded: false,
  uploading: false,
  onChange: vi.fn(),
  clearUploads: vi.fn(),
  fileName: ''
};

const renderComponent = (props = {}) => ({
  user: userEvent,
  ...render(<FileUploader {...initialProps} {...props} />)
});

describe('file uploader', () => {
  it('renders', () => {
    renderComponent();

    expect(screen.getByText(/browse file to upload/i)).toBeInTheDocument();
  });

  it('is accessible', async () => {
    const { container } = renderComponent();
    const result = await axe(container);

    expect(result).toHaveNoViolations();
  });

  it('can upload files', async () => {
    const file = new File(['hello'], 'hello.png', { type: 'image/png' });

    const { user } = renderComponent({ fileName: 'hello' });
    const input = screen.getByLabelText(/browse file to upload/i) as HTMLInputElement;
    await user.upload(input, file);

    expect(input?.files?.[0]).toStrictEqual(file);
    expect(input?.files?.item(0)).toStrictEqual(file);
    expect(input.files).toHaveLength(1);
  });
});
