import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { axe, toHaveNoViolations } from 'jest-axe';
import { expect, vi } from 'vitest';

import EditProfileForm from '../EditProfileForm';

expect.extend(toHaveNoViolations);

describe('EditProfileForm', () => {
  const mockOnSubmit = vi.fn();
  const initialValues = {
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>'
  };

  it('renders form fields with initial values', () => {
    render(<EditProfileForm initialValues={initialValues} onSubmit={mockOnSubmit} />);
    expect(screen.getByLabelText(/First Name/i)).toHaveValue('John');
    expect(screen.getByLabelText(/Last Name/i)).toHaveValue('Doe');
    expect(screen.getByLabelText(/Email Address/i)).toHaveValue('<EMAIL>');
  });

  it('shows validation errors for empty fields', async () => {
    render(<EditProfileForm initialValues={{ firstName: '', lastName: '', email: '' }} onSubmit={mockOnSubmit} />);
    await userEvent.click(screen.getByLabelText(/First Name/i));
    await userEvent.tab();
    await userEvent.click(screen.getByLabelText(/Last Name/i));
    await userEvent.tab();
    await userEvent.click(screen.getByLabelText(/Email Address/i));
    await userEvent.tab();
    expect(await screen.findByText(/First name is required/i)).toBeInTheDocument();
    expect(await screen.findByText(/Last name is required/i)).toBeInTheDocument();
    expect(await screen.findByText(/Email is required/i)).toBeInTheDocument();
  });

  it('shows invalid email format error', async () => {
    render(<EditProfileForm initialValues={{ ...initialValues, email: 'invalid-email' }} onSubmit={mockOnSubmit} />);
    await userEvent.click(screen.getByLabelText(/Email Address/i));
    await userEvent.tab();
    expect(await screen.findByText(/Invalid email format/i)).toBeInTheDocument();
  });

  it('calls onSubmit with correct values on change', async () => {
    render(<EditProfileForm initialValues={initialValues} onSubmit={mockOnSubmit} />);
    await userEvent.clear(screen.getByLabelText(/First Name/i));
    await userEvent.type(screen.getByLabelText(/First Name/i), 'Jane');
    await userEvent.clear(screen.getByLabelText(/Last Name/i));
    await userEvent.type(screen.getByLabelText(/Last Name/i), 'Smith');
    await userEvent.clear(screen.getByLabelText(/Email Address/i));
    await userEvent.type(screen.getByLabelText(/Email Address/i), '<EMAIL>');
    expect(mockOnSubmit).toHaveBeenCalledWith({ firstName: 'Jane', lastName: 'Doe', email: '<EMAIL>' });
    expect(mockOnSubmit).toHaveBeenCalledWith({ firstName: 'Jane', lastName: 'Smith', email: '<EMAIL>' });
    expect(mockOnSubmit).toHaveBeenCalledWith({ firstName: 'Jane', lastName: 'Smith', email: '<EMAIL>' });
  });

  // Accessibility Tests
  it('should have no accessibility violations', async () => {
    const { container } = render(<EditProfileForm initialValues={initialValues} onSubmit={mockOnSubmit} />);

    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });

  it('should have properly associated labels with form controls', () => {
    render(<EditProfileForm initialValues={initialValues} onSubmit={mockOnSubmit} />);

    const firstNameInput = screen.getByLabelText(/First Name/i);
    const lastNameInput = screen.getByLabelText(/Last Name/i);
    const emailInput = screen.getByLabelText(/Email Address/i);

    expect(firstNameInput).toHaveAttribute('aria-required', 'true');
    expect(lastNameInput).toHaveAttribute('aria-required', 'true');
    expect(emailInput).toHaveAttribute('aria-required', 'true');
  });
});
