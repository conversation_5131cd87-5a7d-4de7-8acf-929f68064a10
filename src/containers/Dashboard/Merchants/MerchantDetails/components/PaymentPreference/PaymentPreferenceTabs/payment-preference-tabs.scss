.payment-preference-tabs {
  margin-top: 2rem;
  font-size: 1rem;

  .payment-preference-alert {
    background-color: #fff8e1;
    border-radius: 8px;
    display: flex;
    gap: 0.5rem;
    margin-bottom: 3rem;
    padding: 1rem;
    width: 100%;

    .action-info {
      display: flex;
      gap: 0.5rem;

      span {
        font-weight: 500;
      }

      p {
        margin: 0;
      }
    }
  }

  .tabs-wrapper {
    display: flex;
    gap: 4rem;
    width: 100%;

    ul {
      flex-basis: 22%;
      flex-shrink: 0;
    }

    > div:nth-child(2) {
      flex-basis: 75%;
    }
  }

  .vertical-menu {
    padding-inline-start: 0;
    width: 100%;

    li {
      color: #414f5f;
      font-size: 14px;
      list-style-type: none;

      button {
        all: unset;
        cursor: pointer;
        display: block;
        max-width: 300px;
        padding: 12px 15px;

        .highlight {
          background-color: red;
          border-radius: 50%;
          color: #fff;
          display: inline-grid;
          font-size: 12px;
          height: 16px;
          margin-left: 4px;
          place-items: center;
          width: 16px;
        }
      }
    }

    li:hover {
      color: #2376f3;
      font-weight: 500;
    }

    .active {
      color: #2376f3;
      background-color: #f1f6fa;
      border-radius: 8px;
      font-weight: 500;
    }
  }
}

.preference-dropdown-wrapper {
  background-color: #f9fbfd;
  border-radius: 8px;
  padding: 0 1rem;
  margin: 1rem 0;

  .preference-dropdown__btn {
    all: unset;

    h6 {
      margin: 0;
    }

    i {
      display: inline;
      transition: transform 0.5s ease;

      &.rotate-up {
        transform: rotate(180deg);
      }
    }
  }

  .preference-dropdown__item,
  .preference-dropdown__btn {
    align-items: center;
    border: 0 solid #dde2ec;
    border-bottom-width: 1px;
    display: flex;
    flex-wrap: wrap;
    font-size: 0.9rem;
    justify-content: space-between;
    padding: 1rem 0;
    width: 100%;
  }

  .preference-section {
    h6 {
      display: flex;
      flex-grow: 1;
      margin-right: 1rem;

      .header-wrapper {
        align-items: center;
        display: flex;
        flex-grow: 1;
        font-weight: 400;
        justify-content: space-between;

        .time-stamp {
          align-items: center;
          display: flex;
          gap: 0.75rem;

          .approved {
            border-right: 1px solid #80808039;
            font-weight: 500;
            padding-right: 0.75rem;
          }
        }

        .active-preference {
          background-color: #e4fff1;
          border-radius: 5px;
          color: #24b314;
          padding: 0.25rem 0.5rem;
        }
      }
    }

    .pref-history-summary {
      display: flex;
      flex-direction: column;
      gap: 1rem;
      margin-top: 0.5rem;
      padding: 0 0 1rem;

      .history-content-wrapper {
        background-color: #eaf2fe;
        border-radius: 10px;
        padding: 0.5rem 1rem;

        .preference-dropdown__item:first-of-type {
          border-top: 0;
        }
      }

      .history-attestation-wrapper {
        background-color: #eaf2fe;
        border-radius: 10px;
      }
    }
  }

  .attestation {
    align-items: center;
    border: 2px dashed #dde2ec;
    border-radius: 10px;
    display: flex;
    justify-content: space-between;
    padding: 0.75rem;
    width: 100%;

    div,
    a {
      display: flex;
      align-items: center;
    }
  }

  .preference-section:last-of-type {
    .preference-dropdown__btn:first-of-type {
      border: 0;
    }
    .preference-dropdown__item:first-of-type {
      border-top-width: 1px;
    }
    .preference-dropdown__item:last-of-type {
      border-bottom-width: 0;
    }
  }

  .preference-dropdown__item {
    p {
      all: unset;
      color: #414f5f;
      font-weight: 300;
    }

    .preference-status {
      align-items: center;
      display: flex;
      gap: 1rem;
    }
  }
}
