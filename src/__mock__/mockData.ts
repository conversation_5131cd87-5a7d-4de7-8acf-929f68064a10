import { IssuingMerchantDetailsResponseType } from '+types';

/* eslint-disable import/prefer-default-export */
export const mockPayOutData = {
  data: {
    data: [
      {
        reference: 'KPY-DD-LZ48fiZDUhUoYHDZ',
        status: 'success',
        currency: 'NGN',
        amount: '1000.00',
        fee: '25.00',
        vat: '1.88',
        amount_charged: '1026.88',
        amount_paid: '1000.00',
        channel: 'web',
        created_at: '2022-06-07T10:51:14.000Z',
        narration: 'test',
        message: 'Payout successful',
        trace_id: '***********',
        customer_name: 'Test Account',
        customer_email: null,
        bank_account: 'Test Account - **********',
        transaction_date: '2022-06-07 11:51:14',
        completed_at: '2022-06-07 11:54:42',
        merchant: '<PERSON> Bag',
        processor: 'quickteller',
        processor_reference: '***********',
        payment: {
          reference: 'KPY-PAY-Iz6xpSjuw1F6',
          customer: {
            id: 5883,
            name: 'Test Account',
            email: null
          },
          account: {
            id: 70,
            name: '<PERSON> Bag'
          }
        }
      },
      {
        reference: 'KPY-DD-C66zEvUMhnLW30IZ',
        status: 'requires_auth',
        currency: 'NGN',
        amount: '3000.00',
        fee: '75.00',
        vat: '5.63',
        amount_charged: '3080.63',
        amount_paid: '3000.00',
        channel: 'web',
        created_at: '2022-05-26T19:50:22.000Z',
        narration: 'Withdrawal from Merchant Balance',
        message: null,
        trace_id: null,
        customer_name: 'Test Account',
        customer_email: null,
        bank_account: 'Test Account - **********',
        transaction_date: '2022-05-26 20:50:22',
        completed_at: null,
        merchant: 'Tofunmi Soyemi',
        processor: null,
        processor_reference: null,
        payment: {
          reference: 'KPY-PAY-knTvZaqQlpxK',
          customer: {
            id: 5925,
            name: 'Test Account',
            email: null
          },
          account: {
            id: 90,
            name: 'Tofunmi Soyemi'
          }
        }
      },
      {
        reference: 'KPY-DD-s2pMv9YcDzafYNpG',
        status: 'requires_auth',
        currency: 'NGN',
        amount: '10000.00',
        fee: '9.30',
        vat: '0.70',
        amount_charged: '10010.00',
        amount_paid: '10000.00',
        channel: 'web',
        created_at: '2022-05-17T09:20:25.000Z',
        narration: 'food',
        message: null,
        trace_id: null,
        customer_name: 'Test Account',
        customer_email: null,
        bank_account: 'Test Account - **********',
        transaction_date: '2022-05-17 10:20:25',
        completed_at: null,
        merchant: 'damiworkTest',
        processor: null,
        processor_reference: null,
        payment: {
          reference: 'KPY-PAY-gHaCN88Y10Fi',
          customer: {
            id: 5895,
            name: 'Test Account',
            email: null
          },
          account: {
            id: 96,
            name: 'damiworkTest'
          }
        }
      },
      {
        reference: 'KPY-DD-ilIp2CuOu3Ce2Bef',
        status: 'requires_auth',
        currency: 'NGN',
        amount: '50000.00',
        fee: '0.00',
        vat: '0.00',
        amount_charged: '50000.00',
        amount_paid: '50000.00',
        channel: 'web',
        created_at: '2022-05-17T00:21:53.000Z',
        narration: 'Withdrawal from Merchant Balance',
        message: null,
        trace_id: null,
        customer_name: 'Test Account',
        customer_email: null,
        bank_account: 'Test Account - **********',
        transaction_date: '2022-05-17 01:21:53',
        completed_at: null,
        merchant: 'Timi Adesoji',
        processor: null,
        processor_reference: null,
        payment: {
          reference: 'KPY-PAY-wgMxdpB9VSsm',
          customer: {
            id: 5764,
            name: 'Test Account',
            email: null
          },
          account: {
            id: 25,
            name: 'Timi Adesoji'
          }
        }
      }
    ],
    paging: {
      total_items: 2832,
      page_size: 10,
      current: 1,
      count: 10,
      next: 2
    },
    links: [
      {
        href: 'https://api.koraapi.com/merchant/api/admin/transactions/payouts?page=1&limit=10&currency=NGN',
        rel: 'current',
        method: 'GET'
      },
      {
        href: 'https://api.koraapi.com/merchant/api/admin/transactions/payouts?page=2&limit=10&currency=NGN',
        rel: 'next',
        method: 'GET'
      }
    ]
  }
};

export const mockProcessorData = {
  data: {
    status: true,
    code: 'AA000',
    message: 'Successful',
    data: [
      {
        id: 1,
        name: 'Korapay',
        meta: null,
        slug: 'korapay',
        is_active: true,
        createdAt: '2019-09-26T07:01:57.000Z',
        updatedAt: '2019-09-26T07:01:57.000Z',
        deletedAt: null
      },
      {
        id: 2,
        name: 'Monnify',
        meta: null,
        slug: 'monnify',
        is_active: true,
        createdAt: '2019-09-26T07:01:57.000Z',
        updatedAt: '2019-09-26T07:01:57.000Z',
        deletedAt: null
      },
      {
        id: 3,
        name: 'Test',
        meta: null,
        slug: 'test',
        is_active: true,
        createdAt: '2019-09-26T07:01:57.000Z',
        updatedAt: '2019-09-26T07:01:57.000Z',
        deletedAt: null
      },
      {
        id: 4,
        name: 'Rave',
        meta: null,
        slug: 'rave',
        is_active: true,
        createdAt: '2019-09-26T07:01:57.000Z',
        updatedAt: '2019-09-26T07:01:57.000Z',
        deletedAt: null
      },
      {
        id: 5,
        name: 'UPSL',
        meta: null,
        slug: 'upsl_card',
        is_active: true,
        createdAt: '2019-09-26T07:01:57.000Z',
        updatedAt: '2019-09-26T07:01:57.000Z',
        deletedAt: null
      },
      {
        id: 6,
        name: 'Interswitch',
        meta: null,
        slug: 'interswitch',
        is_active: true,
        createdAt: '2019-09-26T07:01:57.000Z',
        updatedAt: '2019-09-26T07:01:57.000Z',
        deletedAt: null
      },
      {
        id: 7,
        name: 'Quickteller',
        meta: null,
        slug: 'quickteller',
        is_active: true,
        createdAt: '2019-09-26T07:01:57.000Z',
        updatedAt: '2019-09-26T07:01:57.000Z',
        deletedAt: null
      },
      {
        id: 8,
        name: 'Test Bank Transfer',
        meta: null,
        slug: 'test_bank_transfer',
        is_active: true,
        createdAt: '2019-09-30T06:19:30.000Z',
        updatedAt: '2019-09-30T06:19:30.000Z',
        deletedAt: null
      },
      {
        id: 9,
        name: 'Test Card Payment',
        meta: null,
        slug: 'test_card_payment',
        is_active: true,
        createdAt: '2019-09-30T06:19:30.000Z',
        updatedAt: '2019-09-30T06:19:30.000Z',
        deletedAt: null
      },
      {
        id: 10,
        name: 'GIPS',
        meta: null,
        slug: 'gips',
        is_active: true,
        createdAt: '2019-10-16T04:18:55.000Z',
        updatedAt: '2019-10-16T04:18:55.000Z',
        deletedAt: null
      },
      {
        id: 11,
        name: 'Test Transfer',
        meta: null,
        slug: 'test_transfer',
        is_active: true,
        createdAt: '2019-10-21T04:25:47.000Z',
        updatedAt: '2019-10-21T04:25:47.000Z',
        deletedAt: null
      },
      {
        id: 12,
        name: 'Flutterwave',
        meta: null,
        slug: 'flutterwave',
        is_active: true,
        createdAt: '2021-03-09T20:00:40.000Z',
        updatedAt: '2021-03-09T20:00:40.000Z',
        deletedAt: null
      },
      {
        id: 13,
        name: 'Wema',
        meta: null,
        slug: 'wema',
        is_active: true,
        createdAt: '2021-05-19T13:27:27.000Z',
        updatedAt: '2021-05-19T13:27:27.000Z',
        deletedAt: null
      },
      {
        id: 14,
        name: 'Providus',
        meta: null,
        slug: 'providus',
        is_active: true,
        createdAt: '2021-06-16T18:47:37.000Z',
        updatedAt: '2021-06-16T18:47:37.000Z',
        deletedAt: null
      },
      {
        id: 15,
        name: 'Mastercard-Wema',
        meta: null,
        slug: 'mastercard-wema',
        is_active: true,
        createdAt: '2021-11-23T08:12:19.000Z',
        updatedAt: '2021-11-23T08:12:19.000Z',
        deletedAt: null
      },
      {
        id: 16,
        name: 'Mastercard-Equity',
        meta: null,
        slug: 'mastercard-equity',
        is_active: true,
        createdAt: '2021-11-23T08:12:19.000Z',
        updatedAt: '2021-11-23T08:12:19.000Z',
        deletedAt: null
      },
      {
        id: 17,
        name: 'Nibss',
        meta: null,
        slug: 'nibss',
        is_active: true,
        createdAt: '2022-04-21T12:21:24.000Z',
        updatedAt: '2022-04-21T12:21:24.000Z',
        deletedAt: null
      }
    ]
  }
};

export const mockedPermissionsData = {
  data: [
    {
      id: 1,
      name: 'View dashboard',
      slug: 'dashboard.view',
      type: 'admin',
      entity: 'dashboard',
      description: 'This grants the permission for an administrator to access the dashboard page',
      createdAt: '2023-01-21T11:27:22.000Z',
      updatedAt: '2023-01-25T12:35:43.000Z',
      deletedAt: null
    },
    {
      id: 2,
      name: 'View dashboard analytics',
      slug: 'dashboard_analytics.view',
      type: 'admin',
      entity: 'dashboard',
      description: 'This grants the permission for an administrator to view the dashboard analytics',
      createdAt: '2023-01-21T11:27:22.000Z',
      updatedAt: '2023-01-25T12:35:43.000Z',
      deletedAt: null
    },
    {
      id: 3,
      name: 'View merchants list',
      slug: 'merchants.view',
      type: 'admin',
      entity: 'merchants',
      description: 'This grants the permission for an administrator to view the merchant list',
      createdAt: '2023-01-21T11:27:22.000Z',
      updatedAt: '2023-01-25T12:35:43.000Z',
      deletedAt: null
    },
    {
      id: 4,
      name: 'Export merchants list',
      slug: 'merchants.export',
      type: 'admin',
      entity: 'merchants',
      description: 'This grants the permission for an administrator to export merchant list',
      createdAt: '2023-01-21T11:27:22.000Z',
      updatedAt: '2023-01-25T12:35:43.000Z',
      deletedAt: null
    },
    {
      id: 5,
      name: 'View merchant ',
      slug: 'merchant_analytics.view',
      type: 'admin',
      entity: 'merchants',
      description: 'This grants the permission for an administrator to view merchant analytics',
      createdAt: '2023-01-21T11:27:22.000Z',
      updatedAt: '2023-01-25T12:35:43.000Z',
      deletedAt: null
    },
    {
      id: 6,
      name: 'View merchant general details',
      slug: 'merchant_general_details.view',
      type: 'admin',
      entity: 'merchants',
      description: 'This grants the permission for an administrator to view merchant general information',
      createdAt: '2023-01-21T11:27:22.000Z',
      updatedAt: '2023-01-25T12:35:43.000Z',
      deletedAt: null
    },
    {
      id: 7,
      name: 'View merchant balance',
      slug: 'merchant_balances.view',
      type: 'admin',
      entity: 'merchants',
      description: 'This grants the permission for an administrator to view merchant balance',
      createdAt: '2023-01-21T11:27:22.000Z',
      updatedAt: '2023-01-25T12:35:43.000Z',
      deletedAt: null
    },
    {
      id: 8,
      name: 'View merchant balance history',
      slug: 'merchant_balance_history.view',
      type: 'admin',
      entity: 'merchants',
      description: 'This grants the permission for an administrator to view a merchant balance history',
      createdAt: '2023-01-21T11:27:22.000Z',
      updatedAt: '2023-01-25T12:35:43.000Z',
      deletedAt: null
    },
    {
      id: 9,
      name: 'Export merchant balance history',
      slug: 'merchant_balance_history.export',
      type: 'admin',
      entity: 'merchants',
      description: 'This grants the permission for an administrator to export a merchant balance history',
      createdAt: '2023-01-21T11:27:22.000Z',
      updatedAt: '2023-01-25T12:35:43.000Z',
      deletedAt: null
    },
    {
      id: 10,
      name: 'Export merchant rolling reserve configuration',
      slug: 'merchant_rolling_reserve_config.view',
      type: 'admin',
      entity: 'merchants',
      description: 'This grants the permission for an administrator to view a merchant rolling reserve configuration',
      createdAt: '2023-01-21T11:27:22.000Z',
      updatedAt: '2023-01-25T12:35:43.000Z',
      deletedAt: null
    },
    {
      id: 11,
      name: 'Update merchant rolling reserve configuration',
      slug: 'merchant_rolling_reserve_config.update',
      type: 'admin',
      entity: 'merchants',
      description: 'This grants the permission for an administrator to update a merchant rolling reserve configuration',
      createdAt: '2023-01-21T11:27:22.000Z',
      updatedAt: '2023-01-25T12:35:43.000Z',
      deletedAt: null
    },
    {
      id: 12,
      name: 'View merchant rolling reserve balance history',
      slug: 'merchant_rolling_reserve_balance_history.view',
      type: 'admin',
      entity: 'merchants',
      description: 'This grants the permission for an administrator to view a merchant rolling reserve balance history',
      createdAt: '2023-01-21T11:27:22.000Z',
      updatedAt: '2023-01-25T12:35:43.000Z',
      deletedAt: null
    },
    {
      id: 13,
      name: 'View merchant settlement configuration',
      slug: 'merchant_settlement_config.view',
      type: 'admin',
      entity: 'merchants',
      description: 'This grants the permission for an administrator to view a merchant settlement configuration',
      createdAt: '2023-01-21T11:27:22.000Z',
      updatedAt: '2023-01-25T12:35:43.000Z',
      deletedAt: null
    },
    {
      id: 14,
      name: 'Update merchant settlement configuration',
      slug: 'merchant_settlement_config.update',
      type: 'admin',
      entity: 'merchants',
      description: 'This grants the permission for an administrator to update a merchant settlement configuration',
      createdAt: '2023-01-21T11:27:22.000Z',
      updatedAt: '2023-01-25T12:35:43.000Z',
      deletedAt: null
    },
    {
      id: 15,
      name: 'View merchant fee configuration',
      slug: 'merchant_fee_config.view',
      type: 'admin',
      entity: 'merchants',
      description: 'This grants the permission for an administrator to view a merchant fee configuration',
      createdAt: '2023-01-21T11:27:22.000Z',
      updatedAt: '2023-01-25T12:35:43.000Z',
      deletedAt: null
    },
    {
      id: 16,
      name: 'Update merchant fee configuration',
      slug: 'merchant_fee_config.update',
      type: 'admin',
      entity: 'merchants',
      description: 'This grants the permission for an administrator to update a merchant fee configuration',
      createdAt: '2023-01-21T11:27:22.000Z',
      updatedAt: '2023-01-25T12:35:43.000Z',
      deletedAt: null
    },
    {
      id: 17,
      name: 'View merchant teams',
      slug: 'merchant_teams.view',
      type: 'admin',
      entity: 'merchants',
      description: 'This grants the permission for an administrator to view a merchant team members',
      createdAt: '2023-01-21T11:27:22.000Z',
      updatedAt: '2023-01-25T12:35:43.000Z',
      deletedAt: null
    },
    {
      id: 18,
      name: 'View merchant audit logs',
      slug: 'merchant_audit_logs.view',
      type: 'admin',
      entity: 'merchants',
      description: 'This grants the permission for an administrator to view a merchant audit logs',
      createdAt: '2023-01-21T11:27:22.000Z',
      updatedAt: '2023-01-25T12:35:43.000Z',
      deletedAt: null
    },
    {
      id: 19,
      name: 'Update merchant status',
      slug: 'merchant_status.update',
      type: 'admin',
      entity: 'merchants',
      description: 'This grants the permission for an administrator to update a merchant account status',
      createdAt: '2023-01-21T11:27:22.000Z',
      updatedAt: '2023-01-25T12:35:43.000Z',
      deletedAt: null
    },
    {
      id: 20,
      name: 'View merchant compliance list',
      slug: 'merchant_compliance.view',
      type: 'admin',
      entity: 'merchants',
      description: 'This grants the permission for an administrator to view a merchant compliance list',
      createdAt: '2023-01-21T11:27:22.000Z',
      updatedAt: '2023-01-25T12:35:43.000Z',
      deletedAt: null
    },
    {
      id: 21,
      name: 'View merchant compliance details',
      slug: 'merchant_compliance_details.view',
      type: 'admin',
      entity: 'merchants',
      description: 'This grants the permission for an administrator to view a merchant compliance information',
      createdAt: '2023-01-21T11:27:22.000Z',
      updatedAt: '2023-01-25T12:35:43.000Z',
      deletedAt: null
    },
    {
      id: 22,
      name: 'Update merchant KYC status',
      slug: 'merchant_kyc_status.update',
      type: 'admin',
      entity: 'merchants',
      description: 'This grants the permission for an administrator to update a merchant KYC status',
      createdAt: '2023-01-21T11:27:22.000Z',
      updatedAt: '2023-01-25T12:35:43.000Z',
      deletedAt: null
    },
    {
      id: 23,
      name: 'merchant_support_email.update',
      slug: 'merchant_support_email.update',
      type: 'admin',
      entity: 'merchants',
      description: 'This grants the permission for an administrator to update a merchant support email',
      createdAt: '2023-01-21T11:27:22.000Z',
      updatedAt: '2023-01-25T12:35:43.000Z',
      deletedAt: null
    },
    {
      id: 24,
      name: 'View merchant settlement account',
      slug: 'merchant_settlement_account.view',
      type: 'admin',
      entity: 'merchants',
      description: 'This grants the permission for an administrator to view a merchant settlement account',
      createdAt: '2023-01-21T11:27:22.000Z',
      updatedAt: '2023-01-25T12:35:43.000Z',
      deletedAt: null
    },
    {
      id: 25,
      name: 'Update merchant settlement account status',
      slug: 'merchant_settlement_account_status.update',
      type: 'admin',
      entity: 'merchants',
      description: 'This grants the permission for an administrator to update a merchant settlement account',
      createdAt: '2023-01-21T11:27:22.000Z',
      updatedAt: '2023-01-25T12:35:43.000Z',
      deletedAt: null
    },
    {
      id: 26,
      name: 'View virtual accounts list',
      slug: 'virtual_accounts.view',
      type: 'admin',
      entity: 'virtual_accounts',
      description: 'This grants the permission for an administrator to view virtual accounts list',
      createdAt: '2023-01-21T11:27:22.000Z',
      updatedAt: '2023-01-25T12:35:43.000Z',
      deletedAt: null
    },
    {
      id: 27,
      name: 'View virtual account details',
      slug: 'virtual_account_details.view',
      type: 'admin',
      entity: 'virtual_accounts',
      description: 'This grants the permission for an administrator to view a virtual account details',
      createdAt: '2023-01-21T11:27:22.000Z',
      updatedAt: '2023-01-25T12:35:43.000Z',
      deletedAt: null
    },
    {
      id: 28,
      name: 'Update virtual account status',
      slug: 'virtual_account_status.update',
      type: 'admin',
      entity: 'virtual_accounts',
      description: 'This grants the permission for an administrator to update a virtual account status',
      createdAt: '2023-01-21T11:27:22.000Z',
      updatedAt: '2023-01-25T12:35:43.000Z',
      deletedAt: null
    },
    {
      id: 29,
      name: 'View Pay-ins List',
      slug: 'pay_ins.view',
      type: 'admin',
      entity: 'pay-ins',
      description: 'This grants the permission for an administrator to view payins list',
      createdAt: '2023-01-21T11:27:22.000Z',
      updatedAt: '2023-01-25T12:35:43.000Z',
      deletedAt: null
    },
    {
      id: 30,
      name: 'Export pay-ins list',
      slug: 'pay_ins.export',
      type: 'admin',
      entity: 'pay-ins',
      description: 'This grants the permission for an administrator to export payins list',
      createdAt: '2023-01-21T11:27:22.000Z',
      updatedAt: '2023-01-25T12:35:43.000Z',
      deletedAt: null
    },
    {
      id: 31,
      name: 'View pay-in details',
      slug: 'pay_in_details.view',
      type: 'admin',
      entity: 'pay-ins',
      description: 'This grants the permission for an administrator to view a payins details',
      createdAt: '2023-01-21T11:27:22.000Z',
      updatedAt: '2023-01-25T12:35:43.000Z',
      deletedAt: null
    },
    {
      id: 32,
      name: 'View payouts list',
      slug: 'payouts.view',
      type: 'admin',
      entity: 'payouts',
      description: 'This grants the permission for an administrator to view a payout list',
      createdAt: '2023-01-21T11:27:22.000Z',
      updatedAt: '2023-01-25T12:35:43.000Z',
      deletedAt: null
    },
    {
      id: 33,
      name: 'Export payouts list',
      slug: 'payouts.export',
      type: 'admin',
      entity: 'payouts',
      description: 'This grants the permission for an administrator to export a payout list',
      createdAt: '2023-01-21T11:27:22.000Z',
      updatedAt: '2023-01-25T12:35:43.000Z',
      deletedAt: null
    },
    {
      id: 34,
      name: 'View payout details',
      slug: 'payout_details.view',
      type: 'admin',
      entity: 'payouts',
      description: 'This grants the permission for an administrator to view a payout details',
      createdAt: '2023-01-21T11:27:22.000Z',
      updatedAt: '2023-01-25T12:35:43.000Z',
      deletedAt: null
    },
    {
      id: 35,
      name: 'View payment reversals list',
      slug: 'payment_reversals.view',
      type: 'admin',
      entity: 'payment_reversals',
      description: 'This grants the permission for an administrator to view payment reversal list',
      createdAt: '2023-01-21T11:27:22.000Z',
      updatedAt: '2023-01-25T12:35:43.000Z',
      deletedAt: null
    },
    {
      id: 36,
      name: 'Export payment reversals list',
      slug: 'payment_reversals.export',
      type: 'admin',
      entity: 'payment_reversals',
      description: 'This grants the permission for an administrator to export payment reversal list',
      createdAt: '2023-01-21T11:27:22.000Z',
      updatedAt: '2023-01-25T12:35:43.000Z',
      deletedAt: null
    },
    {
      id: 37,
      name: 'View settlements list',
      slug: 'settlements.view',
      type: 'admin',
      entity: 'settlements',
      description: 'This grants the permission for an administrator to view settlements list',
      createdAt: '2023-01-21T11:27:22.000Z',
      updatedAt: '2023-01-25T12:35:43.000Z',
      deletedAt: null
    },
    {
      id: 38,
      name: 'Export settlements list',
      slug: 'settlements.export',
      type: 'admin',
      entity: 'settlements',
      description: 'This grants the permission for an administrator to export settlements list',
      createdAt: '2023-01-21T11:27:22.000Z',
      updatedAt: '2023-01-25T12:35:43.000Z',
      deletedAt: null
    },
    {
      id: 39,
      name: 'Approve pending settlement',
      slug: 'settlement.approve',
      type: 'admin',
      entity: 'settlements',
      description: 'This grants the permission for an administrator to approve a pending settlement',
      createdAt: '2023-01-21T11:27:22.000Z',
      updatedAt: '2023-01-25T12:35:43.000Z',
      deletedAt: null
    },
    {
      id: 40,
      name: 'Process approved settlement',
      slug: 'settlement.process',
      type: 'admin',
      entity: 'settlements',
      description: 'This grants the permission for an administrator to process an approved settlement',
      createdAt: '2023-01-21T11:27:22.000Z',
      updatedAt: '2023-01-25T12:35:43.000Z',
      deletedAt: null
    },
    {
      id: 41,
      name: 'View settlement details',
      slug: 'settlement_details.view',
      type: 'admin',
      entity: 'settlements',
      description: 'This grants the permission for an administrator to view a settlement details',
      createdAt: '2023-01-21T11:27:22.000Z',
      updatedAt: '2023-01-25T12:35:43.000Z',
      deletedAt: null
    },
    {
      id: 42,
      name: 'View settlement payouts list',
      slug: 'settlement_payouts.view',
      type: 'admin',
      entity: 'settlement_payouts',
      description: 'This grants the permission for an administrator to view settlement payout list',
      createdAt: '2023-01-21T11:27:22.000Z',
      updatedAt: '2023-01-25T12:35:43.000Z',
      deletedAt: null
    },
    {
      id: 43,
      name: 'Export settlement payouts',
      slug: 'settlement_payouts.export',
      type: 'admin',
      entity: 'settlement_payouts',
      description: 'This grants the permission for an administrator to export settlement payout list',
      createdAt: '2023-01-21T11:27:22.000Z',
      updatedAt: '2023-01-25T12:35:43.000Z',
      deletedAt: null
    },
    {
      id: 44,
      name: 'View webhooks list',
      slug: 'webhooks.view',
      type: 'admin',
      entity: 'webhooks',
      description: 'This grants the permission for an administrator to view webhook list',
      createdAt: '2023-01-21T11:27:22.000Z',
      updatedAt: '2023-01-25T12:35:43.000Z',
      deletedAt: null
    },
    {
      id: 45,
      name: 'Export webhooks list',
      slug: 'webhooks.export',
      type: 'admin',
      entity: 'webhooks',
      description: 'This grants the permission for an administrator to export webhook list',
      createdAt: '2023-01-21T11:27:22.000Z',
      updatedAt: '2023-01-25T12:35:43.000Z',
      deletedAt: null
    },
    {
      id: 46,
      name: 'View audit logs list',
      slug: 'audit_logs.view',
      type: 'admin',
      entity: 'audit_logs',
      description: 'This grants the permission for an administrator to view audit logs list',
      createdAt: '2023-01-21T11:27:22.000Z',
      updatedAt: '2023-01-25T12:35:43.000Z',
      deletedAt: null
    },
    {
      id: 47,
      name: 'View default fee configurations',
      slug: 'default_fee_config.view',
      type: 'admin',
      entity: 'global_settings',
      description: 'This grants the permission for an administrator to view the default settlement configuration',
      createdAt: '2023-01-21T11:27:22.000Z',
      updatedAt: '2023-01-25T12:35:43.000Z',
      deletedAt: null
    },
    {
      id: 48,
      name: 'Update default fee configurations',
      slug: 'default_fee_config.update',
      type: 'admin',
      entity: 'global_settings',
      description: 'This grants the permission for an administrator to update the default settlement configuration',
      createdAt: '2023-01-21T11:27:22.000Z',
      updatedAt: '2023-01-25T12:35:43.000Z',
      deletedAt: null
    },
    {
      id: 49,
      name: 'View default settlement configurations',
      slug: 'default_settlement_config.view',
      type: 'admin',
      entity: 'global_settings',
      description: 'This grants the permission for an administrator to view the default settlement configuration',
      createdAt: '2023-01-21T11:27:22.000Z',
      updatedAt: '2023-01-25T12:35:43.000Z',
      deletedAt: null
    },
    {
      id: 50,
      name: 'Update default settlement configurations',
      slug: 'default_settlement_config.update',
      type: 'admin',
      entity: 'global_settings',
      description: 'This grants the permission for an administrator to update the default settlement configuration',
      createdAt: '2023-01-21T11:27:22.000Z',
      updatedAt: '2023-01-25T12:35:43.000Z',
      deletedAt: null
    },
    {
      id: 51,
      name: 'Update system roles',
      slug: 'system_roles.update',
      type: 'admin',
      entity: 'roles',
      description: 'This grants the permission for an administrator to assign a system role to a user',
      createdAt: '2023-01-21T11:27:22.000Z',
      updatedAt: '2023-01-25T12:35:43.000Z',
      deletedAt: null
    },
    {
      id: 52,
      name: 'View list of admin users',
      slug: 'admin_users.view',
      type: 'admin',
      entity: 'users',
      description: 'This grants the permission for an administrator to the list of internal users',
      createdAt: '2023-01-21T11:27:22.000Z',
      updatedAt: '2023-01-25T12:35:43.000Z',
      deletedAt: null
    },
    {
      id: 53,
      name: 'Export users list',
      slug: 'admin_users.export',
      type: 'admin',
      entity: 'users',
      description: 'This grants the permission to export the list of admin users',
      createdAt: '2023-01-21T11:27:22.000Z',
      updatedAt: '2023-01-25T12:35:43.000Z',
      deletedAt: null
    },
    {
      id: 54,
      name: 'View user details',
      slug: 'admin_user_details.view',
      type: 'admin',
      entity: 'users',
      description: 'This grants the permission for an administrator to suspend, activate or deactivate a user',
      createdAt: '2023-01-21T11:27:22.000Z',
      updatedAt: '2023-01-25T12:35:43.000Z',
      deletedAt: null
    },
    {
      id: 55,
      name: 'Update user',
      slug: 'admin_users.update',
      type: 'admin',
      entity: 'users',
      description: 'This grants the permission for an administrator to suspend, activate or deactivate a user',
      createdAt: '2023-01-21T11:27:22.000Z',
      updatedAt: '2023-01-25T12:35:43.000Z',
      deletedAt: null
    },
    {
      id: 57,
      name: 'View admin permissions',
      slug: 'admin_user_permissions.view',
      type: 'admin',
      entity: 'roles',
      description: 'This grants the permission for an administrator to view a user permissions',
      createdAt: '2023-01-21T11:27:22.000Z',
      updatedAt: '2023-01-25T12:35:43.000Z',
      deletedAt: null
    },
    {
      id: 58,
      name: 'Update admin permissions',
      slug: 'admin_user_permissions.update',
      type: 'admin',
      entity: 'roles',
      description: 'This grants the permission for an administrator to modify a user permissions',
      createdAt: '2023-01-21T11:27:22.000Z',
      updatedAt: '2023-01-25T12:35:43.000Z',
      deletedAt: null
    },
    {
      id: 59,
      name: 'View admin invitations list',
      slug: 'admin_user_invitations.view',
      type: 'admin',
      entity: 'users',
      description: 'This grants the permission for an administrator to view admin user invitations list',
      createdAt: '2023-01-21T11:27:22.000Z',
      updatedAt: '2023-01-25T12:35:43.000Z',
      deletedAt: null
    },
    {
      id: 60,
      name: 'Invite an admin user',
      slug: 'admin_user_invitations.create',
      type: 'admin',
      entity: 'users',
      description: 'This grants the permission for an administrator to invite an admin user',
      createdAt: '2023-01-21T11:27:22.000Z',
      updatedAt: '2023-01-25T12:35:43.000Z',
      deletedAt: null
    },
    {
      id: 61,
      name: 'Revoke an admin user invite',
      slug: 'admin_user_invitations.delete',
      type: 'admin',
      entity: 'users',
      description: 'This grants the permission for an administrator to invite an admin user',
      createdAt: '2023-01-21T11:27:22.000Z',
      updatedAt: '2023-01-25T12:35:43.000Z',
      deletedAt: null
    },
    {
      id: 62,
      name: 'Admin user audit logs list',
      slug: 'admin_user_audit_logs.view',
      type: 'admin',
      entity: 'users',
      description: 'This grants the permission for an administrator to invite an admin user',
      createdAt: '2023-01-21T11:27:22.000Z',
      updatedAt: '2023-01-25T12:35:43.000Z',
      deletedAt: null
    },
    {
      id: 63,
      name: 'View admin system roles',
      slug: 'system_roles.view',
      type: 'admin',
      entity: 'roles',
      description: 'This grants the permission for an administrator to view system roles and also assign it to a user',
      createdAt: '2023-01-21T11:27:22.000Z',
      updatedAt: '2023-01-25T12:35:43.000Z',
      deletedAt: null
    },
    {
      id: 64,
      name: 'Create admin custom roles',
      slug: 'custom_roles.create',
      type: 'admin',
      entity: 'roles',
      description: 'This grants the permission for an administrator to create custom roles',
      createdAt: '2023-01-21T11:27:22.000Z',
      updatedAt: '2023-01-25T12:35:43.000Z',
      deletedAt: null
    },
    {
      id: 65,
      name: 'View my custom roles',
      slug: 'my_custom_roles.view',
      type: 'admin',
      entity: 'roles',
      description: 'This grants the permission for an administrator to view custom roles added by them',
      createdAt: '2023-01-21T11:27:22.000Z',
      updatedAt: '2023-01-25T12:35:43.000Z',
      deletedAt: null
    },
    {
      id: 66,
      name: 'Update my custom roles',
      slug: 'my_custom_roles.update',
      type: 'admin',
      entity: 'roles',
      description: 'This grants the permission for an administrator to update custom roles added by them',
      createdAt: '2023-01-21T11:27:22.000Z',
      updatedAt: '2023-01-25T12:35:43.000Z',
      deletedAt: null
    },
    {
      id: 67,
      name: 'View any custom roles',
      slug: 'custom_roles.view',
      type: 'admin',
      entity: 'roles',
      description: 'This grants the permission for an administrator to view any custom roles',
      createdAt: '2023-01-21T11:27:22.000Z',
      updatedAt: '2023-01-25T12:35:43.000Z',
      deletedAt: null
    },
    {
      id: 68,
      name: 'Update any custom role',
      slug: 'custom_roles.update',
      type: 'admin',
      entity: 'roles',
      description: 'This grants the permission for an administrator to update any custom role and also assign it to a user',
      createdAt: '2023-01-21T11:27:22.000Z',
      updatedAt: '2023-01-25T12:35:43.000Z',
      deletedAt: null
    }
  ]
};

export const mockedUserRoles = {
  data: {
    paging: {
      total_items: 9,
      page_size: 50,
      current: 1,
      count: 9
    },
    links: [
      {
        href: 'http://localhost:7100/api/admin/user-roles?page=1&limit=1000&includeSystemRoles=true',
        rel: 'current',
        method: 'GET'
      }
    ],
    data: [
      {
        id: 6,
        name: 'Highest Admin Role',
        slug: 'highest_admin_role',
        type: 'admin',
        category: 'system',
        created_by: null,
        permissions: [
          'dashboard.view',
          'dashboard_analytics.view',
          'merchants.view',
          'merchants.export',
          'merchant_analytics.view',
          'merchant_general_details.view',
          'merchant_balances.view',
          'merchant_balance_history.view',
          'merchant_balance_history.export',
          'merchant_rolling_reserve_config.view',
          'merchant_rolling_reserve_config.update',
          'merchant_rolling_reserve_balance_history.view',
          'merchant_settlement_config.view',
          'merchant_settlement_config.update',
          'merchant_fee_config.view',
          'merchant_fee_config.update',
          'merchant_teams.view',
          'merchant_audit_logs.view',
          'merchant_status.update',
          'merchant_compliance.view',
          'merchant_compliance_details.view',
          'merchant_kyc_status.update',
          'merchant_support_email.update',
          'merchant_settlement_account.view',
          'merchant_settlement_account_status.update',
          'virtual_accounts.view',
          'virtual_account_status.update',
          'virtual_account_details.view',
          'pay_ins.view',
          'pay_ins.export',
          'pay_in_details.view',
          'payouts.view',
          'payouts.export',
          'payout_details.view',
          'payment_reversals.view',
          'payment_reversals.export',
          'settlements.view',
          'settlements.export',
          'settlement.approve',
          'settlement.process',
          'settlement_details.view',
          'settlement_payouts.view',
          'settlement_payouts.export',
          'webhooks.view',
          'webhooks.export',
          'audit_logs.view',
          'default_fee_config.view',
          'default_fee_config.update',
          'default_settlement_config.view',
          'default_settlement_config.update',
          'admin_users.view',
          'admin_users.export',
          'admin_user_details.view',
          'admin_users.update',
          'admin_user_permissions.view',
          'admin_user_permissions.update',
          'admin_users.delete',
          'admin_user_invitations.view',
          'admin_user_invitations.create',
          'admin_user_invitation.delete',
          'admin_user_audit_logs.view',
          'system_roles.view',
          'system_roles.update',
          'custom_roles.create',
          'my_custom_roles.view',
          'my_custom_roles.update',
          'custom_roles.view',
          'custom_roles.update'
        ],
        createdAt: '2022-12-01T14:27:54.000Z',
        updatedAt: '2022-12-01T14:27:54.000Z',
        deletedAt: null,
        createdBy: null
      },
      {
        id: 123,
        name: 'Custom-MErchant success TEst 2',
        slug: 'internal_admin_custom-merchant_success_test_2',
        type: 'admin',
        category: 'custom',
        created_by: 471,
        permissions: [
          'webhooks.view',
          'admin_users.view',
          'admin_user_details.view',
          'admin_user_invitations.view',
          'admin_user_audit_logs.view',
          'dashboard.view',
          'dashboard_analytics.view'
        ],
        createdAt: '2023-02-08T12:36:37.000Z',
        updatedAt: '2023-02-14T17:42:22.000Z',
        deletedAt: null,
        createdBy: {
          first_name: 'Lewis ',
          last_name: 'Dzeremo ',
          email: '<EMAIL>'
        }
      },
      {
        id: 127,
        name: 'Custom-Test Check',
        slug: 'internal_admin_custom-test_check',
        type: 'admin',
        category: 'custom',
        created_by: 556,
        permissions: [
          'dashboard.view',
          'dashboard_analytics.view',
          'merchants.view',
          'merchants.export',
          'merchant_analytics.view',
          'merchant_general_details.view',
          'merchant_balances.view',
          'merchant_balance_history.view',
          'merchant_balance_history.export',
          'merchant_rolling_reserve_config.update',
          'merchant_rolling_reserve_config.view',
          'merchant_rolling_reserve_balance_history.view',
          'merchant_settlement_config.view',
          'merchant_settlement_config.update',
          'merchant_fee_config.update',
          'merchant_fee_config.view',
          'merchant_teams.view',
          'merchant_audit_logs.view',
          'merchant_status.update',
          'merchant_compliance.view',
          'merchant_compliance_details.view',
          'merchant_kyc_status.update',
          'merchant_support_email.update',
          'merchant_settlement_account.view',
          'merchant_settlement_account_status.update',
          'virtual_accounts.view',
          'virtual_account_details.view',
          'virtual_account_status.update',
          'pay_ins.view',
          'pay_in_details.view',
          'payouts.view',
          'payout_details.view',
          'settlement_payouts.view',
          'settlement_payouts.export',
          'payouts.export',
          'payment_reversals.view',
          'payment_reversals.export',
          'settlements.view',
          'settlements.export',
          'settlement.approve',
          'settlement.process',
          'settlement_details.view',
          'webhooks.view',
          'webhooks.export',
          'audit_logs.view',
          'default_fee_config.view',
          'default_settlement_config.view',
          'default_fee_config.update',
          'default_settlement_config.update',
          'admin_users.view',
          'admin_users.export',
          'admin_users.update',
          'admin_user_details.view',
          'admin_user_invitations.view',
          'admin_user_audit_logs.view',
          'admin_user_invitations.create',
          'system_roles.view',
          'admin_user_permissions.view',
          'custom_roles.view',
          'my_custom_roles.view',
          'system_roles.update',
          'admin_user_permissions.update',
          'custom_roles.update',
          'my_custom_roles.update',
          'custom_roles.create'
        ],
        createdAt: '2023-02-16T11:06:12.000Z',
        updatedAt: '2023-02-16T11:06:12.000Z',
        deletedAt: null,
        createdBy: {
          first_name: 'Joy',
          last_name: 'Amadikwa',
          email: '<EMAIL>'
        }
      },
      {
        id: 121,
        name: 'Custom-Test Lead Role',
        slug: 'internal_admin_custom-test_lead_role',
        type: 'admin',
        category: 'custom',
        created_by: 503,
        permissions: [
          'dashboard.view',
          'dashboard_analytics.view',
          'merchants.view',
          'merchants.export',
          'merchant_analytics.view',
          'merchant_general_details.view',
          'merchant_balances.view',
          'merchant_balance_history.view',
          'merchant_balance_history.export',
          'merchant_rolling_reserve_config.update',
          'merchant_rolling_reserve_config.view',
          'merchant_rolling_reserve_balance_history.view',
          'merchant_settlement_config.view',
          'merchant_settlement_config.update',
          'merchant_fee_config.update',
          'merchant_fee_config.view',
          'merchant_teams.view',
          'merchant_audit_logs.view',
          'merchant_status.update',
          'merchant_compliance.view',
          'merchant_compliance_details.view',
          'merchant_kyc_status.update',
          'merchant_support_email.update',
          'merchant_settlement_account.view',
          'merchant_settlement_account_status.update',
          'virtual_accounts.view',
          'virtual_account_details.view',
          'virtual_account_status.update',
          'pay_ins.view',
          'pay_ins.export',
          'pay_in_details.view',
          'payouts.view',
          'payout_details.view',
          'settlement_payouts.view',
          'settlement_payouts.export',
          'payouts.export',
          'payment_reversals.view',
          'payment_reversals.export',
          'settlements.view',
          'settlements.export',
          'settlement.approve',
          'settlement.process',
          'settlement_details.view',
          'webhooks.view',
          'webhooks.export',
          'audit_logs.view',
          'default_fee_config.view',
          'default_settlement_config.view',
          'default_fee_config.update',
          'default_settlement_config.update',
          'admin_users.view',
          'admin_users.export',
          'admin_users.update',
          'admin_user_details.view',
          'admin_user_invitations.view',
          'admin_user_audit_logs.view',
          'admin_user_invitations.create',
          'system_roles.view',
          'admin_user_permissions.view',
          'custom_roles.view',
          'my_custom_roles.view',
          'system_roles.update',
          'admin_user_permissions.update',
          'custom_roles.update',
          'my_custom_roles.update',
          'custom_roles.create'
        ],
        createdAt: '2023-01-27T15:13:06.000Z',
        updatedAt: '2023-01-27T15:13:06.000Z',
        deletedAt: null,
        createdBy: {
          first_name: 'Tunde',
          last_name: 'Osborne',
          email: '<EMAIL>'
        }
      },
      {
        id: 124,
        name: 'Custom-test role',
        slug: 'internal_admin_custom-test_role',
        type: 'admin',
        category: 'custom',
        created_by: 471,
        permissions: [
          'dashboard.view',
          'dashboard_analytics.view',
          'merchants.view',
          'merchant_analytics.view',
          'merchant_general_details.view',
          'merchant_balances.view',
          'merchant_balance_history.view',
          'merchant_rolling_reserve_config.view',
          'merchant_rolling_reserve_balance_history.view',
          'merchant_settlement_config.view',
          'virtual_accounts.view',
          'virtual_account_details.view',
          'pay_ins.view',
          'pay_in_details.view',
          'payouts.view',
          'payout_details.view',
          'settlement_payouts.view',
          'payment_reversals.view',
          'settlements.view',
          'settlement_details.view',
          'webhooks.view',
          'audit_logs.view',
          'default_fee_config.view',
          'default_settlement_config.view',
          'admin_users.view',
          'admin_user_details.view',
          'admin_user_invitations.view',
          'admin_user_audit_logs.view',
          'my_custom_roles.view',
          'pay_ins.export',
          'settlement_payouts.export'
        ],
        createdAt: '2023-02-09T09:43:18.000Z',
        updatedAt: '2023-02-09T09:43:18.000Z',
        deletedAt: null,
        createdBy: {
          first_name: 'Lewis ',
          last_name: 'Dzeremo ',
          email: '<EMAIL>'
        }
      },
      {
        id: 125,
        name: 'Custom-test test',
        slug: 'internal_admin_custom-test_test',
        type: 'admin',
        category: 'custom',
        created_by: 471,
        permissions: ['pay_ins.view', 'pay_in_details.view'],
        createdAt: '2023-02-09T09:55:07.000Z',
        updatedAt: '2023-02-09T09:55:07.000Z',
        deletedAt: null,
        createdBy: {
          first_name: 'Lewis ',
          last_name: 'Dzeremo ',
          email: '<EMAIL>'
        }
      },
      {
        id: 122,
        name: 'Dami Test',
        slug: 'internal_admin_dami_test',
        type: 'admin',
        category: 'custom',
        created_by: 471,
        permissions: [
          'dashboard.view',
          'dashboard_analytics.view',
          'merchants.view',
          'merchant_analytics.view',
          'merchant_general_details.view',
          'merchant_balances.view',
          'merchant_balance_history.view',
          'merchant_rolling_reserve_config.view',
          'merchant_rolling_reserve_balance_history.view',
          'merchant_settlement_config.view',
          'virtual_accounts.view',
          'virtual_account_details.view',
          'pay_ins.view',
          'pay_in_details.view',
          'payouts.view',
          'payout_details.view',
          'settlement_payouts.view',
          'payment_reversals.view',
          'settlements.view',
          'settlement_details.view',
          'webhooks.view',
          'audit_logs.view',
          'default_fee_config.view',
          'default_settlement_config.view',
          'admin_users.view',
          'admin_user_details.view',
          'admin_user_invitations.view',
          'admin_user_audit_logs.view',
          'my_custom_roles.view',
          'pay_ins.export'
        ],
        createdAt: '2023-01-30T10:21:27.000Z',
        updatedAt: '2023-02-08T12:29:03.000Z',
        deletedAt: null,
        createdBy: {
          first_name: 'Lewis ',
          last_name: 'Dzeremo ',
          email: '<EMAIL>'
        }
      },
      {
        id: 126,
        name: 'tester 3030',
        slug: 'internal_admin_tester_3030',
        type: 'admin',
        category: 'custom',
        created_by: 495,
        permissions: ['dashboard_analytics.view', 'webhooks.view', 'dashboard.view'],
        createdAt: '2023-02-15T10:01:16.000Z',
        updatedAt: '2023-02-16T09:18:33.000Z',
        deletedAt: null,
        createdBy: {
          first_name: 'Damilola',
          last_name: 'Adejuwon',
          email: '<EMAIL>'
        }
      },
      {
        id: 7,
        name: 'Semi-Highest Admin Role',
        slug: 'semi_highest_admin_role',
        type: 'admin',
        category: 'system',
        created_by: null,
        permissions: [
          'dashboard.view',
          'dashboard_analytics.view',
          'merchants.view',
          'merchants.export',
          'merchant_analytics.view',
          'merchant_general_details.view',
          'merchant_balances.view',
          'merchant_balance_history.view',
          'merchant_balance_history.export',
          'merchant_rolling_reserve_config.view',
          'merchant_rolling_reserve_config.update',
          'merchant_rolling_reserve_balance_history.view',
          'merchant_settlement_config.view',
          'merchant_settlement_config.update',
          'merchant_fee_config.view',
          'merchant_fee_config.update',
          'merchant_teams.view',
          'merchant_audit_logs.view',
          'merchant_status.update',
          'merchant_compliance.view',
          'merchant_compliance_details.view',
          'merchant_kyc_status.update',
          'merchant_support_email.update',
          'merchant_settlement_account.view',
          'merchant_settlement_account_status.update',
          'virtual_accounts.view',
          'virtual_account_status.update',
          'virtual_account_details.view',
          'pay_ins.view',
          'pay_ins.export',
          'pay_in_details.view',
          'payouts.view',
          'payouts.export',
          'payout_details.view',
          'payment_reversals.view',
          'payment_reversals.export',
          'settlements.view',
          'settlements.export',
          'settlement.approve',
          'settlement.process',
          'settlement_details.view',
          'settlement_payouts.view',
          'settlement_payouts.export',
          'webhooks.view',
          'webhooks.export',
          'audit_logs.view',
          'default_fee_config.view',
          'default_fee_config.update',
          'default_settlement_config.view',
          'default_settlement_config.update',
          'admin_users.view',
          'admin_users.export',
          'admin_user_details.view',
          'admin_users.update',
          'admin_user_permissions.view',
          'admin_user_permissions.update',
          'admin_users.delete',
          'admin_user_invitations.view',
          'admin_user_invitations.create',
          'admin_user_invitation.delete',
          'admin_user_audit_logs.view',
          'custom_roles.create',
          'my_custom_roles.view',
          'my_custom_roles.update',
          'custom_roles.view',
          'custom_roles.update'
        ],
        createdAt: '2022-12-28T13:56:51.000Z',
        updatedAt: '2022-12-28T13:56:51.000Z',
        deletedAt: null,
        createdBy: null
      }
    ]
  }
};

export const mockedUserRole = {
  data: {
    id: 6,
    name: 'Highest Admin Role',
    slug: 'highest_admin_role',
    type: 'admin',
    category: 'system',
    created_by: null,
    permissions: [
      'dashboard.view',
      'dashboard_analytics.view',
      'merchants.view',
      'merchants.export',
      'merchant_analytics.view',
      'merchant_general_details.view',
      'merchant_balances.view',
      'merchant_balance_history.view',
      'merchant_balance_history.export',
      'merchant_rolling_reserve_config.view',
      'merchant_rolling_reserve_config.update',
      'merchant_rolling_reserve_balance_history.view',
      'merchant_settlement_config.view',
      'merchant_settlement_config.update',
      'merchant_fee_config.view',
      'merchant_fee_config.update',
      'merchant_teams.view',
      'merchant_audit_logs.view',
      'merchant_status.update',
      'merchant_compliance.view',
      'merchant_compliance_details.view',
      'merchant_kyc_status.update',
      'merchant_support_email.update',
      'merchant_settlement_account.view',
      'merchant_settlement_account_status.update',
      'virtual_accounts.view',
      'virtual_account_status.update',
      'virtual_account_details.view',
      'pay_ins.view',
      'pay_ins.export',
      'pay_in_details.view',
      'payouts.view',
      'payouts.export',
      'payout_details.view',
      'payment_reversals.view',
      'payment_reversals.export',
      'settlements.view',
      'settlements.export',
      'settlement.approve',
      'settlement.process',
      'settlement_details.view',
      'settlement_payouts.view',
      'settlement_payouts.export',
      'webhooks.view',
      'webhooks.export',
      'audit_logs.view',
      'default_fee_config.view',
      'default_fee_config.update',
      'default_settlement_config.view',
      'default_settlement_config.update',
      'admin_users.view',
      'admin_users.export',
      'admin_user_details.view',
      'admin_users.update',
      'admin_user_permissions.view',
      'admin_user_permissions.update',
      'admin_users.delete',
      'admin_user_invitations.view',
      'admin_user_invitations.create',
      'admin_user_invitation.delete',
      'admin_user_audit_logs.view',
      'system_roles.view',
      'system_roles.update',
      'custom_roles.create',
      'my_custom_roles.view',
      'my_custom_roles.update',
      'custom_roles.view',
      'custom_roles.update'
    ],
    createdAt: '2022-12-01T14:27:54.000Z',
    updatedAt: '2022-12-01T14:27:54.000Z',
    deletedAt: null,
    'createdBy.first_name': null,
    'createdBy.last_name': null,
    'createdBy.email': null,
    numberOfAssignees: 5
  }
};

export const mockedCustomUserRole = {
  data: {
    id: 121,
    name: 'Custom-Test Lead Role',
    slug: 'internal_admin_custom-test_lead_role',
    type: 'admin',
    category: 'custom',
    created_by: 503,
    permissions: [
      'dashboard.view',
      'merchants.view',
      'merchants.export',
      'merchant_analytics.view',
      'merchant_general_details.view',
      'merchant_balances.view',
      'merchant_balance_history.view',
      'merchant_balance_history.export',
      'merchant_rolling_reserve_config.view',
      'merchant_rolling_reserve_config.update',
      'merchant_rolling_reserve_balance_history.view',
      'merchant_settlement_config.view',
      'merchant_settlement_config.update',
      'merchant_fee_config.view',
      'merchant_fee_config.update',
      'merchant_teams.view',
      'merchant_audit_logs.view',
      'merchant_status.update',
      'merchant_compliance.view',
      'merchant_compliance_details.view',
      'merchant_kyc_status.update',
      'merchant_support_email.update',
      'merchant_settlement_account.view',
      'merchant_settlement_account_status.update',
      'merchant_unlock_account.update',
      'virtual_accounts.view',
      'virtual_account_status.update',
      'virtual_account_details.view',
      'pay_ins.view',
      'pay_ins.export',
      'pay_in_details.view',
      'payouts.view',
      'payouts.export',
      'payout_details.view',
      'payment_reversals.view',
      'payment_reversals.export',
      'settlements.view',
      'settlements.export',
      'settlement.approve',
      'settlement.process',
      'settlement_details.view',
      'settlement_payouts.view',
      'settlement_payouts.export',
      'webhooks.view',
      'webhooks.export',
      'audit_logs.view',
      'default_fee_config.view',
      'default_fee_config.update',
      'default_settlement_config.view',
      'default_settlement_config.update',
      'admin_users.view',
      'admin_users.export',
      'admin_user_details.view',
      'admin_users.update',
      'admin_user_permissions.view',
      'admin_user_permissions.update',
      'admin_users.delete',
      'admin_user_invitations.view',
      'admin_user_invitations.create',
      'admin_user_invitation.delete',
      'admin_user_audit_logs.view',
      'system_roles.view',
      'system_roles.update',
      'custom_roles.create',
      'my_custom_roles.view',
      'my_custom_roles.update',
      'custom_roles.view',
      'custom_roles.update',
      'custom_roles.update',
      'chargeback_details.view',
      'chargebacks.view',
      'chargebacks.export',
      'chargebacks.update',
      'payout_reversals.create',
      'payout_reversals.process',
      'refunds.update',
      'refunds.export',
      'refunds.view',
      'refund_details.view',
      'transaction_config_list.view',
      'transaction_config_details.view',
      'transaction_config_details.update',
      'card_issuance_chargeback_details.view',
      'card_issuance_chargebacks.view',
      'card_issuance_chargebacks.export',
      'card_issuance_chargeback.update',
      'card_issuance_overview.view',
      'card_issuance_card_details.view',
      'card_issuance_cards.view',
      'card_issuance_cards.export',
      'card_issuance_card.update',
      'card_issuance_transactions.view',
      'card_issuance_transactions.export',
      'card_issuance_wallet_history.view',
      'card_issuance_wallet_history.export',
      'card_issuance_card_events.view',
      'card_issuance_card_events.export',
      'card_issuance_transaction_details.view',
      'issuing_wallet_deposit_request.update',
      'card_issuing_merchants.view',
      'card_issuing_merchants.export',
      'card_issuing_merchant_details.view',
      'card_issuance_access.update',
      'disable_user_totp.update',
      'payins_status.update',
      'payouts_status.update',
      'third_party_reports.create',
      'third_party_reports.view',
      'card_issuance_transaction_refund.update',
      'processor_accounts.view',
      'partner_funding.create',
      'partner_funding.view',
      'partner_funding_details.view',
      'partner_funding.export',
      'merchants_information.update',
      'admin_user_permissions.export',
      'identity_merchant_details.view',
      'identity_merchants.view',
      'identity_merchants.export',
      'identity_merchants.create',
      'identity_merchants.update',
      'identity_verification_details.view',
      'identity_verification_details.update',
      'identity_verifications.view',
      'identity_verifications.export',
      'bank_transfer_settings_requests.view',
      'bank_transfer_settings_requests.process',
      'virtual_bank_account_holders.view',
      'virtual_bank_account_holder.view',
      'virtual_bank_account_holder.update',
      'virtual_bank_account.update',
      'virtual_bank_account_upgrade_requests.view',
      'virtual_bank_account_upgrade_request.view',
      'pay_in_flagged.process',
      'default_conversion_markup_config.update',
      'virtual_account_limits.view',
      'virtual_account_limits.update'
    ],
    createdAt: '2023-01-27T15:13:06.000Z',
    updatedAt: '2024-08-28T16:16:25.000Z',
    deletedAt: null,
    'createdBy.first_name': 'Tunde',
    'createdBy.last_name': 'Osborne',
    'createdBy.email': '<EMAIL>',
    numberOfAssignees: 1
  }
};

export const mockedCustomUserRoleAssignees = {
  data: {
    paging: {
      total_items: 1,
      page_size: 10,
      current: 1,
      count: 1
    },
    links: [
      {
        href: 'http://localhost:7100/api/admin/user-roles/121/users?page=1&limit=10',
        rel: 'current',
        method: 'GET'
      }
    ],
    data: [
      {
        userId: 650,
        firstName: 'Emmanuel',
        lastName: 'Osuh',
        email: '<EMAIL>',
        dateAssigned: '2023-11-08T01:32:33.000Z'
      }
    ]
  }
};

export const mockedInvitedUsers = {
  data: {
    paging: {
      total_items: 3,
      page_size: 10,
      current: 1,
      count: 3
    },
    links: [
      {
        href: 'http://localhost:7100/api/admin/admin-invites?page=1&limit=10&status=pending',
        rel: 'current',
        method: 'GET'
      }
    ],
    data: [
      {
        id: 45,
        admin_id: 471,
        role_id: 122,
        email: '<EMAIL>',
        code: 'aiJAuKRdKDzY',
        expiry_date: '2023-02-10T05:09:09.000Z',
        status: 'pending',
        user_status: 'pending',
        user_status_description: null,
        createdAt: '2023-02-09T09:42:29.000Z',
        updatedAt: '2023-02-09T09:42:29.000Z',
        deletedAt: null,
        admin: {
          id: 471,
          first_name: 'Lewis ',
          last_name: 'Dzeremo ',
          email: '<EMAIL>'
        },
        user_role: {
          id: 122,
          name: 'Dami Test',
          slug: 'internal_admin_dami_test',
          type: 'admin',
          category: 'custom',
          created_by: 471,
          permissions: [
            'dashboard.view',
            'dashboard_analytics.view',
            'merchants.view',
            'merchant_analytics.view',
            'merchant_general_details.view',
            'merchant_balances.view',
            'merchant_balance_history.view',
            'merchant_rolling_reserve_config.view',
            'merchant_rolling_reserve_balance_history.view',
            'merchant_settlement_config.view',
            'virtual_accounts.view',
            'virtual_account_details.view',
            'pay_ins.view',
            'pay_in_details.view',
            'payouts.view',
            'payout_details.view',
            'settlement_payouts.view',
            'payment_reversals.view',
            'settlements.view',
            'settlement_details.view',
            'webhooks.view',
            'audit_logs.view',
            'default_fee_config.view',
            'default_settlement_config.view',
            'admin_users.view',
            'admin_user_details.view',
            'admin_user_invitations.view',
            'admin_user_audit_logs.view',
            'my_custom_roles.view',
            'pay_ins.export'
          ],
          createdAt: '2023-01-30T10:21:27.000Z',
          updatedAt: '2023-02-08T12:29:03.000Z',
          deletedAt: null
        }
      },
      {
        id: 44,
        admin_id: 495,
        role_id: 123,
        email: '<EMAIL>',
        code: 'aity8zv4KDeM',
        expiry_date: '2023-02-10T04:53:32.000Z',
        status: 'pending',
        user_status: 'pending',
        user_status_description: null,
        createdAt: '2023-02-09T09:26:52.000Z',
        updatedAt: '2023-02-09T09:26:52.000Z',
        deletedAt: null,
        admin: {
          id: 495,
          first_name: 'Damilola',
          last_name: 'Adejuwon',
          email: '<EMAIL>'
        },
        user_role: {
          id: 123,
          name: 'Custom-MErchant success TEst 2',
          slug: 'internal_admin_custom-merchant_success_test_2',
          type: 'admin',
          category: 'custom',
          created_by: 471,
          permissions: [
            'webhooks.view',
            'admin_users.view',
            'admin_user_details.view',
            'admin_user_invitations.view',
            'admin_user_audit_logs.view',
            'dashboard.view',
            'dashboard_analytics.view'
          ],
          createdAt: '2023-02-08T12:36:37.000Z',
          updatedAt: '2023-02-14T17:42:22.000Z',
          deletedAt: null
        }
      },
      {
        id: 43,
        admin_id: 471,
        role_id: 123,
        email: '<EMAIL>',
        code: 'ai2Wek41BxSJ',
        expiry_date: '2023-02-09T08:04:54.000Z',
        status: 'pending',
        user_status: 'pending',
        user_status_description: null,
        createdAt: '2023-02-08T12:38:14.000Z',
        updatedAt: '2023-02-08T12:38:14.000Z',
        deletedAt: null,
        admin: {
          id: 471,
          first_name: 'Lewis ',
          last_name: 'Dzeremo ',
          email: '<EMAIL>'
        },
        user_role: {
          id: 123,
          name: 'Custom-MErchant success TEst 2',
          slug: 'internal_admin_custom-merchant_success_test_2',
          type: 'admin',
          category: 'custom',
          created_by: 471,
          permissions: [
            'webhooks.view',
            'admin_users.view',
            'admin_user_details.view',
            'admin_user_invitations.view',
            'admin_user_audit_logs.view',
            'dashboard.view',
            'dashboard_analytics.view'
          ],
          createdAt: '2023-02-08T12:36:37.000Z',
          updatedAt: '2023-02-14T17:42:22.000Z',
          deletedAt: null
        }
      }
    ]
  }
};

export const mockedUsers = {
  data: {
    paging: {
      total_items: 38,
      page_size: 10,
      current: 1,
      count: 10,
      next: 2
    },
    links: [
      {
        href: 'http://localhost:7100/api/admin/admin-users?page=1&limit=10',
        rel: 'current',
        method: 'GET'
      },
      {
        href: 'http://localhost:7100/api/admin/admin-users?page=2&limit=10',
        rel: 'next',
        method: 'GET'
      }
    ],
    data: [
      {
        id: 73,
        admin_id: 495,
        role_id: 121,
        email: '<EMAIL>',
        code: 'aiWLJy8GXpDS',
        expiry_date: '2023-02-18T09:09:37.000Z',
        status: 'accepted',
        user_status: 'active',
        user_status_description: null,
        createdAt: '2023-02-17T13:42:57.000Z',
        updatedAt: '2023-02-17T13:44:50.000Z',
        deletedAt: null,
        user: {
          id: 471,
          first_name: 'Lewis ',
          last_name: 'Dzeremo ',
          email: '<EMAIL>',
          last_login: '2023-02-17T14:18:57.000Z',
          adminRoles: [
            {
              id: 125,
              name: 'Custom-test test',
              admin_user_role: {
                id: 61,
                user_id: 471,
                role_id: 125,
                createdAt: '2023-02-17T14:10:46.000Z',
                updatedAt: '2023-02-17T14:10:46.000Z'
              }
            }
          ]
        }
      },
      {
        id: 72,
        admin_id: 621,
        role_id: 6,
        email: '<EMAIL>',
        code: 'aiJJFiQSGybx',
        expiry_date: '2023-02-16T12:27:13.000Z',
        status: 'accepted',
        user_status: 'active',
        user_status_description: null,
        createdAt: '2023-02-15T17:00:33.000Z',
        updatedAt: '2023-02-15T18:43:36.000Z',
        deletedAt: null,
        user: {
          id: 723,
          first_name: 'Eniola',
          last_name: 'Olojuola',
          email: '<EMAIL>',
          last_login: '2023-02-19T18:25:00.000Z',
          adminRoles: [
            {
              id: 126,
              name: 'tester 3030',
              admin_user_role: {
                id: 52,
                user_id: 723,
                role_id: 126,
                createdAt: '2023-02-16T09:19:14.000Z',
                updatedAt: '2023-02-16T09:19:14.000Z'
              }
            }
          ]
        }
      },
      {
        id: 60,
        admin_id: 621,
        role_id: 6,
        email: '<EMAIL>',
        code: 'aisTMb5xCz9b',
        expiry_date: '2023-02-14T08:57:37.000Z',
        status: 'accepted',
        user_status: 'active',
        user_status_description: null,
        createdAt: '2023-02-13T13:30:57.000Z',
        updatedAt: '2023-02-13T13:37:58.000Z',
        deletedAt: null,
        user: {
          id: 495,
          first_name: 'Damilola',
          last_name: 'Adejuwon',
          email: '<EMAIL>',
          last_login: '2023-02-17T13:41:06.000Z',
          adminRoles: [
            {
              id: 6,
              name: 'Highest Admin Role',
              admin_user_role: {
                id: 40,
                user_id: 495,
                role_id: 6,
                createdAt: '2023-02-13T13:37:58.000Z',
                updatedAt: '2023-02-13T13:37:58.000Z'
              }
            },
            {
              id: 121,
              name: 'Custom-Test Lead Role',
              admin_user_role: {
                id: 35,
                user_id: 495,
                role_id: 121,
                createdAt: '2023-01-30T10:28:07.000Z',
                updatedAt: '2023-01-30T10:28:07.000Z'
              }
            }
          ]
        }
      },
      {
        id: 55,
        admin_id: 503,
        role_id: 7,
        email: '<EMAIL>',
        code: 'aiHWKa8RWDVU',
        expiry_date: '2023-02-14T04:12:14.000Z',
        status: 'accepted',
        user_status: 'active',
        user_status_description: null,
        createdAt: '2023-02-13T08:45:34.000Z',
        updatedAt: '2023-02-13T08:47:46.000Z',
        deletedAt: null,
        user: {
          id: 833,
          first_name: 'Daniel',
          last_name: 'Oyebanjo',
          email: '<EMAIL>',
          last_login: '2023-02-16T09:54:37.000Z',
          adminRoles: [
            {
              id: 127,
              name: 'Custom-Test Check',
              admin_user_role: {
                id: 62,
                user_id: 833,
                role_id: 127,
                createdAt: '2023-02-17T17:12:26.000Z',
                updatedAt: '2023-02-17T17:12:26.000Z'
              }
            }
          ]
        }
      },
      {
        id: 36,
        admin_id: 503,
        role_id: 121,
        email: '<EMAIL>',
        code: 'aiyRi1VuMZck',
        expiry_date: '2023-01-30T10:48:37.000Z',
        status: 'accepted',
        user_status: 'active',
        user_status_description: null,
        createdAt: '2023-01-29T15:21:57.000Z',
        updatedAt: '2023-01-29T15:24:28.000Z',
        deletedAt: null,
        user: {
          id: 620,
          first_name: 'Ejiroghene',
          last_name: 'Oki-Peter',
          email: '<EMAIL>',
          last_login: '2023-02-15T11:24:37.000Z',
          adminRoles: [
            {
              id: 122,
              name: 'Dami Test',
              admin_user_role: {
                id: 58,
                user_id: 620,
                role_id: 122,
                createdAt: '2023-02-16T11:04:24.000Z',
                updatedAt: '2023-02-16T11:04:24.000Z'
              }
            }
          ]
        }
      },
      {
        id: 22,
        admin_id: 621,
        role_id: 7,
        email: '<EMAIL>',
        code: 'aiSqjjs5xAUr',
        expiry_date: '2023-01-27T13:27:15.000Z',
        status: 'accepted',
        user_status: 'active',
        user_status_description: null,
        createdAt: '2023-01-27T13:27:15.000Z',
        updatedAt: '2023-01-27T13:27:15.000Z',
        deletedAt: null,
        user: {
          id: 504,
          first_name: 'eniola',
          last_name: 'Olojuola',
          email: '<EMAIL>',
          last_login: '2023-02-19T18:24:21.000Z',
          adminRoles: [
            {
              id: 6,
              name: 'Highest Admin Role',
              admin_user_role: {
                id: 51,
                user_id: 504,
                role_id: 6,
                createdAt: '2023-02-16T09:16:08.000Z',
                updatedAt: '2023-02-16T09:16:08.000Z'
              }
            }
          ]
        }
      },
      {
        id: 21,
        admin_id: 621,
        role_id: 7,
        email: '<EMAIL>',
        code: 'aiVtRJLJ1ywW',
        expiry_date: '2023-01-27T13:27:15.000Z',
        status: 'accepted',
        user_status: 'active',
        user_status_description: 'test',
        createdAt: '2023-01-27T13:27:15.000Z',
        updatedAt: '2023-02-09T11:04:01.000Z',
        deletedAt: null,
        user: {
          id: 503,
          first_name: 'Tunde',
          last_name: 'Osborne',
          email: '<EMAIL>',
          last_login: '2023-02-19T23:04:40.000Z',
          adminRoles: [
            {
              id: 6,
              name: 'Highest Admin Role',
              admin_user_role: {
                id: 48,
                user_id: 503,
                role_id: 6,
                createdAt: '2023-02-15T10:19:16.000Z',
                updatedAt: '2023-02-15T10:19:16.000Z'
              }
            }
          ]
        }
      },
      {
        id: 23,
        admin_id: 621,
        role_id: 7,
        email: '<EMAIL>',
        code: 'aiRcAatwiPFw',
        expiry_date: '2023-01-27T13:27:15.000Z',
        status: 'accepted',
        user_status: 'active',
        user_status_description: null,
        createdAt: '2023-01-27T13:27:15.000Z',
        updatedAt: '2023-01-27T13:27:15.000Z',
        deletedAt: null,
        user: {
          id: 506,
          first_name: 'MAYOWA',
          last_name: 'KAYODE',
          email: '<EMAIL>',
          last_login: '2023-02-17T23:23:54.000Z',
          adminRoles: [
            {
              id: 7,
              name: 'Semi-Highest Admin Role',
              admin_user_role: {
                id: 21,
                user_id: 506,
                role_id: 7,
                createdAt: '2023-01-27T13:27:15.000Z',
                updatedAt: '2023-01-27T13:27:15.000Z'
              }
            }
          ]
        }
      },
      {
        id: 24,
        admin_id: 621,
        role_id: 7,
        email: '<EMAIL>',
        code: 'aiYi3geUxVPw',
        expiry_date: '2023-01-27T13:27:15.000Z',
        status: 'accepted',
        user_status: 'active',
        user_status_description: null,
        createdAt: '2023-01-27T13:27:15.000Z',
        updatedAt: '2023-01-27T13:27:15.000Z',
        deletedAt: null,
        user: {
          id: 508,
          first_name: 'Adeyinka',
          last_name: 'Jokanola',
          email: '<EMAIL>',
          last_login: '2023-01-18T11:59:22.000Z',
          adminRoles: [
            {
              id: 7,
              name: 'Semi-Highest Admin Role',
              admin_user_role: {
                id: 22,
                user_id: 508,
                role_id: 7,
                createdAt: '2023-01-27T13:27:15.000Z',
                updatedAt: '2023-01-27T13:27:15.000Z'
              }
            }
          ]
        }
      },
      {
        id: 25,
        admin_id: 621,
        role_id: 7,
        email: '<EMAIL>',
        code: 'aiPXXuEjNUAL',
        expiry_date: '2023-01-27T13:27:15.000Z',
        status: 'accepted',
        user_status: 'active',
        user_status_description: 'Good coder',
        createdAt: '2023-01-27T13:27:15.000Z',
        updatedAt: '2023-01-27T16:57:43.000Z',
        deletedAt: null,
        user: {
          id: 556,
          first_name: 'Joy',
          last_name: 'Amadikwa',
          email: '<EMAIL>',
          last_login: '2023-02-17T16:29:06.000Z',
          adminRoles: [
            {
              id: 6,
              name: 'Highest Admin Role',
              admin_user_role: {
                id: 23,
                user_id: 556,
                role_id: 6,
                createdAt: '2023-01-27T13:27:15.000Z',
                updatedAt: '2023-01-27T13:27:15.000Z'
              }
            }
          ]
        }
      }
    ]
  }
};

export const mockedUserDetails = {
  data: {
    id: 471,
    email: '<EMAIL>',
    first_name: 'Lewis ',
    last_name: 'Dzeremo ',
    avatar: 'https://lh3.googleusercontent.com/a/AATXAJzUaximvMIc-uFHBV9Nu5ih4RrU-wPZOC26tNOJ=s96-c',
    last_login: '2023-02-17T14:18:57.000Z',
    created_at: '2022-01-10T00:39:11.000Z',
    adminInvitation: {
      id: 73,
      code: 'aiWLJy8GXpDS',
      user_status: 'active',
      status_description: null,
      created_at: '2023-02-17T13:42:57.000Z'
    },
    adminRoles: [
      {
        id: 125,
        name: 'Custom-test test',
        slug: 'internal_admin_custom-test_test',
        type: 'admin',
        category: 'custom',
        created_by: 471,
        permissions: ['pay_ins.view', 'pay_in_details.view'],
        createdAt: '2023-02-09T09:55:07.000Z',
        updatedAt: '2023-02-09T09:55:07.000Z',
        deletedAt: null,
        admin_user_role: {
          id: 61,
          user_id: 471,
          role_id: 125,
          createdAt: '2023-02-17T14:10:46.000Z',
          updatedAt: '2023-02-17T14:10:46.000Z'
        }
      }
    ],
    adminPermissions: [
      {
        id: 5979,
        user_id: 471,
        permission_id: 1,
        allowed: false,
        createdAt: '2023-02-19T12:54:49.000Z',
        updatedAt: '2023-02-19T12:54:49.000Z',
        permissionId: 1,
        userId: 471,
        permission: {
          id: 1,
          name: 'View dashboard',
          slug: 'dashboard.view',
          type: 'admin',
          entity: 'dashboard',
          description: 'This grants the permission for an administrator to access the dashboard page',
          createdAt: '2023-01-21T11:27:22.000Z',
          updatedAt: '2023-01-25T12:35:43.000Z',
          deletedAt: null
        }
      },
      {
        id: 5964,
        user_id: 471,
        permission_id: 29,
        allowed: true,
        createdAt: '2023-02-17T14:00:00.000Z',
        updatedAt: '2023-02-17T14:00:00.000Z',
        permissionId: 29,
        userId: 471,
        permission: {
          id: 29,
          name: 'View Pay-ins List',
          slug: 'pay_ins.view',
          type: 'admin',
          entity: 'pay-ins',
          description: 'This grants the permission for an administrator to view payins list',
          createdAt: '2023-01-21T11:27:22.000Z',
          updatedAt: '2023-01-25T12:35:43.000Z',
          deletedAt: null
        }
      },
      {
        id: 5962,
        user_id: 471,
        permission_id: 30,
        allowed: true,
        createdAt: '2023-02-17T13:55:55.000Z',
        updatedAt: '2023-02-17T13:55:55.000Z',
        permissionId: 30,
        userId: 471,
        permission: {
          id: 30,
          name: 'Export pay-ins list',
          slug: 'pay_ins.export',
          type: 'admin',
          entity: 'pay-ins',
          description: 'This grants the permission for an administrator to export payins list',
          createdAt: '2023-01-21T11:27:22.000Z',
          updatedAt: '2023-01-25T12:35:43.000Z',
          deletedAt: null
        }
      },
      {
        id: 5963,
        user_id: 471,
        permission_id: 31,
        allowed: true,
        createdAt: '2023-02-17T13:57:41.000Z',
        updatedAt: '2023-02-17T13:57:41.000Z',
        permissionId: 31,
        userId: 471,
        permission: {
          id: 31,
          name: 'View pay-in details',
          slug: 'pay_in_details.view',
          type: 'admin',
          entity: 'pay-ins',
          description: 'This grants the permission for an administrator to view a payins details',
          createdAt: '2023-01-21T11:27:22.000Z',
          updatedAt: '2023-01-25T12:35:43.000Z',
          deletedAt: null
        }
      },
      {
        id: 5967,
        user_id: 471,
        permission_id: 32,
        allowed: true,
        createdAt: '2023-02-17T14:02:26.000Z',
        updatedAt: '2023-02-17T14:02:26.000Z',
        permissionId: 32,
        userId: 471,
        permission: {
          id: 32,
          name: 'View payouts list',
          slug: 'payouts.view',
          type: 'admin',
          entity: 'payouts',
          description: 'This grants the permission for an administrator to view a payout list',
          createdAt: '2023-01-21T11:27:22.000Z',
          updatedAt: '2023-01-25T12:35:43.000Z',
          deletedAt: null
        }
      },
      {
        id: 5966,
        user_id: 471,
        permission_id: 33,
        allowed: true,
        createdAt: '2023-02-17T14:02:26.000Z',
        updatedAt: '2023-02-17T14:02:26.000Z',
        permissionId: 33,
        userId: 471,
        permission: {
          id: 33,
          name: 'Export payouts list',
          slug: 'payouts.export',
          type: 'admin',
          entity: 'payouts',
          description: 'This grants the permission for an administrator to export a payout list',
          createdAt: '2023-01-21T11:27:22.000Z',
          updatedAt: '2023-01-25T12:35:43.000Z',
          deletedAt: null
        }
      },
      {
        id: 5965,
        user_id: 471,
        permission_id: 34,
        allowed: true,
        createdAt: '2023-02-17T14:02:26.000Z',
        updatedAt: '2023-02-17T14:02:26.000Z',
        permissionId: 34,
        userId: 471,
        permission: {
          id: 34,
          name: 'View payout details',
          slug: 'payout_details.view',
          type: 'admin',
          entity: 'payouts',
          description: 'This grants the permission for an administrator to view a payout details',
          createdAt: '2023-01-21T11:27:22.000Z',
          updatedAt: '2023-01-25T12:35:43.000Z',
          deletedAt: null
        }
      },
      {
        id: 5978,
        user_id: 471,
        permission_id: 41,
        allowed: true,
        createdAt: '2023-02-17T14:17:39.000Z',
        updatedAt: '2023-02-17T14:17:39.000Z',
        permissionId: 41,
        userId: 471,
        permission: {
          id: 41,
          name: 'View settlement details',
          slug: 'settlement_details.view',
          type: 'admin',
          entity: 'settlements',
          description: 'This grants the permission for an administrator to view a settlement details',
          createdAt: '2023-01-21T11:27:22.000Z',
          updatedAt: '2023-01-25T12:35:43.000Z',
          deletedAt: null
        }
      },
      {
        id: 5969,
        user_id: 471,
        permission_id: 42,
        allowed: true,
        createdAt: '2023-02-17T14:02:26.000Z',
        updatedAt: '2023-02-17T14:02:26.000Z',
        permissionId: 42,
        userId: 471,
        permission: {
          id: 42,
          name: 'View settlement payouts list',
          slug: 'settlement_payouts.view',
          type: 'admin',
          entity: 'settlement_payouts',
          description: 'This grants the permission for an administrator to view settlement payout list',
          createdAt: '2023-01-21T11:27:22.000Z',
          updatedAt: '2023-01-25T12:35:43.000Z',
          deletedAt: null
        }
      },
      {
        id: 5968,
        user_id: 471,
        permission_id: 43,
        allowed: true,
        createdAt: '2023-02-17T14:02:26.000Z',
        updatedAt: '2023-02-17T14:02:26.000Z',
        permissionId: 43,
        userId: 471,
        permission: {
          id: 43,
          name: 'Export settlement payouts',
          slug: 'settlement_payouts.export',
          type: 'admin',
          entity: 'settlement_payouts',
          description: 'This grants the permission for an administrator to export settlement payout list',
          createdAt: '2023-01-21T11:27:22.000Z',
          updatedAt: '2023-01-25T12:35:43.000Z',
          deletedAt: null
        }
      }
    ]
  }
};
export const mockedBankData = {
  data: [
    {
      name: ' AL-Barakah Microfinance Bank',
      slug: 'al-barakah-mfb',
      code: '090133',
      nibss_bank_code: '090133',
      country: 'NG'
    },
    {
      name: '9 Payment Service Bank',
      slug: '9payment',
      code: '000802',
      nibss_bank_code: '000802',
      country: 'NG'
    },
    {
      name: 'AB Microfinance Bank',
      slug: 'ab-mfb',
      code: '090270',
      nibss_bank_code: '090270',
      country: 'NG'
    },
    {
      name: 'Abbey Mortgage Bank',
      slug: 'abbey-mb',
      code: '070010',
      nibss_bank_code: '070010',
      country: 'NG'
    },
    {
      name: 'Above Only Microfinance Bank',
      slug: 'above-only-mfb',
      code: '090260',
      nibss_bank_code: '090260',
      country: 'NG'
    }
  ]
};
export const mockedCurrenciesExchangeRate = {
  data: {
    data: [
      {
        source_currency: 'NGN',
        destination_currency: 'USD',
        exchange_rate: '1021.70',
        source_amount: '34500.00',
        service_charge: '5175.00',
        converted_amount: '33.77',
        status: 'success',
        reference: 'KPY-SWPcjsiprCOWi',
        transaction_date: '2023-10-09 12:40:05',
        provider: 'shutterscore',
        type: 'wallet_swap',
        account: {
          name: 'LANJ',
          email: '<EMAIL>'
        }
      },
      {
        source_currency: 'USD',
        destination_currency: 'NGN',
        exchange_rate: '1014.25',
        source_amount: '55.00',
        service_charge: '5.50',
        converted_amount: '55783.75',
        status: 'success',
        reference: 'KPY-SWPRNqj4xriDw',
        transaction_date: '2023-10-09 12:36:25',
        provider: 'shutterscore',
        type: 'wallet_swap',
        account: {
          name: 'LANJ',
          email: '<EMAIL>'
        }
      },
      {
        source_currency: 'USD',
        destination_currency: 'NGN',
        exchange_rate: '1014.25',
        source_amount: '51.12',
        service_charge: '5.11',
        converted_amount: '51848.46',
        status: 'success',
        reference: 'KPY-SWPiTRGvmIZ8t',
        transaction_date: '2023-10-09 12:35:57',
        provider: 'shutterscore',
        type: 'wallet_swap',
        account: {
          name: 'LANJ',
          email: '<EMAIL>'
        }
      },
      {
        source_currency: 'USD',
        destination_currency: 'NGN',
        exchange_rate: '1014.10',
        source_amount: '51.50',
        service_charge: '5.15',
        converted_amount: '52226.15',
        status: 'success',
        reference: 'KPY-SWPkzFgeW13h5',
        transaction_date: '2023-10-09 12:33:50',
        provider: 'shutterscore',
        type: 'wallet_swap',
        account: {
          name: 'LANJ',
          email: '<EMAIL>'
        }
      }
    ],
    paging: {
      total_items: 4,
      page_size: 10,
      current: 1,
      count: 4,
      next: 2
    },
    links: [
      {
        href: 'https://api.koraapi.com/merchant/api/admin/wallets/swaps/transactions?page=1',
        rel: 'current',
        method: 'GET'
      }
    ]
  }
};
export const mockedCurrenciesData = {
  status: true,
  message: 'Currencies retrieved successfully',
  data: [
    {
      id: 1,
      name: 'Nigerian Naira',
      code: 'NGN',
      enabled: true,
      meta_data: {
        payment_types: {
          payin: [
            {
              label: 'Card Payment',
              value: 'card'
            },
            {
              label: 'Pay with Bank Transfer',
              value: 'bank_transfer'
            },
            {
              label: 'Virtual Bank Account',
              value: 'virtual_bank_account'
            },
            {
              label: 'Pay With Bank',
              value: 'pay_with_bank'
            }
          ],
          payout: [
            {
              label: 'Bank Account',
              value: 'bank_account'
            },
            {
              label: 'Korapay Wallet',
              value: 'disbursement_wallet'
            }
          ]
        },
        settlement_destinations: [
          {
            label: 'Settlement Account',
            value: 'settlement_account'
          },
          {
            label: 'Korapay Wallet',
            value: 'disbursement_wallet'
          }
        ]
      }
    },
    {
      id: 2,
      name: 'United States Dollar',
      code: 'USD',
      enabled: true,
      meta_data: {
        payment_types: {
          payin: [
            {
              label: 'Card Payment',
              value: 'card'
            }
          ],
          payout: [
            {
              label: 'Korapay Wallet',
              value: 'disbursement_wallet'
            }
          ]
        },
        settlement_destinations: [
          {
            label: 'Korapay Wallet',
            value: 'disbursement_wallet'
          }
        ]
      }
    },
    {
      id: 3,
      name: 'Euro',
      code: 'EUR',
      enabled: false,
      meta_data: {
        payment_types: {
          payin: [],
          payout: []
        }
      }
    },
    {
      id: 4,
      name: 'Kenyan Shilling',
      code: 'KES',
      enabled: true,
      meta_data: {
        payment_types: {
          payin: [
            {
              label: 'Card Payment',
              value: 'card'
            },
            {
              label: 'Mobile Money',
              value: 'mobile_money'
            }
          ],
          payout: [
            {
              label: 'Bank Account',
              value: 'bank_account'
            },
            {
              label: 'Mobile Money',
              value: 'mobile_money'
            },
            {
              label: 'Korapay Wallet',
              value: 'disbursement_wallet'
            }
          ]
        },
        settlement_destinations: [
          {
            label: 'Korapay Wallet',
            value: 'disbursement_wallet'
          }
        ]
      }
    },
    {
      id: 5,
      name: 'Ghanaian Cedi',
      code: 'GHS',
      enabled: true,
      meta_data: {
        payment_types: {
          payin: [
            {
              label: 'Card Payment',
              value: 'card'
            }
          ],
          payout: [
            {
              label: 'Mobile Money',
              value: 'mobile_money'
            }
          ]
        },
        settlement_destinations: [
          {
            label: 'Korapay Wallet',
            value: 'disbursement_wallet'
          }
        ]
      }
    }
  ]
};

export const mockedCountriesData = {
  data: [
    {
      id: 25,
      name: 'Benin',
      iso2: 'BJ',
      iso3: 'BEN',
      phone_code: '229'
    },
    {
      id: 65,
      name: 'Egypt',
      iso2: 'EG',
      iso3: 'EGY',
      phone_code: '20'
    },
    {
      id: 82,
      name: 'Ghana',
      iso2: 'GH',
      iso3: 'GHA',
      phone_code: '233'
    },
    {
      id: 165,
      name: 'Nigeria',
      iso2: 'NG',
      iso3: 'NGA',
      phone_code: '234'
    },
    {
      id: 2,
      name: 'United Arab Emirates',
      iso2: 'AE',
      iso3: 'ARE',
      phone_code: '971'
    },
    {
      id: 77,
      name: 'United Kingdom',
      iso2: 'GB',
      iso3: 'GBR',
      phone_code: '44'
    },
    {
      id: 234,
      name: 'United States',
      iso2: 'US',
      iso3: 'USA',
      phone_code: '1'
    }
  ]
};

export const mockChargebackHistoryData = {
  data: {
    data: [
      {
        account_id: 105,
        processor_reference: 'isc-w9CAUfi-ext',
        processor: 'interswitch',
        reference: 'KPY-CHG-9qWflyGluWwPvBS',
        payment_reference: 'KPY-PAY-SCYgHyBIDchn',
        payment_source_reference: 'KPY-CM-vrU7V4iwgC8h',
        batch_code: 'KPY-CHG-BTCH-aABn4GVFjaCJsOu',
        status: 'accepted',
        currency: 'NGN',
        amount: '50.00',
        approved_amount: '50.00',
        deadline: '2023-06-09T15:33:00.000Z',
        payment_method: 'card',
        payment_reversal_reference: 'KPY-CHB-IaQuZSTWyKifu',
        log_code: null,
        reason: 'nope',
        merchant: 'Tuns',
        merchant_email: '<EMAIL>',
        created_at: '2023-06-08 15:33:08',
        account: {
          name: 'Tuns',
          email: '<EMAIL>'
        }
      },
      {
        account_id: 96,
        processor_reference: 'kpy-wt-othTosJlaKkoVFrS',
        processor: 'korapay',
        reference: 'KPY-CHG-a8tO9VZ9e8PlC98',
        payment_reference: 'KPY-PAY-EIQbq0hKq2FyJYem',
        payment_source_reference: 'KPY-CW-VbgBFnZinot0RRph',
        batch_code: 'KPY-CHG-BTCH-ZLDjUHVE5FUdVSf',
        status: 'pending',
        currency: 'NGN',
        amount: '1000.00',
        approved_amount: '0.00',
        deadline: '2023-06-06T09:58:00.000Z',
        payment_method: 'disbursement_wallet',
        payment_reversal_reference: null,
        log_code: null,
        reason: 'tahahj',
        merchant: 'damiworkTest',
        merchant_email: '<EMAIL>',
        created_at: '2023-06-05 10:58:16',
        account: {
          name: 'damiworkTest',
          email: '<EMAIL>'
        }
      },
      {
        account_id: 385,
        processor_reference: 'NmvXx41T',
        processor: 'test_bank_transfer',
        reference: 'KPY-CHG-cBu87lq5xqZO4zj',
        payment_reference: 'KPY-PAY-Qzij0ax2cBET',
        payment_source_reference: 'KPY-CM-A9CFmjtCxLHW',
        batch_code: 'KPY-CHG-BTCH-qjWq1IVDtS8zgPb',
        status: 'pending',
        currency: 'NGN',
        amount: '20000.00',
        approved_amount: '0.00',
        deadline: '2023-06-16T11:23:00.000Z',
        payment_method: 'bank_transfer',
        payment_reversal_reference: null,
        log_code: null,
        reason: 'a goat',
        merchant: 'Oladejooo',
        merchant_email: '<EMAIL>',
        created_at: '2023-06-04 03:18:05',
        account: {
          name: 'Oladejooo',
          email: '<EMAIL>'
        }
      },
      {
        account_id: 96,
        processor_reference: 'kpy-wt-bZ2KOv8sgz1UvLmc',
        processor: 'korapay',
        reference: 'KPY-CHG-fdVgyoigPUCC5VW',
        payment_reference: 'KPY-PAY-5RqdXVyQg9LISTik',
        payment_source_reference: 'KPY-CW-wv6vqf7ucpnsmWwH',
        batch_code: 'KPY-CHG-BTCH-eoU3uLwZgcASVO6',
        status: 'pending',
        currency: 'NGN',
        amount: '1000.00',
        approved_amount: '0.00',
        deadline: '2023-06-22T11:23:00.000Z',
        payment_method: 'disbursement_wallet',
        payment_reversal_reference: null,
        log_code: null,
        reason: 'looll',
        merchant: 'damiworkTest',
        merchant_email: '<EMAIL>',
        created_at: '2023-06-04 03:03:03',
        account: {
          name: 'damiworkTest',
          email: '<EMAIL>'
        }
      },
      {
        account_id: 385,
        processor_reference: 'WEsxeEIj',
        processor: 'test_bank_transfer',
        reference: 'KPY-CHG-0dJPyjl0vhgvAss',
        payment_reference: 'KPY-PAY-ovU6eoKwajGj',
        payment_source_reference: 'KPY-CM-Nii9I4TiMWRs',
        batch_code: 'KPY-CHG-BTCH-hjOv9rTL4Z3BZqh',
        status: 'pending',
        currency: 'NGN',
        amount: '50000.00',
        approved_amount: '0.00',
        deadline: '2023-06-15T11:23:00.000Z',
        payment_method: 'bank_transfer',
        payment_reversal_reference: null,
        log_code: null,
        reason: 'looooll',
        merchant: 'Oladejooo',
        merchant_email: '<EMAIL>',
        created_at: '2023-06-04 02:09:17',
        account: {
          name: 'Oladejooo',
          email: '<EMAIL>'
        }
      }
    ]
  }
};

export const mockChargebackSummaryData = {
  data: {
    pending_chargebacks: 7,
    chargebacks_resolved_today: 0
  }
};

export const mockedChargebackData = {
  processor: 'test_bank_transfer',
  currency: 'NGN',
  deadline: '12/02/2021',
  chargebacks: [
    {
      id: '1',
      processor_reference: 'kp_123456789',
      amount: 2000,
      reason: 'below value'
    },
    {
      id: '2',
      processor_reference: 'kp_12335689',
      amount: 40000,
      reason: 'no value gotten'
    },
    {
      id: '3',
      processor_reference: 'kp_165335689',
      amount: 50000,
      reason: 'got some of the value'
    },
    {
      id: '4',
      processor_reference: 'kp_565335689',
      amount: ********,
      reason: 'transaction doesnt exist'
    }
  ]
};

export const mockedChargebackDataWithEmpty = {
  processor: 'test_bank_transfer',
  currency: 'NGN',
  deadline: '12/02/2021',
  chargebacks: [
    {
      id: '1',
      processor_reference: 'kp_123456789',
      amount: 2000,
      reason: 'below value'
    },
    {
      id: '2',
      processor_reference: 'kp_12335689',
      amount: 40000,
      reason: 'no value gotten'
    },
    {
      id: '3',
      processor_reference: 'kp_165335689',
      amount: 50000,
      reason: 'got some of the value'
    },
    {
      id: '4',
      processor_reference: 'kp_565335689',
      amount: null,
      reason: 'transaction doesnt exist'
    }
  ]
};

export const exchangeMarkup = {
  data: [
    {
      data: {
        reference: 'KPY-CW-N7jMjG0F9okcHlOS',
        status: 'success',
        wallet_currency: 'NGN',
        rate: {
          last_rate: '2%',
          date_set: '30th October 12:30pm',
          new_rate: '1.5%',
          kora_rate: '1 USD - 1,030 NGN'
        }
      }
    }
  ]
};
export const mockedMerchantsData = {
  data: [
    {
      id: 545,
      risk_level: 'medium_risk',
      name: 'wecook',
      created_at: '2023-08-25T14:21:15.000Z',
      settable_id: 545,
      status: 'true',
      configuration_type: 'DEFAULT',
      setting: {
        value: {
          version2: {
            KES: {
              enabled: true
            }
          }
        }
      }
    },
    {
      id: 544,
      risk_level: 'medium_risk',
      name: 'Testering',
      created_at: '2023-08-24T21:06:27.000Z',
      settable_id: null,
      status: 'true',
      configuration_type: 'DEFAULT',
      setting: null
    },
    {
      id: 543,
      risk_level: 'medium_risk',
      name: 'KeOnboarding',
      created_at: '2023-08-24T21:01:31.000Z',
      settable_id: 543,
      status: 'true',
      configuration_type: 'DEFAULT',
      setting: {
        value: {
          version2: {
            KES: {
              enabled: true
            }
          }
        }
      }
    },
    {
      id: 542,
      risk_level: 'medium_risk',
      name: 'TestNigeria',
      created_at: '2023-08-24T20:49:34.000Z',
      settable_id: null,
      status: 'true',
      configuration_type: 'DEFAULT',
      setting: null
    },
    {
      id: 541,
      risk_level: 'medium_risk',
      name: 'Nigeria',
      created_at: '2023-08-24T13:57:04.000Z',
      settable_id: 541,
      status: 'true',
      configuration_type: 'CUSTOM',
      setting: {
        value: {
          version2: {
            GHS: {
              enabled: true
            }
          }
        }
      }
    }
  ],
  paging: {
    total_items: 535,
    page_size: 25,
    current: 1,
    count: 25,
    next: 2
  }
};

export const mockedCurrencyConfigData = {
  setting: {
    enabled: true,
    settlement: {
      cycle: {
        card: {
          low_risk: 1,
          high_risk: 14,
          medium_risk: 7,
          above_average_risk: 10
        },
        mobile_money: {
          low_risk: 1,
          high_risk: 14,
          medium_risk: 7,
          above_average_risk: 10
        }
      },
      destination: 'disbursement_wallet',
      rolling_reserve: {
        card: {
          low_risk: {
            rate: 10,
            period: 180
          },
          high_risk: {
            rate: 20,
            period: 180
          },
          medium_risk: {
            rate: 15,
            period: 180
          },
          above_average_risk: {
            rate: 20,
            period: 180
          }
        }
      }
    },
    transaction: {
      collection: {
        enabled: false,
        mobile_money: {
          enabled: false,
          channels: ['modal'],
          transaction_limit: {
            max: ********,
            min: 10
          }
        }
      },
      disbursement: {
        enabled: true,
        limits: {
          web: {
            daily: {
              settlement_account: 100000,
              non_settlement_account: 80000
            },
            per_transaction: {
              settlement_account: { min: 100, max: 1000 },
              non_settlement_account: { min: 200, max: 900 }
            }
          },
          api: { per_transaction: { min: 50, max: 500 } },
          global: { daily: 200000 }
        },
        mobile_money: {
          enabled: false,
          channels: ['api', 'web'],
          transaction_limit: {
            max: 150000,
            min: 10
          }
        },
        disbursement_wallet: {
          enabled: true,
          channels: ['web'],
          transaction_limit: {
            max: 1**********,
            min: 100
          },
          can_send_to_any_merchant: false
        }
      }
    }
  }
};

export const mockedMerchantConfigDetails = {
  setting: {
    enabled: true,
    settlement: {
      cycle: {
        card: {
          low_risk: 1,
          high_risk: 14,
          medium_risk: 7,
          above_average_risk: 10
        },
        mobile_money: {
          low_risk: 1,
          high_risk: 14,
          medium_risk: 7,
          above_average_risk: 10
        }
      },
      destination: 'disbursement_wallet',
      rolling_reserve: {
        card: {
          low_risk: {
            rate: 10,
            period: 180
          },
          high_risk: {
            rate: 20,
            period: 180
          },
          medium_risk: {
            rate: 15,
            period: 180
          },
          above_average_risk: {
            rate: 20,
            period: 180
          }
        }
      }
    },
    transaction: {
      collection: {
        enabled: true,
        mobile_money: {
          enabled: true,
          channels: ['modal', 'web', 'api'],
          transaction_limit: {
            max: ********,
            min: 1000
          }
        }
      },
      disbursement: {
        enabled: true,
        limits: {
          web: {
            daily: {
              settlement_account: 100000,
              non_settlement_account: 80000
            },
            per_transaction: {
              settlement_account: { min: 100, max: 1000 },
              non_settlement_account: { min: 200, max: 900 }
            }
          },
          api: { per_transaction: { min: 50, max: 500 } },
          global: { daily: 200000 }
        },
        mobile_money: {
          enabled: false,
          channels: ['api', 'web'],
          transaction_limit: {
            max: 150000,
            min: 10
          }
        },
        disbursement_wallet: {
          enabled: true,
          channels: ['web'],
          transaction_limit: {
            max: 1**********,
            min: 100
          },
          can_send_to_any_merchant: false
        }
      }
    }
  },
  account: {
    risk_level: 'medium_risk',
    name: 'Doctest',
    created_at: '2023-08-31T19:26:19.000Z',
    tier_level_id: 5
  },
  currencies: ['GHS', 'NGN', 'ZAR']
};
export const mockedEmailConfigurations = {
  status: true,
  message: 'Notification configuration retrieved successfully',
  data: {
    id: 14,
    merchant_id: 44,
    configuration: {
      payin: {
        support_email: ['<EMAIL>'],
        all_payins: {
          enabled: true,
          email_recipients: ['<EMAIL>', '<EMAIL>']
        },
        other_payin_emails: {
          enabled: true,
          email_recipients: ['<EMAIL>']
        }
      },
      payout: {
        support_email: ['<EMAIL>'],
        api_bulk_payout_breakdown: {
          enabled: false,
          email_recipients: ['<EMAIL>']
        },
        api_bulk_payout_initiation: {
          enabled: true,
          email_recipients: ['<EMAIL>']
        },
        api_single_payout_completion: {
          enabled: false,
          email_recipients: ['<EMAIL>']
        },
        dashboard_single_payout_completion: {
          enabled: true,
          email_recipients: ['<EMAIL>']
        }
      },
      dispute: {
        refund: {
          enabled: true,
          email_recipients: ['<EMAIL>', '<EMAIL>']
        },
        chargeback: {
          enabled: true,
          email_recipients: ['<EMAIL>', '<EMAIL>']
        }
      },
      card_issuance: {
        transaction_limits: {
          enabled: true,
          email_recipients: ['<EMAIL>', '<EMAIL>']
        },
        issuing_balance_funding: {
          enabled: true,
          email_recipients: ['<EMAIL>', '<EMAIL>']
        },
        virtual_card_termination: {
          enabled: true,
          email_recipients: ['<EMAIL>', '<EMAIL>']
        }
      },
      product_and_marketing: {
        enabled: true,
        email_recipients: ['<EMAIL>', '<EMAIL>']
      }
    },
    createdAt: '2023-07-13T11:25:10.000Z',
    updatedAt: '2023-07-13T13:26:36.000Z'
  }
};

export const mockedTeamMembers = {
  data: [
    {
      email: '<EMAIL>',
      user_id: 846,
      role_id: 2,
      completed: 1,
      expires_at: '2023-03-24T15:32:12.000Z',
      userRole: {
        id: 2,
        name: 'Administrator',
        slug: 'admin',
        permissions: {
          team: 'manage',
          payin: 'export',
          payout: 'export',
          refund: 'manage'
        }
      },
      user: {
        first_name: 'Emmanuel',
        last_name: 'Osuh',
        last_login: '2023-09-25 11:04:55',
        two_factor_enabled: 0,
        status: 'locked'
      },
      status: 'invite_accepted'
    },
    {
      email: '<EMAIL>',
      user_id: 482,
      role_id: 1,
      userRole: {
        id: 1,
        name: 'Owner',
        slug: 'owner',
        permissions: {
          team: 'manage',
          payin: 'export',
          payout: 'export',
          refund: 'manage',
          balance: 'manage'
        }
      },
      user: {
        first_name: 'Lynda ',
        last_name: 'Nwakor ',
        last_login: '2023-12-03 16:33:48',
        two_factor_enabled: 0
      },
      status: 'active'
    }
  ]
};
export const mockedTransactionDetailsData = {
  status: true,
  message: 'Transaction retrieved successfully',
  data: {
    reference: 'd3xXH8FNthokMYi',
    status: 'success',
    amount: '1000.00',
    amount_charged: '1000.00',
    amount_paid: '1000.00',
    amount_collected: '1000.00',
    fee: '0.00',
    vat: '0.00',
    narration: 'TEST',
    payment_source_type: 'bank_transfer',
    payment_source_id: 4042,
    payment_id: 28860,
    currency: 'NGN',
    channel: 'modal',
    payment_reversals_type: 1,
    meta: null,
    message: 'Successful',
    merchant_bears_cost: true,
    processor: 'wema',
    processor_reference: '8teSEFPFfqkvQzf',
    transaction_date: '2023-09-29 12:58:57',
    completed_at: '2023-09-29 13:00:42',
    payment: {
      reference: '6KsdgnKp65pK',
      customer: {
        name: 'Example Test',
        email: '<EMAIL>'
      },
      account: {
        id: 96,
        name: 'Test1'
      }
    },
    payment_reversals: [
      {
        id: 1090,
        reference: 'izNbx2REoxqO',
        destination: null,
        reversal_reason: 'Unfulfilled Request',
        status: 'success',
        amount: '100.00',
        type: 'refund',
        created_at: '2023-09-29 13:01:23',
        completed_at: '2023-09-29 13:01:23',
        payment_reversal_payouts: []
      }
    ],
    source: {
      type: 'bank_transfer',
      details: {
        bank_name: 'UBA',
        account_number: '**********',
        account_name: 'Testing Test'
      },
      virtual_bank_account: {
        bank_name: 'wema',
        account_number: '**********',
        account_name: 'KPY-DAM',
        expiry: '2023-09-29T12:58:57.000Z'
      }
    },
    can_reverse_payment: true,
    merchant_id: 115
  }
};

export const mockedCardIssuedDetails = {
  data: {
    reference: '293u1zzi-f0cu-567f-a1e0-4vht9OUAc9jtest7',
    kora_id: 497,
    type: 'virtual',
    first_six: '559292',
    last_four: '1811',
    brand: 'mastercard',
    expiry_month: '09',
    expiry_year: '2026',
    provider: 'passpoint',
    provider_reference: 'ba9b67b0-d91b-4177-8822-61dc0892f9c1',
    currency: 'USD',
    balance: 0,
    status: 'active',
    billing: {
      address1: '8 The Green Ste R',
      city: 'Dover County',
      state: 'Delaware',
      country: 'US',
      zip_code: '19901'
    },
    card_holder: {
      first_name: 'Chijioke',
      last_name: 'Agu',
      email: '<EMAIL>',
      address: {
        city: 'Lekki',
        state: 'Lagos',
        street: 'No 14, Kora street',
        country: 'NG',
        zip_code: '101566'
      }
    },
    date_created: '2024-09-04T22:03:27.000Z',
    category: 'customer'
  }
};
export const mockedIssuedMerchantTabQueries = {
  status: true,
  message: 'Transactions retrieved successfully',
  data: {
    data: [1, 2],
    paging: { total_items: 2 },
    links: []
  }
};
export const mockedIssuedMerchantDetails = {
  data: {
    reference: '**********',
    name: 'John Doe',
    status: 'active',
    plan: {
      reference: 'KPY-PL-8DVURkeQv0lhnEn',
      name: 'startup-KPY-MC-Y1tpt50Bnu3zsvq',
      currency: 'USD',
      min_payment_value: '0.00',
      max_payment_value: '800.00',
      reserved_card_min_payment_value: '1.00',
      reserved_card_max_payment_value: '99.99',
      monthly_card_limit: 5000,
      reserved_card_limit: 20,
      card_type: 'virtual',
      fee: {
        customer: {
          funding: {
            type: 'percentage',
            active: true,
            amount: 2.5,
            vat_inclusive: false,
            charge_interval: 'monthly'
          },
          issuance: {
            type: 'flat',
            active: true,
            amount: 1,
            vat_inclusive: false,
            charge_interval: 'once'
          },
          chargeback: {
            type: 'flat',
            active: true,
            amount: 20,
            vat_inclusive: false,
            charge_interval: 'once'
          },
          withdrawal: {
            type: 'flat',
            active: true,
            amount: 1,
            vat_inclusive: false,
            charge_interval: 'monthly'
          },
          subscription: {
            type: 'flat',
            active: true,
            amount: 10,
            vat_inclusive: true,
            charge_interval: 'monthly'
          },
          cross_currency: {
            type: 'percentage',
            active: true,
            amount: 3,
            vat_inclusive: false,
            charge_interval: 'monthly'
          },
          security_reserve: {
            type: 'flat',
            amount: 0,
            vat_inclusive: false,
            charge_interval: 'once'
          }
        },
        reserved: {
          funding: {
            type: 'percentage',
            active: true,
            amount: 2.5,
            vat_inclusive: false,
            charge_interval: 'monthly'
          },
          issuance: {
            type: 'flat',
            active: true,
            amount: 1,
            vat_inclusive: false,
            charge_interval: 'once'
          },
          chargeback: {
            type: 'flat',
            active: true,
            amount: 45,
            vat_inclusive: false,
            charge_interval: 'once'
          },
          withdrawal: {
            type: 'flat',
            active: true,
            amount: 1,
            vat_inclusive: false,
            charge_interval: 'monthly'
          },
          subscription: {
            type: 'flat',
            active: true,
            amount: 100,
            vat_inclusive: true,
            charge_interval: 'monthly'
          },
          cross_currency: {
            type: 'percentage',
            active: true,
            amount: 3,
            vat_inclusive: false,
            charge_interval: 'monthly'
          },
          security_reserve: {
            type: 'flat',
            amount: 0,
            vat_inclusive: false,
            charge_interval: 'once'
          }
        }
      },
      type: 'custom',
      active: true,
      createdAt: '2023-05-29T06:47:11.000Z',
      updatedAt: '2024-09-03T16:08:46.000Z'
    },
    wallet_balance: 200,
    issued_cards: 1,
    reserved_cards: 5,
    kora_id: 123,
    risk_level: 'medium_risk',
    date_created: '2022-08-17T20:50:29.000Z'
  }
};

export const mockedIssuedWalletHistoryData = {
  data: {
    data: [
      {
        amount: '100.00',
        direction: 'credit',
        currency: 'USD',
        reference: 'KPY-UoIPbyw6N1U74Mx',
        balance_after: '7881.00',
        balance_before: '7781.00',
        description: 'Issuing balance funding',
        history_date: '2023-07-12 12:10:35'
      },
      {
        amount: '250.00',
        direction: 'debit',
        currency: 'USD',
        reference: 'KPY-nWHHuM2D4BwNX1i',
        balance_after: '7781.00',
        balance_before: '8031.00',
        description: 'Card creation',
        history_date: '2023-05-31 16:50:44'
      },
      {
        amount: '1.00',
        direction: 'debit',
        currency: 'USD',
        reference: 'KPY-TgdOYcQCB9OhDxp',
        balance_after: '8031.00',
        balance_before: '8032.00',
        description: 'Card creation fee',
        history_date: '2023-05-31 16:50:44'
      }
    ],
    paging: {
      total_items: 3,
      page_size: 25,
      current: 1,
      count: 25,
      next: 2
    }
  }
};

export const mockedBulkTransactionList = {
  data: {
    data: [
      {
        reference: '002_bulktransferAPISanity',
        amount: '100.00',
        currency: 'NGN',
        narration: 'APISanityTest',
        status: 'success',
        batch_reference: 'payout_batch_reference',
        type: 'bank_account',
        customer: {
          name: 'Victor',
          email: '<EMAIL>'
        },
        unique_reference: 'KPY-PO-202401090923dTU4J858407',
        transaction_date: '2024-01-09 10:23:58',
        bank_account: {
          bank_code: '940',
          account_number: '**********',
          account_name: 'victor  nwauwa'
        }
      },
      {
        reference: '001_bulktransferAPISanity',
        amount: '100.00',
        currency: 'NGN',
        narration: 'APISanityTest',
        status: 'success',
        batch_reference: 'payout_batch_reference',
        type: 'bank_account',
        customer: {
          name: 'Promise',
          email: '<EMAIL>'
        },
        unique_reference: 'KPY-PO-202401090923rFnvrx58421',
        transaction_date: '2024-01-09 10:23:58',
        bank_account: {
          bank_code: '214',
          account_number: '**********',
          account_name: 'ELIMINHELE PROMISE DEBORAH'
        }
      },
      {
        reference: '003_bulktransferAPISanity',
        amount: '100.00',
        currency: 'NGN',
        narration: 'APISanityTest',
        status: 'failed',
        batch_reference: 'payout_batch_reference',
        type: 'bank_account',
        customer: {
          name: 'lynda',
          email: '<EMAIL>'
        },
        unique_reference: 'KPY-PO-202401090923tNf4Ex58437',
        transaction_date: '2024-01-09 10:23:58',
        bank_account: {
          bank_code: null,
          account_number: null,
          account_name: null
        }
      }
    ],
    paging: {
      total_items: 3,
      page_size: 10,
      current: 1,
      count: 3
    },
    links: [
      {
        href: 'https://api.koraapi.com/merchant/api/admin/transactions/bulk/KPY-BPO-202401090923hHt1xX58344/payouts?currency=NGN&page=1',
        rel: 'current',
        method: 'GET'
      }
    ]
  }
};

export const mockedBulkSummary = {
  data: {
    amount: '2000.00',
    total_bulk_amount: 2053.75,
    currency: 'NGN',
    reference: 'KPY-BPO-2024013117543PZ80411375',
    status: 'draft',
    type: 'bank_account',
    description: 'testing failed repeat',
    payouts: [
      {
        amount: 2000,
        errors: [],
        customer: {
          name: 'Test Account',
          email: '<EMAIL>'
        },
        narration: 'Test transfer to F4B Developers',
        reference: 'KPY-DD-EDllQljG40PGC8u',
        bank_account: {
          valid: true,
          bank_code: '033',
          bank_name: 'United Bank for Africa',
          account_name: 'Test Account',
          account_number: '**********'
        }
      }
    ],
    payout_count: 1,
    validation_complete: true,
    validPayoutsCount: 1,
    invalidPayoutsCount: 0,
    unique_reference: 'KPY-BPO-2024013117543PZ80411375',
    channel: 'web',
    account: {
      name: 'Ugo Demo',
      email: '<EMAIL>'
    }
  }
};

export const mockedCustomerCardsData = {
  data: {
    data: [
      {
        reference: '226eeb6f-f6cb-562f-a5e8-4uht91dechhjyabc',
        type: 'virtual',
        currency: 'USD',
        first_six: '222329',
        last_four: '9283',
        status: 'active',
        holder_name: 'sapphire Sanders',
        merchant_name: 'Kora HQ Developer',
        brand: 'visa',
        expiry_month: '08',
        expiry_year: '2028',
        date_created: '2023-08-22T10:50:19.000Z'
      }
    ],
    paging: {
      total_items: 3,
      page_size: 25,
      current: 1,
      count: 25,
      next: 2
    }
  }
};
export const mockedReservedCardsData = {
  data: {
    data: [
      {
        reference: 'RVC-xdxEuuVApVXNB8zn6usOjhyuCpkqCNeRDpJb',
        type: 'virtual',
        currency: 'USD',
        first_six: '404038',
        last_four: '1006',
        status: 'active',
        holder_name: 'Kora HQ-Developer',
        merchant_name: 'Kora HQ Developer',
        brand: 'visa',
        expiry_month: '12',
        expiry_year: '2025',
        label: 'Test',
        date_created: '2023-12-22T15:06:11.000Z'
      },
      {
        reference: 'RVC-y4UdWvDMAEwjZNRx00OWEVTd68mSpJaguLJF',
        type: 'virtual',
        currency: 'USD',
        first_six: '519075',
        last_four: '8066',
        status: 'terminated',
        holder_name: 'Kora HQ-Developer',
        merchant_name: 'Kora HQ Developer',
        brand: 'mastercard',
        expiry_month: '12',
        expiry_year: '2025',
        label: 'my card',
        date_created: '2023-12-15T15:35:06.000Z'
      }
    ],
    paging: {
      total_items: 3,
      page_size: 25,
      current: 1,
      count: 25,
      next: 2
    }
  }
};
export const mockedCardTransactionsData = {
  data: {
    data: [
      {
        reference: 'TX-imAkmujI7hlZfNLGzlAFVB0JT7SLkzWOpfTeQ',
        amount: 20,
        fee: 0,
        currency: 'USD',
        cross_currency: false,
        card_holder_name: 'Kora HQ-Developer',
        description: 'card funding',
        balance_after: 60,
        type: 'card_funding',
        status: 'success',
        date: '2023-12-28T15:48:50.000Z'
      },
      {
        reference: 'TX-x95cjRARWuzez9ToaZxncdyUAxrrc3QyXS6uO',
        amount: 20,
        fee: 0,
        currency: 'USD',
        cross_currency: false,
        card_holder_name: 'Kora HQ-Developer',
        description: 'card funding',
        balance_after: 40,
        type: 'card_funding',
        status: 'success',
        date: '2023-12-27T16:24:51.000Z'
      },
      {
        reference: 'TX-Ir18OycXM4bR4eOYpE2hUvimdkWEz3ZPSlU5Q',
        amount: 20,
        fee: 0,
        currency: 'USD',
        cross_currency: false,
        card_holder_name: 'Kora HQ-Developer',
        description: 'card funding',
        balance_after: 20,
        type: 'card_funding',
        status: 'success',
        date: '2023-12-27T16:24:17.000Z'
      }
    ],
    paging: {
      total_items: 3,
      page_size: 25,
      current: 1,
      count: 25,
      next: 2
    }
  }
};
export const mockedIssuanceChargebacksData = {
  data: {
    data: [
      {
        reference: 'CHB-7FUpdofxMiOar4pqAdzNyzVovzOOoTC4xvt7',
        status: 'pending',
        amount: '20.00',
        card_acceptor_name: null,
        escalation_date: '2024-01-08T15:06:55.000Z',
        card_holder_name: 'sapphire Sanders',
        actual_resolution_date: null,
        processing_date: null,
        transaction_reference: 'TX-VBv8FfzW6HLn5vky0pDGu4jRymWYYd4Ryhy7y',
        transaction_amount: '20.00',
        transaction_type: 'card_transaction',
        transaction_date: '2023-10-05T17:14:32.000Z',
        card_type: 'virtual',
        first_six: '222329',
        last_four: '9283',
        card_expiry_date: '2028-08-31T23:59:59.000Z',
        card_brand: 'visa'
      },
      {
        reference: 'CHB-UCb4epj4Je1NjfpVgZnGhaC7pwzMYtLnFnNT',
        status: 'declined',
        amount: '115.34',
        card_acceptor_name: 'Viola Inc.',
        escalation_date: '2023-12-06T09:07:22.000Z',
        card_holder_name: 'sapphire Sanders',
        actual_resolution_date: '2023-12-11T16:15:53.000Z',
        processing_date: '2023-12-11T12:21:29.000Z',
        transaction_reference: 'TX-YnWZlcswecesdlXMIpBGzPbo5YicpQvhPswNL',
        transaction_amount: '115.34',
        transaction_type: 'card_transaction',
        transaction_date: '2023-09-26T06:03:22.000Z',
        card_type: 'virtual',
        first_six: '222329',
        last_four: '9283',
        card_expiry_date: '2028-08-31T23:59:59.000Z',
        card_brand: 'visa'
      }
    ],
    paging: {
      total_items: 3,
      page_size: 25,
      current: 1,
      count: 25,
      next: 2
    }
  }
};
export const mockedBulkpayouts = {
  data: {
    data: [
      {
        status: 'complete',
        currency: 'NGN',
        amount: '6500.00',
        description: 'test bulk transfer',
        batch_reference: 'test-payouts-api-automation-70086',
        created_at: '2024-02-01T03:02:18.000Z',
        unique_reference: 'KPY-BPO-202402010302KcM7NB18066',
        fee: '162.50',
        vat: '12.19',
        channel: 'api',
        merchant: 'Rufus Bag',
        account: {
          name: 'Rufus Bag'
        },
        total_bulk_amount: 6674.69
      },
      {
        status: 'complete',
        currency: 'NGN',
        amount: '6500.00',
        description: 'test bulk transfer',
        batch_reference: 'test-payouts-api-automation-94146',
        created_at: '2024-01-29T09:33:02.000Z',
        unique_reference: 'KPY-BPO-202401290933JzI09G02961',
        fee: '162.50',
        vat: '12.19',
        channel: 'api',
        merchant: 'Rufus Bag',
        account: {
          name: 'Rufus Bag'
        },
        total_bulk_amount: 6674.69
      },
      {
        status: 'complete',
        currency: 'NGN',
        amount: '200.00',
        description: 'test bulk transfer',
        batch_reference: 'payout_batch_reference',
        created_at: '2024-01-09T09:23:58.000Z',
        unique_reference: 'KPY-BPO-202401090923hHt1xX58344',
        fee: '5.00',
        vat: '0.38',
        channel: 'api',
        merchant: 'Ugo Demo',
        account: {
          name: 'Ugo Demo'
        },
        total_bulk_amount: 205.38
      },
      {
        status: 'complete',
        currency: 'NGN',
        amount: '14500.00',
        description: 'test bulk transfer',
        batch_reference: 'BULK_01_KSEOKAIEOOS1_b_y',
        created_at: '2024-01-08T18:49:39.000Z',
        unique_reference: 'KPY-BPO-2024010818499OaS2j39939',
        fee: '372.50',
        vat: '27.95',
        channel: 'api',
        merchant: 'Ugo Demo',
        account: {
          name: 'Ugo Demo'
        },
        total_bulk_amount: 14900.45
      },
      {
        status: 'complete',
        currency: 'NGN',
        amount: '14500.00',
        description: 'test bulk transfer',
        batch_reference: 'BULK_01_KSEOKAIEOOS1_7',
        created_at: '2024-01-08T18:35:39.000Z',
        unique_reference: 'KPY-BPO-202401081835WIWNMp39341',
        fee: '372.50',
        vat: '27.95',
        channel: 'api',
        merchant: 'Ugo Demo',
        account: {
          name: 'Ugo Demo'
        },
        total_bulk_amount: 14900.45
      },
      {
        status: 'complete',
        currency: 'NGN',
        amount: '14500.00',
        description: 'test bulk transfer',
        batch_reference: 'BULK_01_KSEOKAIEOOS1_r',
        created_at: '2024-01-08T18:33:28.000Z',
        unique_reference: 'KPY-BPO-20240108183375cEt928473',
        fee: '372.50',
        vat: '27.95',
        channel: 'api',
        merchant: 'Ugo Demo',
        account: {
          name: 'Ugo Demo'
        },
        total_bulk_amount: 14900.45
      }
    ]
  }
};
export const mockedAccountHolderKycDetail = {
  status: true,
  message: 'Virtual account holder kyc details retrieved successfully',
  data: {
    account_summary: {
      account_status: 'approved',
      account_type: 'individual',
      phone: '+*************',
      verified_identity: {
        type: 'nin',
        number: '11*****1111'
      },
      use_case: 'Personal',
      email: '<EMAIL>',
      occupation: 'Software Engineering',
      date_of_birth: '1988-04-04T00:00:00.000Z',
      address: {
        zip: '12345',
        city: 'Lagos',
        state: 'Lagos',
        address: 'Freedom Way St',
        country: 'NG'
      },
      date_created: '2024-01-29T09:59:35.000Z',
      date_verified: '2024-01-29T10:36:42.000Z'
    },
    documents: {
      selfie: {
        url: 'https://kpy-exported-files-staging.s3.amazonaws.com/identity/selfie/KPY-AH-rTMBm0r9fFd6iqb'
      },
      identification: {
        url: 'https://kpy-exported-files-staging.s3.amazonaws.com/identity/nin/KPY-AH-rTMBm0r9fFd6iqb'
      },
      proof_of_address: {
        type: 'bank_statement',
        document: {
          url: 'https://kpy-exported-files-staging.s3.amazonaws.com/identity/bank_statement/KPY-AH-rTMBm0r9fFd6iqb'
        }
      },
      source_of_inflow: {
        url: 'https://kpy-exported-files-staging.s3.amazonaws.com/identity/source_of_inflow/KPY-AH-rTMBm0r9fFd6iqb'
      }
    },
    documents_upload_completed: true
  }
};

export const mockedVbaHolder = {
  data: {
    reference: 'KPY-AH-rTMBm0r9fFd6iqb',
    account_type: 'individual',
    first_name: 'Sarah',
    last_name: 'Doe',
    email: '<EMAIL>',
    phone_number: '+*************',
    occupation: 'Software Engineering',
    status: 'approved',
    metadata: {
      key1: 'value1'
    },
    date_created: '2024-01-29T09:59:35.000Z',
    country: 'NG',
    date_of_birth: '1988-04-04T00:00:00.000Z',
    address: {
      zip: '12345',
      city: 'Lagos',
      state: 'Lagos',
      address: 'Freedom Way St',
      country: 'NG'
    }
  }
};

export const mockedAccountHolderEvent = {
  data: {
    data: [
      {
        reference: 'KPY-AHE-bAmGPaWEOIlqeBGtIPS91i4YSJIcBLsG21Sid',
        event_type: 'approval',
        description: null,
        event_action: 'Admin approved account holder',
        initiator: 'admin',
        created_at: '2024-01-29T10:36:42.000Z',
        account_holder: {
          first_name: 'Sarah',
          last_name: 'Doe'
        }
      },
      {
        reference: 'KPY-AHE-3b6IQOrprH5eQF8DCJi2l5S8eRZqthjfN9XAT',
        event_type: 'creation',
        description: 'Account Holder creation',
        event_action: 'Merchant requested to create account holder',
        initiator: 'merchant',
        created_at: '2024-01-29T09:59:35.000Z',
        account_holder: {
          first_name: 'Sarah',
          last_name: 'Doe'
        }
      }
    ]
  }
};

export const mockedAccountHolderKycHistory = {
  data: {
    count: 1,
    rows: [
      {
        status: 'approved',
        decline_reason: null,
        created_at: '2024-02-01T15:57:00.000Z',
        account_holder: {
          first_name: 'Peter',
          last_name: 'Doe',
          reference: 'KPY-AH-tON0aYskx98aJFV'
        }
      },
      {
        status: 'rejected',
        decline_reason: 'account holder’s name do not match',
        created_at: '2024-02-01T15:50:35.000Z',
        account_holder: {
          first_name: 'Peter',
          last_name: 'Doe',
          reference: 'KPY-AH-tON0aYskx98aJFV'
        }
      }
    ]
  }
};

export const mockedNgnVbaNumberDetails = {
  data: {
    account_name: 'Steph James',
    account_number: '**********',
    account_status: 'active',
    account_reference: 'xyz123abc46547',
    unique_id: 'KPY-VA-YnTjb6GsGfLgqvh',
    bank_name: 'Wema Bank',
    bank_code: '035',
    currency: 'NGN',
    created_at: '2024-01-17T18:46:19.000Z',
    customer: {
      name: 'John Doe',
      email: null
    },
    total_transacted_amount: 700,
    merchant: {
      id: 70,
      name: 'Rufus Bag',
      kora_id: 388
    }
  }
};

export const mockedFvbaNumberDetails = {
  data: {
    ...mockedNgnVbaNumberDetails.data,
    iban: '**********************',
    tier: 1,
    bank_name: 'FINANCIAL HOUSE LIMITED',
    account_type: 'individual',
    email: '<EMAIL>',
    account_holder: {
      reference: 'KPY-AH-m0fClbD6sJr16FI'
    },
    currency: 'EUR',
    transaction_limit: {
      individual: {
        tier_one: {
          daily: 500,
          single: 200,
          monthly: 5000
        },
        tier_two: {
          daily: 5000,
          single: 5000,
          monthly: 50000
        }
      }
    }
  }
};

export const mockedVbaNumberTransactions = {
  data: {
    data: [
      {
        status: 'success',
        reference: 'KPY-CV-HEZlWU0UhzNE0Az',
        created_at: '2024-02-27T14:30:55.000Z',
        amount: '200.00',
        payment_source_id: 1797,
        payment_source_type: 'virtual_bank_account',
        transaction_date: '2024-02-27 15:30:55',
        payment: {
          reference: 'KPY-PAY-NNXocYX80DluEPm'
        },
        payer_bank_account: {
          account_number: '**********',
          account_name: null,
          bank_name: null
        }
      },
      {
        status: 'flagged',
        reference: 'KPY-CV-64dHrdF4ohmEcF6',
        created_at: '2024-02-23T18:58:10.000Z',
        amount: '100.00',
        payment_source_id: 1797,
        payment_source_type: 'virtual_bank_account',
        transaction_date: '2024-02-23 19:58:10',
        payment: {
          reference: 'KPY-PAY-e8A3KCWhVJu9q9A'
        },
        payer_bank_account: {
          account_number: '**********',
          account_name: null,
          bank_name: null
        }
      },
      {
        status: 'pending',
        reference: 'KPY-CV-64dHrdF4ohmEcF9',
        created_at: '2024-02-23T18:58:10.000Z',
        amount: '100.00',
        payment_source_id: 1797,
        payment_source_type: 'virtual_bank_account',
        transaction_date: '2024-02-23 19:58:10',
        payment: {
          reference: 'KPY-PAY-e8A3KCWhVJu9q9r'
        },
        payer_bank_account: {
          account_number: '**********',
          account_name: null,
          bank_name: null
        }
      }
    ]
  }
};

export const mockedVbaOverview = {
  data: {
    currency: 'NGN',
    account_holders_count: 68,
    fixed_virtual_bank_accounts_count: 152,
    fixed_virtual_bank_account_transactions_count: 133
  }
};

export const mockedVbaPayins = {
  data: {
    data: [
      {
        reference: 'KPY-CV-uq3ceYPqAE44mSf',
        status: 'flagged',
        amount: '508.00',
        fee: '37.70',
        vat: '2.83',
        currency: 'NGN',
        amount_charged: '508.00',
        amount_paid: '467.47',
        payment_source_type: 'virtual_bank_account',
        channel: 'api',
        narration: 'Payment to Rufus Bag by John Doe',
        payment_reversals_type: 0,
        meta: null,
        message: 'Payment made to inactive virtual bank account',
        transaction_reference: 'KPY-CV-uq3ceYPqAE44mSf',
        payment_reference: 'KPY-PAY-q4XrVTauhtczkm7',
        mobile_number: null,
        transaction_date: '2024-02-29 00:43:38',
        completed_at: '2024-02-29 00:43:38',
        payment: {
          reference: 'KPY-PAY-q4XrVTauhtczkm7',
          account: {
            id: 70,
            name: 'Rufus Bag'
          }
        }
      },
      {
        reference: 'KPY-CV-CpPcCHsCWUSHttZ',
        status: 'failed',
        amount: '507.00',
        fee: '37.68',
        vat: '2.83',
        currency: 'NGN',
        amount_charged: '507.00',
        amount_paid: '466.49',
        payment_source_type: 'virtual_bank_account',
        channel: 'api',
        narration: 'Payment to Rufus Bag by John Doe',
        payment_reversals_type: 0,
        meta: null,
        message: 'Payment made to inactive virtual bank account',
        transaction_reference: 'KPY-CV-CpPcCHsCWUSHttZ',
        payment_reference: 'KPY-PAY-mKK7BjWkyIVomKl',
        mobile_number: null,
        transaction_date: '2024-02-29 00:43:23',
        completed_at: '2024-02-29 00:43:23',
        payment: {
          reference: 'KPY-PAY-mKK7BjWkyIVomKl',
          account: {
            id: 70,
            name: 'Rufus Bag'
          }
        }
      },
      {
        reference: 'KPY-CV-uOMY6TvgKnkCD2A',
        status: 'success',
        amount: '506.00',
        fee: '37.65',
        vat: '2.83',
        currency: 'NGN',
        amount_charged: '506.00',
        amount_paid: '465.52',
        payment_source_type: 'virtual_bank_account',
        channel: 'api',
        narration: 'Payment to Rufus Bag by John Doe',
        payment_reversals_type: 0,
        meta: null,
        message: 'Payment made to inactive virtual bank account',
        transaction_reference: 'KPY-CV-uOMY6TvgKnkCD2A',
        payment_reference: 'KPY-PAY-iYOJOkJMB3WIBOK',
        mobile_number: null,
        transaction_date: '2024-02-29 00:43:00',
        completed_at: '2024-02-29 00:43:00',
        payment: {
          reference: 'KPY-PAY-iYOJOkJMB3WIBOK',
          account: {
            id: 70,
            name: 'Rufus Bag'
          }
        }
      },
      {
        reference: 'KPY-CV-uQjT1rOfLeHlH0y',
        status: 'success',
        amount: '505.00',
        fee: '37.63',
        vat: '2.83',
        currency: 'NGN',
        amount_charged: '505.00',
        amount_paid: '464.54',
        payment_source_type: 'virtual_bank_account',
        channel: 'api',
        narration: 'Payment to Rufus Bag by John Doe',
        payment_reversals_type: 0,
        meta: null,
        message: 'Payment made to inactive virtual bank account',
        transaction_reference: 'KPY-CV-uQjT1rOfLeHlH0y',
        payment_reference: 'KPY-PAY-S3PWlU09npVBiiX',
        mobile_number: null,
        transaction_date: '2024-02-29 00:42:49',
        completed_at: '2024-02-29 00:42:49',
        payment: {
          reference: 'KPY-PAY-S3PWlU09npVBiiX',
          account: {
            id: 70,
            name: 'Rufus Bag'
          }
        }
      },
      {
        reference: 'KPY-CV-N7JOGluOOrP6OZX',
        status: 'success',
        amount: '504.00',
        fee: '37.60',
        vat: '2.82',
        currency: 'NGN',
        amount_charged: '504.00',
        amount_paid: '463.58',
        payment_source_type: 'virtual_bank_account',
        channel: 'api',
        narration: 'Payment to Rufus Bag by John Doe',
        payment_reversals_type: 0,
        meta: null,
        message: 'Payment made to inactive virtual bank account',
        transaction_reference: 'KPY-CV-N7JOGluOOrP6OZX',
        payment_reference: 'KPY-PAY-p0dd0cY9u9k2BTW',
        mobile_number: null,
        transaction_date: '2024-02-29 00:42:42',
        completed_at: '2024-02-29 00:42:42',
        payment: {
          reference: 'KPY-PAY-p0dd0cY9u9k2BTW',
          account: {
            id: 70,
            name: 'Rufus Bag'
          }
        }
      },
      {
        reference: 'KPY-CV-jDkCGNwAHcWx8Rq',
        status: 'success',
        amount: '502.00',
        fee: '37.55',
        vat: '2.82',
        currency: 'NGN',
        amount_charged: '502.00',
        amount_paid: '461.63',
        payment_source_type: 'virtual_bank_account',
        channel: 'api',
        narration: 'Payment to Rufus Bag by John Doe',
        payment_reversals_type: 0,
        meta: null,
        message: 'Payment made to inactive virtual bank account',
        transaction_reference: 'KPY-CV-jDkCGNwAHcWx8Rq',
        payment_reference: 'KPY-PAY-b7jySQjXAyS6qQ9',
        mobile_number: null,
        transaction_date: '2024-02-29 00:42:33',
        completed_at: '2024-02-29 00:42:33',
        payment: {
          reference: 'KPY-PAY-b7jySQjXAyS6qQ9',
          account: {
            id: 70,
            name: 'Rufus Bag'
          }
        }
      },
      {
        reference: 'KPY-CV-bukiLQYcg9nTlna',
        status: 'success',
        amount: '501.00',
        fee: '37.53',
        vat: '2.82',
        currency: 'NGN',
        amount_charged: '501.00',
        amount_paid: '460.65',
        payment_source_type: 'virtual_bank_account',
        channel: 'api',
        narration: 'Payment to Rufus Bag by John Doe',
        payment_reversals_type: 0,
        meta: null,
        message: 'Payment made to inactive virtual bank account',
        transaction_reference: 'KPY-CV-bukiLQYcg9nTlna',
        payment_reference: 'KPY-PAY-yrBq8zInhc9uA4K',
        mobile_number: null,
        transaction_date: '2024-02-29 00:42:24',
        completed_at: '2024-02-29 00:42:24',
        payment: {
          reference: 'KPY-PAY-yrBq8zInhc9uA4K',
          account: {
            id: 70,
            name: 'Rufus Bag'
          }
        }
      },
      {
        reference: 'KPY-CV-QbJkKSM04rW0eYg',
        status: 'success',
        amount: '500.00',
        fee: '37.50',
        vat: '2.82',
        currency: 'NGN',
        amount_charged: '500.00',
        amount_paid: '459.68',
        payment_source_type: 'virtual_bank_account',
        channel: 'api',
        narration: 'Payment to Rufus Bag by John Doe',
        payment_reversals_type: 0,
        meta: null,
        message: 'Payment made to inactive virtual bank account',
        transaction_reference: 'KPY-CV-QbJkKSM04rW0eYg',
        payment_reference: 'KPY-PAY-c2GCyKN7NFoR9kD',
        mobile_number: null,
        transaction_date: '2024-02-29 00:42:03',
        completed_at: '2024-02-29 00:42:03',
        payment: {
          reference: 'KPY-PAY-c2GCyKN7NFoR9kD',
          account: {
            id: 70,
            name: 'Rufus Bag'
          }
        }
      },
      {
        reference: 'KPY-CV-IiCbac0xP85wOKT',
        status: 'success',
        amount: '500.00',
        fee: '37.50',
        vat: '2.82',
        currency: 'NGN',
        amount_charged: '500.00',
        amount_paid: '459.68',
        payment_source_type: 'virtual_bank_account',
        channel: 'api',
        narration: 'Payment to Rufus Bag by John Doe',
        payment_reversals_type: 0,
        meta: null,
        message: 'Payment made to inactive virtual bank account',
        transaction_reference: 'KPY-CV-IiCbac0xP85wOKT',
        payment_reference: 'KPY-PAY-iWul7FQ9P1whrcp',
        mobile_number: null,
        transaction_date: '2024-02-29 00:41:58',
        completed_at: '2024-02-29 00:41:58',
        payment: {
          reference: 'KPY-PAY-iWul7FQ9P1whrcp',
          account: {
            id: 70,
            name: 'Rufus Bag'
          }
        }
      },
      {
        reference: 'KPY-CV-YN0uf2AJEcEjTWI',
        status: 'success',
        amount: '500.00',
        fee: '37.50',
        vat: '2.82',
        currency: 'NGN',
        amount_charged: '500.00',
        amount_paid: '459.68',
        payment_source_type: 'virtual_bank_account',
        channel: 'api',
        narration: 'Payment to Rufus Bag by John Doe',
        payment_reversals_type: 0,
        meta: null,
        message: 'Payment made to inactive virtual bank account',
        transaction_reference: 'KPY-CV-YN0uf2AJEcEjTWI',
        payment_reference: 'KPY-PAY-2Ut25bGWIATloBZ',
        mobile_number: null,
        transaction_date: '2024-02-29 00:41:52',
        completed_at: '2024-02-29 00:41:52',
        payment: {
          reference: 'KPY-PAY-2Ut25bGWIATloBZ',
          account: {
            id: 70,
            name: 'Rufus Bag'
          }
        }
      }
    ],
    paging: {
      total_items: 133,

      page_size: 10,
      current: 1,
      count: 10,
      next: 2
    }
  }
};
export const mockSpoolingReportData = {
  status: true,
  message: 'Processor transaction reports retrieved successfully',
  data: {
    data: [
      {
        id: 3,
        processor: 'kora',
        channel: null,
        currency: 'NGN',
        email_to: '<EMAIL>',
        status: 'failed',
        start_date: '2024-05-13T03:00:00.000Z',
        end_date: '2024-05-13T12:00:00.000Z',
        createdAt: '2024-10-02T12:15:45.000Z',
        updatedAt: '2024-05-13T12:33:27.000Z'
      },
      {
        id: 2,
        processor: 'sterling',
        channel: null,
        currency: 'NGN',
        email_to: '<EMAIL>',
        status: 'failed',
        start_date: '2024-05-13T03:00:00.000Z',
        end_date: '2024-05-13T12:00:00.000Z',
        createdAt: '2024-05-13T11:26:52.000Z',
        updatedAt: '2024-05-13T11:26:52.000Z'
      },
      {
        id: 1,
        processor: 'sterling',
        channel: null,
        currency: 'NGN',
        email_to: '<EMAIL>',
        status: 'processing',
        start_date: '2024-05-13T03:00:00.000Z',
        end_date: '2024-05-13T12:00:00.000Z',
        createdAt: '2024-05-13T10:53:03.000Z',
        updatedAt: '2024-05-13T10:53:03.000Z'
      }
    ]
  }
};

export const mockedAuditLogs = {
  data: {
    data: [
      {
        event: 'Processor Reports',
        description:
          'Mmekut-mfon Gabriel Edet (<EMAIL>) generated processor transaction report for sterling from 26/2/2025 12:00 AM to 26/2/2025 12:00 AM',
        metadata: null,
        event_id: 'evt_5outU_1051740670410297',
        created_at: '2025-02-27 16:33:30',
        event_date: '2025-02-27 16:33:30'
      },
      {
        event: 'Processor Reports',
        description:
          'Kehinde Egunjobi (<EMAIL>) generated processor transaction report for sterling from 26/2/2025 12:00 AM to 27/2/2025 02:00 AM',
        metadata: null,
        event_id: 'evt_BFXRF_551740670274548',
        created_at: '2025-02-27 16:31:14',
        event_date: '2025-02-27 16:31:14'
      },
      {
        event: 'Login',
        description: 'Abdullateef Adeniran (<EMAIL>) logged into the admin dashboard',
        metadata: {
          location: {
            city: 'Lagos',
            region: 'Lagos',
            country: 'NG',
            latitude: '6.44740',
            ipAddress: '**************',
            longitude: '3.39030'
          },
          device_info: {
            os: {
              name: 'Mac',
              version: '10.15',
              platform: ''
            },
            bot: null,
            client: {
              name: 'Firefox',
              type: 'browser',
              engine: 'Gecko',
              version: '135.0',
              engineVersion: '135.0'
            },
            device: {
              type: 'desktop',
              brand: 'Apple',
              model: ''
            }
          }
        },
        event_id: 'evt_VkHio_531740662280522',
        created_at: '2025-02-27 14:18:00',
        event_date: '2025-02-27 14:18:00',
        admin: {
          id: 35,
          email: '<EMAIL>',
          name: 'Abdullateef Adeniran'
        }
      }
    ]
  }
};
export const mockedSettlements = {
  data: {
    data: [
      {
        id: 2786121,
        reference: 'KPY-SET-MHvlspu6FASUKMFx',
        transactions_amount: '2000.00',
        amount: '1974.20',
        category: 'card',
        processed_at: null,
        payment_method: 'card',
        expected_settlement_date: '2024-03-04T23:00:00.000Z',
        createdAt: '2024-03-08T06:50:01.000Z',
        amount_settled: '1974.20',
        account: {
          name: 'Timi Adesoji'
        },
        rolling_reserve: {
          amount: null,
          reserved_at: null
        },
        settlement_payout: {
          amount: null,
          fee: null,
          vat: null,
          destination_number: null,
          reference: null,
          processor: null,
          trace_id: null,
          institution_name: null
        }
      }
    ]
  }
};

export const mockedSettlementsSummary = {
  data: {
    totalUndueSettlements: *********.42,
    totalPendingSettlements: ********.61,
    totalApprovedSettlements: *********.23,
    totalProcessedSettlements: **********.16,
    totalReadySettlements: 3497.2,
    totalFlaggedTransactions: 2000
  }
};

export const mockedCardIssuancePlans = {
  data: [
    {
      reference: 'KPY-PL-wpd01URxeBNpQwe',
      name: 'demo1',
      currency: 'USD',
      min_payment_value: '********.00',
      max_payment_value: '1*********.00',
      reserved_card_min_payment_value: '********.00',
      reserved_card_max_payment_value: '1*********.00',
      monthly_card_limit: 10,
      reserved_card_limit: 10,
      fee: {
        customer: {
          funding: {
            type: 'flat',
            amount: 10,
            vat_inclusive: false,
            charge_interval: 'once'
          },
          issuance: {
            type: 'flat',
            amount: 10,
            vat_inclusive: false,
            charge_interval: 'once'
          },
          chargeback: {
            type: 'flat',
            amount: 10,
            vat_inclusive: false,
            charge_interval: 'once'
          },
          withdrawal: {
            type: 'flat',
            amount: 10,
            vat_inclusive: false,
            charge_interval: 'once'
          },
          subscription: {
            type: 'flat',
            amount: 10,
            vat_inclusive: false,
            charge_interval: 'once'
          },
          cross_currency: {
            type: 'flat',
            amount: 10,
            vat_inclusive: false,
            charge_interval: 'once'
          }
        },
        reserved: {
          funding: {
            type: 'flat',
            amount: 10,
            vat_inclusive: false,
            charge_interval: 'once'
          },
          issuance: {
            type: 'flat',
            amount: 10,
            vat_inclusive: false,
            charge_interval: 'once'
          },
          chargeback: {
            type: 'flat',
            amount: 10,
            vat_inclusive: false,
            charge_interval: 'once'
          },
          withdrawal: {
            type: 'flat',
            amount: 10,
            vat_inclusive: false,
            charge_interval: 'once'
          },
          subscription: {
            type: 'flat',
            amount: 10,
            vat_inclusive: false,
            charge_interval: 'once'
          },
          cross_currency: {
            type: 'flat',
            amount: 10,
            vat_inclusive: false,
            charge_interval: 'once'
          }
        }
      },
      active: true
    },
    {
      reference: 'KPY-PL-xbV4DqYMSlTbLHP',
      name: 'Enterprise',
      currency: 'USD',
      min_payment_value: '1000000.00',
      max_payment_value: '********.00',
      reserved_card_min_payment_value: '50000.00',
      reserved_card_max_payment_value: '********.00',
      monthly_card_limit: 5000,
      reserved_card_limit: 20,
      fee: {
        customer: {
          funding: {
            type: 'percentage',
            amount: 2.5,
            vat_inclusive: false,
            charge_interval: 'monthly'
          },
          issuance: {
            type: 'flat',
            amount: 1,
            vat_inclusive: false,
            charge_interval: 'once'
          },
          chargeback: {
            type: 'flat',
            amount: 20,
            vat_inclusive: false,
            charge_interval: 'once'
          },
          withdrawal: {
            type: 'flat',
            amount: 1,
            vat_inclusive: false,
            charge_interval: 'monthly'
          },
          subscription: {
            type: 'flat',
            amount: 1500,
            vat_inclusive: true,
            charge_interval: 'monthly'
          },
          cross_currency: {
            type: 'percentage',
            amount: 2,
            vat_inclusive: false,
            charge_interval: 'monthly'
          },
          security_reserve: {
            type: 'flat',
            amount: 15000,
            vat_inclusive: false,
            charge_interval: 'once'
          }
        },
        reserved: {
          funding: {
            type: 'percentage',
            amount: 2.5,
            vat_inclusive: false,
            charge_interval: 'monthly'
          },
          issuance: {
            type: 'flat',
            amount: 1,
            vat_inclusive: false,
            charge_interval: 'once'
          },
          chargeback: {
            type: 'flat',
            amount: 45,
            vat_inclusive: false,
            charge_interval: 'once'
          },
          withdrawal: {
            type: 'flat',
            amount: 1,
            vat_inclusive: false,
            charge_interval: 'monthly'
          },
          subscription: {
            type: 'flat',
            amount: 100,
            vat_inclusive: true,
            charge_interval: 'monthly'
          },
          cross_currency: {
            type: 'percentage',
            amount: 3,
            vat_inclusive: false,
            charge_interval: 'monthly'
          },
          security_reserve: {
            type: 'flat',
            amount: 0,
            vat_inclusive: false,
            charge_interval: 'once'
          }
        }
      },
      active: true
    }
  ]
};

export const mockedCardIssuanceBillingCycle = {
  data: {
    day: 22,
    type: 'dynamic'
  }
};

export const mockedDefaultMerchant = {
  data: {
    setting: {
      country: {
        ng: {
          enabled: true,
          access: {
            kyb: {
              enabled: true,
              ng_cac: {
                enabled: true
              }
            },
            kyc: {
              enabled: true,
              ng_passport: {
                enabled: true
              },
              ng_bvn: {
                enabled: true
              },
              ng_nin: {
                enabled: true
              },
              ng_vnin: {
                enabled: true
              },
              ng_nin_phone: {
                enabled: true
              },
              ng_phone: {
                enabled: true
              },
              ng_pvc: {
                enabled: true
              }
            }
          },
          billing: {
            currency: 'NGN',
            vat_inclusive: false,
            kyb: {
              ng_cac: 120
            },
            kyc: {
              ng_passport: 85,
              ng_bvn: 45,
              ng_nin: 85,
              ng_vnin: 90,
              ng_nin_phone: 85,
              ng_phone: 50,
              ng_pvc: 100,
              selfie_validation: 100
            }
          }
        },
        gh: {
          enabled: true,
          access: {
            kyc: {
              enabled: true,
              gh_passport: {
                enabled: true
              },
              gh_ssnit: {
                enabled: true
              },
              gh_voters_card: {
                enabled: true
              },
              gh_drivers_license: {
                enabled: true
              }
            }
          },
          billing: {
            currency: 'USD',
            vat_inclusive: false,
            kyc: {
              gh_passport: 0.2,
              gh_ssnit: 0.2,
              gh_voters_card: 0.2,
              gh_drivers_license: 0.2,
              gh_national_id: 0.2,
              selfie_validation: 0.1
            }
          }
        },
        ke: {
          enabled: true,
          access: {
            kyc: {
              enabled: true,
              ke_passport: {
                enabled: true
              },
              ke_national_id: {
                enabled: true
              },
              ke_tax_pin: {
                enabled: true
              },
              ke_phone: {
                enabled: true
              },
              ke_get_phone: {
                enabled: true
              }
            }
          },
          billing: {
            currency: 'USD',
            vat_inclusive: false,
            kyc: {
              ke_passport: 0.4,
              ke_national_id: 0.4,
              ke_tax_pin: 0.1,
              ke_phone: 0.5,
              ke_get_phone: 0.5,
              ke_alien_card: 0.5,
              selfie_validation: 0.1
            }
          }
        },
        za: {
          enabled: true,
          access: {
            kyc: {
              enabled: true,
              za_said: {
                enabled: true
              }
            }
          },
          billing: {
            currency: 'USD',
            vat_inclusive: false,
            kyc: {
              za_said: 0.25
            }
          }
        }
      },
      provider_selection: {
        ng_bvn: {
          youverify: {
            billing: {
              fee: 40
            },
            has_selfie_validation: true
          },
          prembly: {
            billing: {
              fee: 36
            },
            has_selfie_validation: true
          }
        },
        ng_nin: {
          youverify: {
            billing: {
              fee: 75
            },
            has_selfie_validation: true
          },
          prembly: {
            billing: {
              fee: 72
            },
            has_selfie_validation: true
          }
        },
        ng_nin_phone: {
          youverify: {
            billing: {
              fee: 90
            },
            has_selfie_validation: true
          },
          prembly: {
            billing: {
              fee: 1000
            },
            has_selfie_validation: false
          }
        },
        ng_passport: {
          youverify: {
            billing: {
              fee: 55
            },
            has_selfie_validation: true
          },
          prembly: {
            billing: {
              fee: 72
            },
            has_selfie_validation: true
          }
        },
        ng_phone: {
          youverify: {
            billing: {
              fee: 10
            },
            has_selfie_validation: false
          },
          prembly: {
            billing: {
              fee: 27
            },
            has_selfie_validation: false
          }
        },
        ng_pvc: {
          youverify: {
            billing: {
              fee: 130
            },
            has_selfie_validation: false
          },
          prembly: {
            billing: {
              fee: 72
            },
            has_selfie_validation: false
          }
        },
        ng_vnin: {
          youverify: {
            billing: {
              fee: 85
            },
            has_selfie_validation: true
          },
          prembly: {
            billing: {
              fee: 72
            },
            has_selfie_validation: false
          }
        },
        ng_cac: {
          youverify: {
            billing: {
              fee: 120
            },
            has_selfie_validation: false
          },
          prembly: {
            billing: {
              fee: 90
            },
            has_selfie_validation: false
          }
        },
        gh_passport: {
          youverify: {
            billing: {
              fee: 200
            },
            has_selfie_validation: true
          },
          prembly: {
            billing: {
              fee: 270
            },
            has_selfie_validation: false
          }
        },
        gh_drivers_license: {
          youverify: {
            billing: {
              fee: 200
            },
            has_selfie_validation: true
          },
          prembly: {
            billing: {
              fee: 270
            },
            has_selfie_validation: false
          }
        },
        gh_ssnit: {
          youverify: {
            billing: {
              fee: 200
            },
            has_selfie_validation: true
          }
        },
        gh_voters_card: {
          youverify: {
            billing: {
              fee: 200
            },
            has_selfie_validation: true
          }
        },
        gh_national_id: {
          qore: {
            billing: {
              fee: 200
            },
            has_selfie_validation: false
          }
        },
        ke_national_id: {
          youverify: {
            billing: {
              fee: 500
            },
            has_selfie_validation: true
          },
          prembly: {
            billing: {
              fee: 540
            },
            has_selfie_validation: false
          }
        },
        ke_passport: {
          youverify: {
            billing: {
              fee: 1000
            },
            has_selfie_validation: true
          },
          prembly: {
            billing: {
              fee: 450
            },
            has_selfie_validation: false
          }
        },
        ke_phone: {
          prembly: {
            billing: {
              fee: 45
            },
            has_selfie_validation: false
          }
        },
        ke_get_phone: {
          youverify: {
            billing: {
              fee: 1000
            },
            has_selfie_validation: false
          }
        },
        ke_tax_pin: {
          youverify: {
            billing: {
              fee: 1000
            },
            has_selfie_validation: false
          },
          prembly: {
            billing: {
              fee: 90
            },
            has_selfie_validation: false
          }
        },
        ke_alien_card: {
          qore: {
            billing: {
              fee: 1000
            },
            has_selfie_validation: false
          }
        },
        za_said: {
          youverify: {
            billing: {
              fee: 1000
            },
            has_selfie_validation: false
          }
        }
      }
    },
    countries: ['ng', 'gh', 'ke', 'za'],
    identities: [
      {
        type: 'gh_ssnit',
        label: 'SSNIT',
        description: 'Ghanaian Social Security and National Insurance Trust'
      },
      {
        type: 'gh_drivers_license',
        label: "Driver's License",
        description: "Ghanaian Driver's License"
      },
      {
        type: 'gh_voters_card',
        label: 'GVC',
        description: 'Ghanaian Voters ID'
      },
      {
        type: 'gh_passport',
        label: 'Passport',
        description: 'Ghanaian International Passport'
      },
      {
        type: 'gh_national_id',
        label: 'National ID',
        description: 'Ghanaian National ID'
      },
      {
        type: 'ke_national_id',
        label: 'National ID',
        description: 'Kenyan National ID'
      },
      {
        type: 'ke_passport',
        label: 'Passport',
        description: 'Kenyan Passport'
      },
      {
        type: 'ke_phone',
        label: 'Phone Number',
        description: 'Kenyan Phone Number'
      },
      {
        type: 'ke_get_phone',
        label: 'Get Phone Data',
        description: 'Kenyan Get Phone Data'
      },
      {
        type: 'ke_tax_pin',
        label: 'Tax Pin',
        description: 'Kenyan Tax Pin'
      },
      {
        type: 'ke_alien_card',
        label: 'Alien Card',
        description: 'Kenyan Alien Card'
      },
      {
        type: 'ng_bvn',
        label: 'BVN',
        description: 'Nigerian Bank Verification Number'
      },
      {
        type: 'ng_nin',
        label: 'NIN',
        description: 'Nigerian National Identification Number'
      },
      {
        type: 'ng_pvc',
        label: 'PVC',
        description: 'Nigerian Permanent Voters Card'
      },
      {
        type: 'ng_passport',
        label: 'Passport',
        description: 'Nigerian International Passport'
      },
      {
        type: 'ng_phone',
        label: 'Phone Number',
        description: 'Nigerian Phone Number'
      },
      {
        type: 'ng_nin_phone',
        label: 'NIN Phone',
        description: 'Nigerian NIN-Phone Number'
      },
      {
        type: 'ng_vnin',
        label: 'VNIN',
        description: 'Nigerian Virtual National Identification Number'
      },
      {
        type: 'ng_cac',
        label: 'CAC',
        description: 'Nigerian Corporate Affairs Commission'
      },
      {
        type: 'za_said',
        label: 'SAID',
        description: 'South African ID'
      }
    ],

    active: false,
    date_created: '2025-01-30T13:15:07.414Z'
  }
};

export const mockedVerificationsData = {
  data: [
    {
      reference: 'VR-WF3W54oLwD604Eqf7',
      id: '***********',
      first_name: 'John',
      last_name: 'Doe',
      full_name: 'Samuel',
      phone_number: '***********',
      type: 'kyc',
      class: 'id_lookup',
      identity_type: 'ng_bvn',
      identity_type_description: 'Nigerian Bank Verification Number',
      country: 'ng',
      merchant: {
        reference: 'MC-xxswqdfehhutwexxx',
        name: 'Teis Ent.',
        email: '<EMAIL>'
      },
      requested_by: 'API User',
      status: 'valid',
      date_created: '2024-04-05T15:45:19.291Z'
    },
    {
      reference: 'VR-XqlobK4Ynv9K17Vej',
      id: 'A012345678R',
      type: 'kyc',
      class: 'id_lookup',
      identity_type: 'ke_tax_pin',
      identity_type_description: 'Kenyan Tax Pin',
      country: 'ke',
      merchant: {
        reference: 'MC-xxhysdfehhutwesew',
        name: 'Ugo Ent.',
        email: '<EMAIL>'
      },
      requested_by: 'API User',
      status: 'failed',
      date_created: '2024-04-12T07:08:19.445Z'
    },
    {
      reference: 'VR-ilbdcZXlxZrdAMvQg',
      id: 'A009274632R',
      type: 'kyc',
      class: 'id_lookup',
      identity_type: 'ke_tax_pin',
      identity_type_description: 'Kenyan Tax Pin',
      country: 'ke',
      merchant: {
        reference: 'MC-xxhysdfehhutwesew',
        name: 'Ugo Ent.',
        email: '<EMAIL>'
      },
      requested_by: 'API User',
      status: 'valid',
      date_created: '2024-04-12T07:09:02.851Z'
    },
    {
      reference: 'VR-DBeXRtdt28rSw9T9j',
      id: 'RC00000000',
      full_name: 'John Doe Inc',
      phone_number: '****** 000 0000',
      type: 'kyb',
      class: 'id_lookup',
      identity_type: 'ng_cac',
      identity_type_description: 'Nigerian Corporate Affairs Commission',
      country: 'ng',
      merchant: {
        reference: 'MC-xxhysdfehhutwesew',
        name: 'Ugo Ent.',
        email: '<EMAIL>'
      },
      requested_by: 'API User',
      status: 'valid',
      date_created: '2024-04-12T07:33:40.294Z'
    },
    {
      reference: 'VR-X06XJ9IfyXEyutA2v',
      id: '08030537420',
      type: 'kyc',
      class: 'id_lookup',
      identity_type: 'ng_nin_phone',
      identity_type_description: 'Nigerian NIN-Phone Number',
      country: 'ng',
      merchant: {
        reference: 'MC-xxhysdfehhutwesew',
        name: 'Ugo Ent.',
        email: '<EMAIL>'
      },
      requested_by: 'API User',
      status: 'failed',
      date_created: '2024-04-12T18:01:29.835Z'
    },
    {
      reference: 'VR-x86aiF5rc2fwZPE2X',
      id: '08*********',
      first_name: 'Sarah',
      middle_name: 'Jane',
      last_name: 'Doe',
      full_name: 'Sarah Doe',
      phone_number: '08*********',
      type: 'kyc',
      class: 'id_lookup',
      identity_type: 'ng_nin_phone',
      identity_type_description: 'Nigerian NIN-Phone Number',
      country: 'ng',
      merchant: {
        reference: 'MC-xxhysdfehhutwesew',
        name: 'Ugo Ent.',
        email: '<EMAIL>'
      },
      requested_by: 'API User',
      status: 'valid',
      date_created: '2024-04-12T18:01:43.127Z'
    },
    {
      reference: 'VR-OeYuIft9f4fqK7p2A',
      id: '08*********',
      first_name: 'JOHN',
      middle_name: 'MICHAEL',
      last_name: 'DOE',
      full_name: 'JOHN MICHAEL DOE',
      type: 'kyc',
      class: 'id_lookup',
      identity_type: 'ng_phone',
      identity_type_description: 'Nigerian Phone Number',
      country: 'ng',
      merchant: {
        reference: 'MC-xxhysdfehhutwesew',
        name: 'Ugo Ent.',
        email: '<EMAIL>'
      },
      requested_by: 'API User',
      status: 'valid',
      date_created: '2024-04-12T18:08:41.865Z'
    },
    {
      reference: 'VR-c5ZkNwY7jMip3lgLt',
      id: '*********',
      first_name: 'Sarah',
      middle_name: 'Jane',
      last_name: 'Doe',
      full_name: 'Sarah Doe',
      gender: 'female',
      type: 'kyc',
      class: 'id_lookup',
      identity_type: 'ng_passport',
      identity_type_description: 'Nigerian International Passport',
      country: 'ng',
      merchant: {
        reference: 'MC-xxhysdfehhutwesew',
        name: 'Ugo Ent.',
        email: '<EMAIL>'
      },
      requested_by: 'API User',
      status: 'valid',
      date_created: '2024-04-15T16:25:21.680Z'
    },
    {
      reference: 'VR-y8YxBzdu2ogjq1Vls',
      id: '*********',
      first_name: 'Sarah',
      middle_name: 'Jane',
      last_name: 'Doe',
      full_name: 'Sarah Doe',
      gender: 'female',
      type: 'kyc',
      class: 'id_lookup',
      identity_type: 'ng_passport',
      identity_type_description: 'Nigerian International Passport',
      country: 'ng',
      merchant: {
        reference: 'MC-xxhysdfehhutwesew',
        name: 'Ugo Ent.',
        email: '<EMAIL>'
      },
      requested_by: 'API User',
      status: 'valid',
      date_created: '2024-04-16T10:21:43.830Z'
    },
    {
      reference: 'VR-9016ex9fZPuffXULJ',
      id: '***********',
      first_name: 'John',
      last_name: 'Doe',
      full_name: 'John Doe',
      phone_number: '***********',
      type: 'kyc',
      class: 'id_lookup',
      identity_type: 'ng_bvn',
      identity_type_description: 'Nigerian Bank Verification Number',
      country: 'ng',
      merchant: {
        reference: 'MC-xxswqdfehhutwexxx',
        name: 'Ten Violatte Nigeria LTD',
        email: '<EMAIL>'
      },
      requested_by: 'API User',
      status: 'valid',
      date_created: '2024-04-16T10:37:24.685Z'
    }
  ],
  paging: {
    total_items: 40,
    page_size: 10,
    current: 1,
    count: 10,
    next: 2
  },
  links: [
    {
      href: 'https://api.koraapi.com/merchant/api/admin/transactions/payins?payment_method=virtual_bank_account&page=1&limit=10&currency=NGN',
      rel: 'current',
      method: 'GET'
    },
    {
      href: 'https://api.koraapi.com/merchant/api/admin/transactions/payins?payment_method=virtual_bank_account&page=2&limit=10&currency=NGN',
      rel: 'current',
      method: 'GET'
    },
    {
      href: 'https://api.koraapi.com/merchant/api/admin/verifications?page=2&limit=10&isWeb=true',
      rel: 'next',
      method: 'GET'
    }
  ]
};

export const mockedAllVbaHolders = {
  data: {
    data: [
      {
        reference: 'KPY-AH-Rl8KF1XmqGcdnOG',
        status: 'suspended',
        first_name: 'Sarah',
        last_name: 'Doe',
        account_type: 'individual',
        nationality: 'NG',
        email: '<EMAIL>',
        phone_number: '+*************',
        created_at: '2024-02-28T10:36:53.000Z',
        merchant: {
          name: 'HKT'
        }
      },
      {
        reference: 'KPY-AH-0RP86jFPvAMrsT8',
        status: 'approved',
        first_name: 'Sarah',
        last_name: 'Doe',
        account_type: 'individual',
        nationality: 'NG',
        email: '<EMAIL>',
        phone_number: '+*************',
        created_at: '2024-02-26T18:22:45.000Z',
        merchant: {
          name: 'Bytesfield'
        }
      },
      {
        reference: 'KPY-AH-mVkIIpFCjHJxfGi',
        status: 'approved',
        first_name: 'Sarah',
        last_name: 'Doe',
        account_type: 'individual',
        nationality: 'NG',
        email: '<EMAIL>',
        phone_number: '+*************',
        created_at: '2024-02-26T18:08:06.000Z',
        merchant: {
          name: 'HKT'
        }
      },
      {
        reference: 'KPY-AH-2bnaO2EFg0Di2RO',
        status: 'approved',
        first_name: 'Sarah',
        last_name: 'Doe',
        account_type: 'individual',
        nationality: 'NG',
        email: '<EMAIL>',
        phone_number: '+*************',
        created_at: '2024-02-26T14:14:48.000Z',
        merchant: {
          name: 'HKT'
        }
      },
      {
        reference: 'KPY-AH-U0MhAnv6eS3pGzW',
        status: 'approved',
        first_name: 'Sarah',
        last_name: 'Doe',
        account_type: 'individual',
        nationality: 'NG',
        email: '<EMAIL>',
        phone_number: '+*************',
        created_at: '2024-02-26T12:39:39.000Z',
        merchant: {
          name: 'HKT'
        }
      },
      {
        reference: 'KPY-AH-w2nRX3lazXRHUsj',
        status: 'approved',
        first_name: 'Sarah',
        last_name: 'Doe',
        account_type: 'individual',
        nationality: 'NG',
        email: '<EMAIL>',
        phone_number: '+*************',
        created_at: '2024-02-26T12:36:48.000Z',
        merchant: {
          name: 'HKT'
        }
      },
      {
        reference: 'KPY-AH-D3F0CaKSLQvGkNY',
        status: 'approved',
        first_name: 'Sarah',
        last_name: 'Doe',
        account_type: 'individual',
        nationality: 'NG',
        email: '<EMAIL>',
        phone_number: '+*************',
        created_at: '2024-02-26T12:23:21.000Z',
        merchant: {
          name: 'HKT'
        }
      },
      {
        reference: 'KPY-AH-N8H6uM3LcXpEmIj',
        status: 'approved',
        first_name: 'Sarah',
        last_name: 'Doe',
        account_type: 'individual',
        nationality: 'NG',
        email: '<EMAIL>',
        phone_number: '+*************',
        created_at: '2024-02-26T12:18:32.000Z',
        merchant: {
          name: 'HKT'
        }
      },
      {
        reference: 'KPY-AH-uYc27FOXniSGEmt',
        status: 'approved',
        first_name: 'Sarah',
        last_name: 'Doe',
        account_type: 'individual',
        nationality: 'NG',
        email: '<EMAIL>',
        phone_number: '+*************',
        created_at: '2024-02-26T10:45:55.000Z',
        merchant: {
          name: 'HKT'
        }
      },
      {
        reference: 'KPY-AH-rrBC0ycaIExGMBI',
        status: 'approved',
        first_name: 'Sarah',
        last_name: 'Doe',
        account_type: 'individual',
        nationality: 'NG',
        email: '<EMAIL>',
        phone_number: '+*************',
        created_at: '2024-02-22T11:29:08.000Z',
        merchant: {
          name: 'HKT'
        }
      }
    ],
    paging: {
      total_items: 68,
      page_size: 10,
      current: 1,
      count: 10,
      next: 2
    },
    links: [
      {
        href: 'https://api.koraapi.com/merchant/api/admin/virtual-bank-account/account-holders?page=1&limit=10',
        rel: 'current',
        method: 'GET'
      },
      {
        href: 'https://api.koraapi.com/merchant/api/admin/virtual-bank-account/account-holders?page=2&limit=10',
        rel: 'next',
        method: 'GET'
      }
    ]
  }
};

export const mockedAllVbaNumbers = {
  status: true,
  message: 'Virtual bank accounts retrieved successfully',
  data: {
    data: [
      {
        status: 'active',
        account_number: '**********',
        account_name: 'Tes',
        korapay_reference: 'KPY-VA-EAJOowkgpLaWW46',
        account_reference: 'xyz123bbd196',
        bank_name: 'Monniepoint Bank',
        iban: null,
        tier: null,
        currency: 'NGN',
        created_at: '2024-02-02T15:42:34.000Z',
        merchant: { name: 'LFC', id: 539 },
        account_holder: null
      },
      {
        status: 'active',
        account_number: '**********',
        account_name: 'Tes',
        korapay_reference: 'KPY-VA-NrncuRUUCZLP7A3',
        account_reference: 'xyz123bbd206',
        bank_name: 'Monniepoint Bank',
        iban: null,
        tier: null,
        currency: 'NGN',
        created_at: '2024-02-01T08:50:56.000Z',
        merchant: { name: 'Rufus Bag', id: 70 },
        account_holder: null
      },
      {
        status: 'active',
        account_number: '**********',
        account_name: 'Testing 2',
        korapay_reference: 'KPY-VA-ZGrF1sukqXrryEF',
        account_reference: 'xyz123abd105',
        bank_name: 'Sterling Bank',
        iban: null,
        tier: null,
        currency: 'NGN',
        created_at: '2024-01-31T19:15:18.000Z',
        merchant: { name: 'LFC', id: 539 },
        account_holder: null
      },
      {
        status: 'active',
        account_number: '**********',
        account_name: 'Steph James',
        korapay_reference: 'KPY-VA-UWOkoHTFdhMaqum',
        account_reference: 'xyz123abd104',
        bank_name: 'Wema Bank',
        iban: null,
        tier: null,
        currency: 'NGN',
        created_at: '2024-01-31T19:13:11.000Z',
        merchant: { name: 'LFC', id: 539 },
        account_holder: null
      },
      {
        status: 'active',
        account_number: '**********',
        account_name: 'Testing 2',
        korapay_reference: 'KPY-VA-s5jKOUipRcgylbr',
        account_reference: 'xyz123abd909',
        bank_name: 'Sterling Bank',
        iban: null,
        tier: null,
        currency: 'NGN',
        created_at: '2024-01-22T12:44:26.000Z',
        merchant: { name: 'LFC', id: 539 },
        account_holder: null
      }
    ],
    paging: { total_items: 152, page_size: 10, current: 1, count: 10, next: 2 },
    links: [
      {
        href: 'https://api.koraapi.com/merchant/api/admin/virtual-bank-account?currency=NGN&page=1&limit=10',
        rel: 'current',
        method: 'GET'
      },
      { href: 'https://api.koraapi.com/merchant/api/admin/virtual-bank-account?currency=NGN&page=2&limit=10', rel: 'next', method: 'GET' }
    ]
  }
};

export const mockedAllVbaAccountUpgradeReq = {
  status: true,
  message: 'Virtual bank account upgrade requests retrieved successfully',
  data: {
    data: [
      {
        reference: 'KPY-UG-khBJJrGoh0UF9YK',
        upgrade_from: 1,
        upgrade_to: 2,
        status: 'pending',
        created_at: '2024-03-06T11:19:56.000Z',
        decline_reason: null,
        upgrade_reason: 'test',
        date_upgraded: null,
        account_holder: { first_name: 'Sarah', last_name: 'Doe' },
        virtual_bank_account: {
          account_name: null,
          bank_name: 'FINANCIAL HOUSE LIMITED',
          account_number: '********',
          iban: '**********************'
        }
      },
      {
        reference: 'KPY-UG-hG2LPEjnL2VYCY8',
        upgrade_from: 1,
        upgrade_to: 2,
        status: 'approved',
        created_at: '2024-03-05T09:40:53.000Z',
        decline_reason: null,
        upgrade_reason: 'test',
        date_upgraded: '2024-03-05T09:41:28.000Z',
        account_holder: { first_name: 'Sarah', last_name: 'Doe' },
        virtual_bank_account: {
          account_name: null,
          bank_name: 'FINANCIAL HOUSE LIMITED',
          account_number: '********',
          iban: '**********************'
        }
      },
      {
        reference: 'KPY-UG-XeRRCVF5xEADPJF',
        upgrade_from: 1,
        upgrade_to: 2,
        status: 'declined',
        created_at: '2024-03-05T08:07:38.000Z',
        decline_reason: 'sdafaf',
        upgrade_reason: 'Test',
        date_upgraded: '2024-03-05T09:35:17.000Z',
        account_holder: { first_name: 'Sarah', last_name: 'Doe' },
        virtual_bank_account: {
          account_name: null,
          bank_name: 'FINANCIAL HOUSE LIMITED',
          account_number: '********',
          iban: '**********************'
        }
      },
      {
        reference: 'KPY-UG-2zVTJLIa2isoqbJ',
        upgrade_from: 1,
        upgrade_to: 2,
        status: 'approved',
        created_at: '2024-03-04T14:12:04.000Z',
        decline_reason: null,
        upgrade_reason: 'good stuff',
        date_upgraded: '2024-03-05T09:28:50.000Z',
        account_holder: { first_name: 'Sarah', last_name: 'Doe' },
        virtual_bank_account: {
          account_name: null,
          bank_name: 'FINANCIAL HOUSE LIMITED',
          account_number: '********',
          iban: '**********************'
        }
      },
      {
        reference: 'KPY-UG-V1GlZ0tcY2Go7CM',
        upgrade_from: 1,
        upgrade_to: 2,
        status: 'approved',
        created_at: '2024-02-28T11:42:33.000Z',
        decline_reason: null,
        upgrade_reason: 'Test',
        date_upgraded: '2024-02-29T15:15:47.000Z',
        account_holder: { first_name: 'Sarah', last_name: 'Doe' },
        virtual_bank_account: {
          account_name: null,
          bank_name: 'FINANCIAL HOUSE LIMITED',
          account_number: '********',
          iban: '**********************'
        }
      }
    ],
    paging: { total_items: 6, page_size: 10, current: 1, count: 6 },
    links: [
      {
        href: 'https://api.koraapi.com/merchant/api/admin/virtual-bank-account/upgrade-requests?currency=EUR&page=1&limit=10',
        rel: 'current',
        method: 'GET'
      }
    ]
  }
};

export const mockedVbaUpgradeReq = {
  status: true,
  message: 'Virtual bank account upgrade request retrieved successfully',
  data: {
    reference: 'KPY-UG-khBJJrGoh0UF9YK',
    merchant: 'HKT',
    current_tier: 1,
    status: 'pending',
    requested_tier: 2,
    requested_date: '2024-03-06T11:19:56.000Z',
    upgrade_use_case: 'Increased Sales',
    upgrade_use_case_document: {
      url: 'https://kpy-exported-files-staging.s3.us-east-1.amazonaws.com/account_upgrade_request/account_upgrade_request/KPY-VA-EjNWiwavjuDbo5g?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIA2LOU5WV77642VU7J%2F20240307%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20240307T135233Z&X-Amz-Expires=300&X-Amz-Signature=1839db83e4a0c4af96627cd4c28b5bcce84a2d0b85b71ace140984981f234888&X-Amz-SignedHeaders=host&x-id=GetObject'
    },
    virtual_bank_account: {
      reference: 'KPY-VA-EjNWiwavjuDbo5g',
      account_name: 'Testing',
      account_number: '********',
      bank: 'FINANCIAL HOUSE LIMITED'
    },
    account_holder: {
      name: 'Sarah Doe',
      reference: 'KPY-AH-m0fClbD6sJr16FI',
      status: 'approved',
      type: 'individual',
      email: '<EMAIL>',
      phone: '+*************',
      date_of_birth: 'Mon Apr 04 1988 00:00:00 GMT+0000 (Coordinated Universal Time)',
      address: {
        zip: '12345',
        city: 'Lagos',
        state: 'Lagos',
        address: 'Freedom Way St',
        country: 'NG'
      }
    }
  }
};

export const mockedUSDVbaAccountNumberDetails = {
  data: {
    account_name: null,
    account_number: '************',
    account_status: 'active',
    account_reference: 'AcmWYgQUU43xR651',
    unique_id: 'KPY-VA-QLk5kRaCmDIfCym',
    iban: null,
    tier: 1,
    payment_schemes: {
      swift: {
        account_holder_name: 'Sarah Doe',
        account_number: '************',
        memo: 'FCM4K9Z',
        bank_name: 'Bank of the Lakes',
        account_holder_address: {
          city: 'Lagos',
          postal_code: '12345',
          country_code: 'NG',
          address_line1: 'Freedom Way St'
        },
        bank_address: {
          city: 'Duluth',
          state: 'MN',
          postal_code: '55812',
          country_code: 'US',
          address_line1: '123 Cherry Street',
          address_line2: ''
        },
        swift_code: 'LAKEUS41'
      },
      ach: {
        account_holder_name: 'Sarah Doe',
        account_number: '************',
        routing_code: '*********',
        memo: '37BW48V',
        bank_name: 'Bank of the Lakes'
      },
      fedwire: {
        account_holder_name: 'Sarah Doe',
        account_number: '************',
        routing_code: '*********',
        memo: 'RDVQSZ3',
        bank_name: 'Bank of the Lakes',
        account_holder_address: {
          city: 'Lagos',
          postal_code: '12345',
          country_code: 'NG',
          address_line1: 'Freedom Way St'
        },
        bank_address: {
          city: 'Duluth',
          state: 'MN',
          postal_code: '55812',
          country_code: 'US',
          address_line1: '123 Cherry Street',
          address_line2: ''
        }
      }
    },
    account_type: 'individual',
    email: '<EMAIL>',
    pending_upgrade_request: false,
    account_holder: {
      reference: 'KPY-AH-2fRD7LvOyDHPkAl'
    },
    bank_code: null,
    currency: 'USD',
    created_at: '2024-05-03T14:49:34.000Z',
    customer: {
      name: 'Sarah Doe',
      email: '<EMAIL>'
    },
    total_transacted_amount: null,
    merchant: {
      id: 96,
      name: 'damiworkTest',
      kora_id: 495
    },
    transaction_limit: {
      individual: {
        tier_one: {
          daily: 3000,
          single: 1000,
          monthly: 50000
        },
        tier_two: {
          daily: 5000,
          single: 1500,
          monthly: 5000
        }
      }
    }
  }
};
export const mockedVerificationDetails = {
  status: true,
  message: 'Verification retrieved successfully',
  data: {
    reference: 'VR-9016ex9fZPuffXULJ',
    id: '***********',
    first_name: 'John',
    last_name: 'Doe',
    full_name: 'John Doe',
    phone_number: '***********',
    type: 'kyc',
    class: 'id_lookup',
    identity_type: 'ng_bvn',
    identity_type_description: 'Nigerian Bank Verification Number',
    country: 'ng',
    image: '',
    enrollment_branch: null,
    enrollment_institution: null,
    requested_by: 'API User',
    merchant: {
      reference: 'MC-xxswqdfehhutwexxx',
      name: 'Ten Violatte Nigeria LTD',
      email: '<EMAIL>'
    },
    status: 'valid',
    date_created: '2024-04-16T10:37:24.685Z',
    metadata: {
      document_details: [
        {
          key: 'identity_type_description',
          description: 'Document Type'
        },
        {
          key: 'id',
          description: 'ID Number'
        },
        {
          key: 'first_name',
          description: 'First Name'
        },
        {
          key: 'middle_name',
          description: 'Middle Name'
        },
        {
          key: 'last_name',
          description: 'Last Name'
        },
        {
          key: 'gender',
          description: 'Gender'
        },
        {
          key: 'date_of_birth',
          description: 'Date of Birth'
        },
        {
          key: 'enrollment_institution',
          description: 'Enrollment Institution'
        },
        {
          key: 'enrollment_branch',
          description: 'Enrollment Branch'
        }
      ],
      files: [
        {
          key: 'image',
          description: 'Image'
        }
      ]
    }
  }
};

export const mockedMerchantsDataList = {
  status: true,
  message: 'Merchants retrieved successfully.',
  data: {
    data: [
      {
        id: 601,
        name: 'oluwatobi doings',
        description: 'doings',
        is_active: 1,
        email: '<EMAIL>',
        type: 1,
        sra: 'learn',
        can_go_live: 1,
        env: 'live',
        status: 'active',
        kyc_status: 'verified',
        leads_class: 'verified',
        kora_id: 1043,
        kora_account_id: 909,
        has_compliance_feedback: 0,
        created_at: '2023-10-27T10:53:48.000Z',
        merchant_info: {
          compliance: [
            {
              approved_at: '2023-10-27T10:56:55.341Z',
              requirements: {
                documents: {
                  status: 'submitted'
                },
                business_type: {
                  status: 'submitted'
                },
                business_profile: {
                  status: 'submitted'
                },
                settlement_accounts: {
                  status: 'submitted'
                }
              },
              flagged_categories: [],
              submitted_for_review_at: '2023-10-27T10:56:36.446Z',
              last_submitted_categories: []
            }
          ]
        },
        country: {
          id: 165,
          name: 'Nigeria'
        }
      }
    ],
    paging: {
      total_items: 1,
      page_size: 10,
      current: 1,
      count: 1
    },
    links: [
      {
        href: 'https://api.koraapi.com/merchant/api/admin/merchants?merchantName=tobi&leadsClass=verified&page=1',
        rel: 'current',
        method: 'GET'
      }
    ]
  }
};

export const mockedIdentityBillingData = {
  data: [
    {
      reference: 'BL-iiPgC4RUQ4Cdum3cl',
      verification_reference: 'VR-nkde6BuN0v1uDsNpA',
      type: 'facial_validation',
      amount: 31,
      currency: 'NGN',
      narration: 'NG CAC verification fee',
      verification_type: 'kyb',
      verification_class: 'id_lookup',
      identity_type: 'ng_cac',
      status: 'success',
      date_created: '2024-05-20T14:33:59.635Z'
    }
  ],
  paging: {
    total_items: 1,
    page_size: 10
  }
};
export const mockedGetSentinalStatus = { id: 1, identifier: 'fBqems02GFQSTgAbUNID', status: 'PENDING', enabled: true };

export const mockedMerchantKyc = {
  status: true,
  message: 'Merchant kyc retrieved successfully',
  data: {
    merchant: {
      id: 226,
      name: 'ovie5th',
      description: null,
      avatar: null,
      kora_id: 590,
      kora_account_id: 503,
      is_active: 1,
      email: '<EMAIL>',
      risk_level: 'medium_risk',
      type: 1,
      kora_core_engine_id: 207,
      payment_channel_type: 'default',
      can_go_live: true,
      env: 'live',
      configuration: null,
      display_support_email: false,
      status: 'active',
      kyc_status: 'verified',
      country_id: 165,
      sra: null,
      createdAt: '2022-07-20T09:00:49.000Z',
      updatedAt: '2023-07-04T09:08:16.000Z',
      deletedAt: null,
      country: {
        name: 'Nigeria',
        iso2: 'NG',
        is_active: true
      }
    },
    info: {
      settlement_accounts: {
        NGN: [
          {
            id: 186,
            account_id: 207,
            destination_type: 'bank_account',
            destination_type_id: 12,
            currency: 'NGN',
            account_details: {
              bank_code: '011',
              account_name: 'IDHOLO OVIE OGAGAOGHENE',
              account_number: '**********'
            },
            status: 'under_review',
            status_details: null,
            createdAt: '2022-12-16T08:32:29.000Z',
            updatedAt: '2023-06-23T06:25:03.000Z',
            deletedAt: null,
            bank: {
              name: 'First Bank of Nigeria',
              slug: 'firstbank'
            }
          }
        ]
      },
      compliance: {
        status: 'verified'
      }
    }
  }
};

export const mockedConversions = {
  data: {
    USD: {
      activated: true,
      markup: {
        kora: {
          value: 10.22
        },
        merchant: {
          limit: 10
        }
      }
    }
  }
};

export const mockedTransactionLimits = {
  data: {
    individual: { tier_one: { daily: 5000, single: 1500, monthly: 5000 }, tier_two: { daily: 5000, single: 1500, monthly: 5000 } },
    corporate: { tier_one: { daily: 5000, single: 1500, monthly: 5000 }, tier_two: { daily: 5000, single: 1500, monthly: 5000 } }
  }
};
export const mockedPayins = {
  data: {
    data: [
      {
        reference: 'KPY-CM-qbbUQBbTuSdGHRj',
        status: 'processing',
        amount: '2122.00',
        fee: '25.00',
        vat: '1.88',
        currency: 'NGN',
        amount_charged: '2122.00',
        amount_paid: '2095.12',
        payment_source_type: 'bank_account',
        channel: 'modal',
        narration: 'testing',
        payment_reversals_type: 1,
        meta: null,
        message: 'Action required',
        processor: 'jambopay',
        processor_reference: 'jambopay-pwb-wMAuSlNn3xQDxTO',
        transaction_reference: 'KPY-CM-qbbUQBbTuSdGHRj',
        payment_reference: 'KPY-PAY-NVdrT2XLaixcJgh',
        mobile_number: null,
        transaction_date: '2024-03-21 14:14:57',
        completed_at: null,
        payment: { reference: 'KPY-PAY-NVdrT2XLaixcJgh', account: { id: 207, name: 'ovie5th' } }
      },
      {
        reference: 'KPY-CM-ln1nSmJZtji69Dw',
        status: 'processing',
        amount: '333.00',
        fee: '25.00',
        vat: '1.88',
        currency: 'NGN',
        amount_charged: '333.00',
        amount_paid: '306.12',
        payment_source_type: 'bank_account',
        channel: 'modal',
        narration: 'testing',
        payment_reversals_type: 1,
        meta: null,
        message: 'Action required',
        processor: 'opay',
        processor_reference: 'opay-pwb-KnnnBIIqZh9KznH',
        transaction_reference: 'KPY-CM-ln1nSmJZtji69Dw',
        payment_reference: 'KPY-PAY-BDyXDfKZpk7IrkW',
        mobile_number: null,
        transaction_date: '2024-03-21 14:10:34',
        completed_at: null,
        payment: { reference: 'KPY-PAY-BDyXDfKZpk7IrkW', account: { id: 207, name: 'ovie5th' } }
      },
      {
        reference: 'KPY-CM-3WOaOgupkocJB5E',
        status: 'processing',
        amount: '322.00',
        fee: '25.00',
        vat: '1.88',
        currency: 'NGN',
        amount_charged: '322.00',
        amount_paid: '295.12',
        payment_source_type: 'pay_with_bank',
        channel: 'modal',
        narration: 'testing',
        payment_reversals_type: 0,
        meta: null,
        message: 'Action required',
        processor: 'opay',
        processor_reference: 'opay-pwb-MiLMTx0MlJSdQD0',
        transaction_reference: 'KPY-CM-3WOaOgupkocJB5E',
        payment_reference: 'KPY-PAY-lnQvjRYVx8m71Nz',
        mobile_number: null,
        transaction_date: '2024-03-21 14:08:41',
        completed_at: null,
        payment: { reference: 'KPY-PAY-lnQvjRYVx8m71Nz', account: { id: 207, name: 'ovie5th' } }
      }
    ],
    paging: { total_items: 55, page_size: 10, current: 1, count: 10, next: 2 },
    links: [
      {
        href: 'https://api.koraapi.com/merchant/api/admin/transactions/payins?page=1&limit=10&dateFrom=2024-03-21&dateTo=2024-03-21&currency=NGN',
        rel: 'current',
        method: 'GET'
      },
      {
        href: 'https://api.koraapi.com/merchant/api/admin/transactions/payins?page=2&limit=10&dateFrom=2024-03-21&dateTo=2024-03-21&currency=NGN',
        rel: 'next',
        method: 'GET'
      }
    ]
  }
};
export const mockUnlockMerchant = { status: true, message: 'Access Enabled!', data: null };
export const mockedGetAllMerchants = {
  data: {
    data: [
      {
        id: 762,
        name: 'International Business',
        description: null,
        is_active: 0,
        email: '<EMAIL>',
        type: 1,
        sra: 'online',
        can_go_live: 0,
        env: 'test',
        status: 'email_unverified',
        kyc_status: 'unverified',
        leads_class: 'signup',
        bvn_status: null,
        kora_id: 1274,
        kora_account_id: 1075,
        has_compliance_feedback: 0,
        created_at: '2024-08-08T10:58:13.000Z',
        merchant_info: {
          compliance: null
        },
        country: {
          id: 165,
          name: 'Nigeria'
        },
        tier_level: {
          id: 1,
          name: 'Test mode',
          description: 'Test mode'
        }
      },
      {
        id: 761,
        name: "Aunty Neenah's",
        description: 'Catering services, delivering cooked Nigerian foods overseas.',
        is_active: 1,
        email: '<EMAIL>',
        type: 1,
        sra: 'Social Media',
        can_go_live: 0,
        env: 'test',
        status: 'active',
        kyc_status: 'ready',
        leads_class: 'ready',
        bvn_status: 'submitted',
        kora_id: 474,
        kora_account_id: 389,
        has_compliance_feedback: 0,
        created_at: '2024-08-07T15:11:48.000Z',
        merchant_info: {
          compliance: [
            {
              requirements: {
                documents: {
                  status: 'submitted'
                },
                business_type: {
                  status: 'submitted'
                },
                business_profile: {
                  status: 'submitted'
                },
                settlement_accounts: {
                  status: 'submitted'
                }
              },
              flagged_categories: [],
              submitted_for_review_at: '2024-08-07T15:27:57.221Z',
              last_submitted_categories: ['business_profile', 'documents', 'settlement_accounts']
            }
          ]
        },
        country: {
          id: 165,
          name: 'Nigeria'
        },
        tier_level: {
          id: 1,
          name: 'Test mode',
          description: 'Test mode'
        }
      }
    ],
    paging: { total_items: 55, page_size: 10, current: 1, count: 10, next: 2 }
  }
};

export const mockedBankTrxPaymentPreference = {
  data: {
    settings: {
      payment_events: {
        overpayment: {
          action: 'return_excess'
        },
        underpayment: {
          action: 'return_all'
        }
      }
    },
    apply_status: 'active',
    apply_delay_in_seconds: 1
  }
};

export const mockedBankTrxPaymentPreferenceList = {
  data: {
    data: [
      {
        id: 1,
        merchant_id: 207,
        payment_events: {
          overpayment: 'return_excess',
          underpayment: 'return_all'
        },
        status: 'pending',
        document_reference: 'files/757bc110-5ede-11ef-9c9f-4393b90999af.pdf',
        comments: null,
        modified_by: null,
        createdAt: '2024-08-20 12:00:00',
        updatedAt: '2024-08-20 12:00:00',
        merchant: {
          id: 207,
          client_id: null,
          kora_id: 590,
          name: 'ovie5th',
          email: '<EMAIL>',
          risk_level: 'medium_risk',
          tier_level_id: null,
          is_active: true,
          createdAt: '2022-07-20T09:00:49.000Z',
          updatedAt: '2022-07-20T09:00:49.000Z'
        },
        admin: null
      },
      {
        id: 1,
        merchant_id: 207,
        payment_events: {
          overpayment: 'return_excess',
          underpayment: 'return_all'
        },
        status: 'rejected',
        document_reference: 'files/757bc110-5ede-11ef-9c9f-4393b90999af.pdf',
        comments: null,
        modified_by: null,
        createdAt: '2024-08-20T10:30:42.000Z',
        updatedAt: '2024-08-20T10:30:42.000Z',
        merchant: {
          id: 207,
          client_id: null,
          kora_id: 590,
          name: 'ovie5th',
          email: '<EMAIL>',
          risk_level: 'medium_risk',
          tier_level_id: null,
          is_active: true,
          createdAt: '2022-07-20T09:00:49.000Z',
          updatedAt: '2022-07-20T09:00:49.000Z'
        },
        admin: null
      }
    ],
    paging: {
      total_items: 1,
      page_size: 10,
      current: 1,
      count: 1
    }
  }
};
export const mockedMerchantEmailDetails = {
  status: true,
  message: 'success',
  data: {
    id: 1,
    name: 'Test Merchant',
    description: 'Non id odit sed sunt omnis perspiciatis harum23v1',
    avatar: null,
    kora_id: 319,
    kora_account_id: 314,
    is_active: 1,
    email: '<EMAIL>',
    risk_level: 'medium_risk',
    type: 1,
    kora_core_engine_id: 25,
    payment_channel_type: 'default',
    can_go_live: true,
    env: 'live',
    configuration: {
      permissions: {
        can_charge_card_via_api: true
      }
    },
    display_support_email: false,
    status: 'active',
    kyc_status: 'verified',
    country_id: 165,
    sra: null,
    createdAt: '2020-08-25T11:24:39.000Z',
    updatedAt: '2023-09-04T15:46:49.000Z',
    deletedAt: null,
    country: {
      name: 'Nigeria',
      iso2: 'NG',
      is_active: true
    },
    default_currency: 'NGN',
    website: 'https://www.example.co',
    support_email: '<EMAIL>',
    support_phone: '*************',
    reserved_bank_account: {
      merchant_name: 'Test Merchant',
      reserved_bank_accounts: [
        {
          account_name: 'Korapay-Test Merchant',
          account_number: '**********',
          bank_code: '035',
          bank_name: 'Wema Bank',
          preferred: false,
          fee_definition: [
            {
              type: 'flat',
              range: [0, 5000],
              value: 25,
              currency: 'NGN',
              base_fee: 25,
              vat: 1.875,
              vat_inclusive: false,
              total_fee: 26.88,
              cap: 26.88
            },
            {
              type: 'flat',
              range: [5000, 10000],
              value: 10,
              currency: 'NGN',
              base_fee: 10,
              vat: 0.75,
              vat_inclusive: false,
              total_fee: 10.75,
              cap: 10.75
            }
          ]
        },
        {
          account_name: 'Korapay-Sta',
          account_number: '*********1',
          bank_code: '232',
          bank_name: 'Sterling bank',
          preferred: false,
          fee_definition: [
            {
              type: 'flat',
              range: [0, 5000],
              value: 25,
              currency: 'NGN',
              base_fee: 25,
              vat: 1.875,
              vat_inclusive: false,
              total_fee: 26.88,
              cap: 26.88
            },
            {
              type: 'flat',
              range: [5000, 10000],
              value: 10,
              currency: 'NGN',
              base_fee: 10,
              vat: 0.75,
              vat_inclusive: false,
              total_fee: 10.75,
              cap: 10.75
            }
          ]
        },
        {
          account_name: 'Korapay-TIM',
          account_number: '**********',
          bank_code: '035',
          bank_name: 'Wema Bank',
          preferred: true,
          fee_definition: [
            {
              type: 'flat',
              range: [0, 5000],
              value: 25,
              currency: 'NGN',
              base_fee: 25,
              vat: 1.875,
              vat_inclusive: false,
              total_fee: 26.88,
              cap: 26.88
            },
            {
              type: 'flat',
              range: [5000, 10000],
              value: 10,
              currency: 'NGN',
              base_fee: 10,
              vat: 0.75,
              vat_inclusive: false,
              total_fee: 10.75,
              cap: 10.75
            }
          ]
        }
      ]
    },
    can_do_disbursements: true,
    can_do_card_tokenization: true,
    can_do_collections: true
  }
};

export const mockedPartnerAccountBalance = {
  id: 3,
  name: 'Monnify account',
  processor_slug: 'monnify',
  destination_number: '**********',
  destination_type: 'bank_account',
  destination_name: 'Monnify',
  institution_name: 'Monnify Test Bank',
  institution_code: '001',
  currency: 'NGN',
  channel: null,
  threshold: {
    max_balance: 4**********,
    min_balance: 20000,
    max_deposit_amount: 500000,
    max_transfer_amount: 500000
  },
  actions: {
    can_transfer: true,
    can_get_balance: true,
    validate_balance: true
  },
  active: 1,
  createdAt: '2019-09-26T07:01:57.000Z',
  updatedAt: '2019-09-26T07:01:57.000Z',
  available_balance: **********.96
};

export const mockedKesPartnersBalancesList = {
  data: [
    {
      id: 57,
      name: 'Cellulant Momo',
      processor_slug: 'cellulant',
      destination_number: '**********',
      destination_type: 'mobile_money',
      destination_name: 'Cellulant KES',
      institution_name: 'Cellulant Bank',
      institution_code: '101',
      currency: 'KES',
      channel: 'KORA',
      threshold: {
        max_balance: 10000,
        min_balance: 500
      },
      actions: {
        can_transfer: false,
        can_get_balance: false,
        validate_balance: true
      },
      active: 1,
      createdAt: '2025-07-23T15:46:58.000Z',
      updatedAt: '2025-07-23T15:46:58.000Z'
    },
    {
      id: 58,
      name: 'Sasapay Bank',
      processor_slug: 'sasapay',
      destination_number: '**********',
      destination_type: 'bank_account',
      destination_name: 'Sasapay KES',
      institution_name: 'Sasapay Bank',
      institution_code: '101',
      currency: 'KES',
      channel: 'KORA',
      threshold: {
        max_balance: 10000,
        min_balance: 500
      },
      actions: {
        can_transfer: false,
        can_get_balance: false,
        validate_balance: true
      },
      active: 1,
      createdAt: '2025-07-23T15:46:58.000Z',
      updatedAt: '2025-07-23T15:46:58.000Z'
    }
  ]
};
export const mockedKesPartnersAccountBalances = {
  '57': {
    id: 57,
    name: 'Cellulant Momo',
    processor_slug: 'cellulant',
    destination_number: '**********',
    destination_type: 'mobile_money',
    destination_name: 'Cellulant KES',
    institution_name: 'Cellulant Bank',
    institution_code: '101',
    currency: 'KES',
    channel: 'KORA',
    threshold: {
      max_balance: 10000,
      min_balance: 500
    },
    actions: {
      can_transfer: false,
      can_get_balance: false,
      validate_balance: true
    },
    active: 1,
    createdAt: '2025-07-23T15:46:58.000Z',
    updatedAt: '2025-07-23T15:46:58.000Z'
  },
  '58': {
    id: 58,
    name: 'Cellulant Bank',
    processor_slug: 'cellulant',
    destination_number: '**********',
    destination_type: 'bank_account',
    destination_name: 'Cellulant KES',
    institution_name: 'Cellulant Bank',
    institution_code: '101',
    currency: 'KES',
    channel: 'KORA',
    threshold: {
      max_balance: 10000,
      min_balance: 500
    },
    actions: {
      can_transfer: false,
      can_get_balance: false,
      validate_balance: true
    },
    active: 1,
    createdAt: '2025-07-23T15:46:58.000Z',
    updatedAt: '2025-07-23T15:46:58.000Z'
  }
};
export const mockedPartnersAccountBalances = {
  '3': {
    id: 3,
    name: 'Monnify account',
    processor_slug: 'monnify',
    destination_number: '**********',
    destination_type: 'bank_account',
    destination_name: 'Monnify',
    institution_name: 'Monnify Test Bank',
    institution_code: '001',
    currency: 'NGN',
    channel: null,
    threshold: {
      max_balance: 4**********,
      min_balance: 20000,
      max_deposit_amount: 300000,
      max_transfer_amount: 500000
    },
    actions: {
      can_transfer: true,
      can_get_balance: true,
      validate_balance: true
    },
    active: 1,
    createdAt: '2019-09-26T07:01:57.000Z',
    updatedAt: '2019-09-26T07:01:57.000Z',
    available_balance: **********.96
  },
  '5': {
    id: 5,
    name: 'Wema account',
    processor_slug: 'wema',
    destination_number: '**********',
    destination_type: 'bank_account',
    destination_name: 'AweMFB',
    institution_name: 'Wema Bank',
    institution_code: '035',
    currency: 'NGN',
    channel: 'AWE',
    threshold: {
      max_balance: 4**********,
      min_balance: 20000,
      max_deposit_amount: 500000,
      max_transfer_amount: 500000
    },
    actions: {
      can_transfer: true,
      can_get_balance: false,
      validate_balance: true
    },
    active: 1,
    createdAt: '2019-09-26T07:01:57.000Z',
    updatedAt: '2019-09-26T07:01:57.000Z',
    available_balance: 0
  },
  '6': {
    id: 6,
    name: 'Test account',
    processor_slug: 'test_transfer',
    destination_number: '**********',
    destination_type: 'bank_account',
    destination_name: 'Korapay Test',
    institution_name: 'Test Bank',
    institution_code: '000',
    currency: 'NGN',
    channel: null,
    threshold: {
      max_balance: 4**********,
      min_balance: 20000,
      max_deposit_amount: 500000,
      max_transfer_amount: 500000
    },
    actions: {
      can_transfer: false,
      can_get_balance: true,
      validate_balance: true
    },
    active: 1,
    createdAt: '2019-09-26T07:01:57.000Z',
    updatedAt: '2019-09-26T07:01:57.000Z',
    available_balance: 500
  },
  '7': {
    id: 7,
    name: 'Optimus account',
    processor_slug: 'test_transfer',
    destination_number: '**********',
    destination_type: 'bank_account',
    destination_name: 'Optimus Test',
    institution_name: 'Test Bank',
    institution_code: '000',
    currency: 'NGN',
    channel: null,
    threshold: {
      max_balance: 4**********,
      min_balance: 20000,
      max_deposit_amount: 300000,
      max_transfer_amount: 500000
    },
    actions: {
      can_transfer: true,
      can_get_balance: true,
      validate_balance: true
    },
    active: 1,
    createdAt: '2019-09-26T07:01:57.000Z',
    updatedAt: '2019-09-26T07:01:57.000Z',
    available_balance: 5*********
  },
  '8': {
    id: 8,
    name: 'Access account',
    processor_slug: 'access',
    destination_number: '**********',
    destination_type: 'bank_account',
    destination_name: 'Access',
    institution_name: 'Access Test Bank',
    institution_code: '001',
    currency: 'NGN',
    channel: null,
    threshold: {
      max_balance: 4**********,
      min_balance: 20000,
      max_deposit_amount: 8*********,
      max_transfer_amount: 500000
    },
    actions: {
      can_transfer: true,
      can_get_balance: true,
      validate_balance: true
    },
    active: 1,
    createdAt: '2019-09-26T07:01:57.000Z',
    updatedAt: '2019-09-26T07:01:57.000Z',
    available_balance: 37*********.96
  },
  '9': {
    id: 9,
    name: 'Globus account',
    processor_slug: 'test_transfer',
    destination_number: '**********',
    destination_type: 'bank_account',
    destination_name: 'Globus Test',
    institution_name: 'Test Bank',
    institution_code: '000',
    currency: 'NGN',
    channel: null,
    threshold: {
      max_balance: 4**********,
      min_balance: 20000,
      max_deposit_amount: 8*********,
      max_transfer_amount: 8*********
    },
    actions: {
      can_transfer: true,
      can_get_balance: true,
      validate_balance: true
    },
    active: 1,
    createdAt: '2019-09-26T07:01:57.000Z',
    updatedAt: '2019-09-26T07:01:57.000Z',
    available_balance: 5*********
  }
};

export const mockedNgnPartnerBalanceList = {
  data: [
    {
      id: 3,
      name: 'Monnify account',
      processor_slug: 'monnify',
      destination_number: '**********',
      destination_type: 'bank_account',
      destination_name: 'Monnify',
      institution_name: 'Monnify Test Bank',
      institution_code: '001',
      currency: 'NGN',
      channel: null,
      threshold: {
        max_balance: 4**********,
        min_balance: 20000,
        max_deposit_amount: 500000,
        max_transfer_amount: 500000
      },
      actions: {
        can_transfer: true,
        can_get_balance: true,
        validate_balance: true
      },
      active: 1,
      createdAt: '2019-09-26T07:01:57.000Z',
      updatedAt: '2019-09-26T07:01:57.000Z'
    },
    {
      id: 4,
      name: 'Providus account',
      processor_slug: 'providus',
      destination_number: '**********',
      destination_type: 'bank_account',
      destination_name: 'NATHAN AGBARA',
      institution_name: 'Providus Bank',
      institution_code: '101',
      currency: 'NGN',
      channel: 'KORA',
      threshold: {
        max_balance: 4**********,
        min_balance: 20000,
        max_deposit_amount: 500000,
        max_transfer_amount: 500000
      },
      actions: {
        can_transfer: true,
        can_get_balance: true,
        validate_balance: true
      },
      active: 1,
      createdAt: '2019-09-26T07:01:57.000Z',
      updatedAt: '2019-09-26T07:01:57.000Z'
    },
    {
      id: 5,
      name: 'Wema account',
      processor_slug: 'wema',
      destination_number: '**********',
      destination_type: 'bank_account',
      destination_name: 'AweMFB',
      institution_name: 'Wema Bank',
      institution_code: '035',
      currency: 'NGN',
      channel: 'AWE',
      threshold: {
        max_balance: 4**********,
        min_balance: 20000,
        max_deposit_amount: 500000,
        max_transfer_amount: 500000
      },
      actions: {
        can_transfer: true,
        can_get_balance: true,
        validate_balance: true
      },
      active: 1,
      createdAt: '2019-09-26T07:01:57.000Z',
      updatedAt: '2019-09-26T07:01:57.000Z'
    },
    {
      id: 6,
      name: 'Test account',
      processor_slug: 'test_transfer',
      destination_number: '**********',
      destination_type: 'bank_account',
      destination_name: 'Korapay Test',
      institution_name: 'Test Bank',
      institution_code: '000',
      currency: 'NGN',
      channel: null,
      threshold: {
        max_balance: 4**********,
        min_balance: 20000,
        max_deposit_amount: 500000,
        max_transfer_amount: 500000
      },
      actions: {
        can_transfer: false,
        can_get_balance: true,
        validate_balance: true
      },
      active: 1,
      createdAt: '2019-09-26T07:01:57.000Z',
      updatedAt: '2019-09-26T07:01:57.000Z'
    }
  ]
};

export const mockedPartnerBalanceCurrency = {
  data: ['NGN', 'KES', 'GHS']
};

export const mockedPartnerBalanceTransactionHistory = {
  data: {
    data: [
      {
        id: 102,
        source_processor_account_id: 3,
        destination_processor_account_id: 6,
        reference: 'KPY-PTF-202408221422A2HpZR05379',
        processor_reference: 'mnnfy-trf-KwmpjzfVhJeHeFQ',
        session_id: null,
        amount: '20000.00',
        currency: 'NGN',
        initiated_by: 'Dennis Egbo',
        initiator_email: '<EMAIL>',
        status: 'success',
        completed_at: '2024-08-22T14:22:05.000Z',
        created_at: '2024-08-22T14:22:05.000Z',
        updated_at: '2024-08-22T14:22:05.000Z',
        source_account_number: '**********',
        source_account_name: 'Monnify',
        source_institution_name: 'Monnify Test Bank',
        destination_account_number: '**********',
        destination_account_name: 'Korapay Test',
        destination_institution_name: 'Test Bank',
        source_partner_account: {
          id: 3,
          name: 'Monnify account',
          processor_slug: 'monnify',
          destination_number: '**********',
          destination_type: 'bank_account',
          destination_name: 'Monnify',
          institution_name: 'Monnify Test Bank',
          institution_code: '001',
          currency: 'NGN',
          channel: null,
          threshold: {
            max_balance: 4**********,
            min_balance: 20000,
            max_deposit_amount: 500000,
            max_transfer_amount: 500000
          },
          actions: {
            can_transfer: true,
            can_get_balance: true,
            validate_balance: true
          },
          active: true,
          createdAt: '2019-09-26T07:01:57.000Z',
          updatedAt: '2019-09-26T07:01:57.000Z'
        },
        destination_partner_account: {
          id: 6,
          name: 'Test account',
          processor_slug: 'test_transfer',
          destination_number: '**********',
          destination_type: 'bank_account',
          destination_name: 'Korapay Test',
          institution_name: 'Test Bank',
          institution_code: '000',
          currency: 'NGN',
          channel: null,
          threshold: {
            max_balance: 4**********,
            min_balance: 20000,
            max_deposit_amount: 500000,
            max_transfer_amount: 500000
          },
          actions: {
            can_transfer: false,
            can_get_balance: true,
            validate_balance: true
          },
          active: true,
          createdAt: '2019-09-26T07:01:57.000Z',
          updatedAt: '2019-09-26T07:01:57.000Z'
        }
      },
      {
        id: 101,
        source_processor_account_id: 3,
        destination_processor_account_id: 6,
        reference: 'KPY-PTF-202408221248ZfptPE27096',
        processor_reference: 'mnnfy-trf-MhSvyQ9yifuMmez',
        session_id: null,
        amount: '5000.00',
        currency: 'NGN',
        initiated_by: 'Dennis Egbo',
        initiator_email: '<EMAIL>',
        status: 'success',
        completed_at: '2024-08-22T12:48:27.000Z',
        created_at: '2024-08-22T12:48:27.000Z',
        updated_at: '2024-08-22T12:48:27.000Z',
        source_account_number: '**********',
        source_account_name: 'Monnify',
        source_institution_name: 'Monnify Test Bank',
        destination_account_number: '**********',
        destination_account_name: 'Korapay Test',
        destination_institution_name: 'Test Bank',
        source_partner_account: {
          id: 3,
          name: 'Monnify account',
          processor_slug: 'monnify',
          destination_number: '**********',
          destination_type: 'bank_account',
          destination_name: 'Monnify',
          institution_name: 'Monnify Test Bank',
          institution_code: '001',
          currency: 'NGN',
          channel: null,
          threshold: {
            max_balance: 4**********,
            min_balance: 20000,
            max_deposit_amount: 500000,
            max_transfer_amount: 500000
          },
          actions: {
            can_transfer: true,
            can_get_balance: true,
            validate_balance: true
          },
          active: true,
          createdAt: '2019-09-26T07:01:57.000Z',
          updatedAt: '2019-09-26T07:01:57.000Z'
        },
        destination_partner_account: {
          id: 6,
          name: 'Test account',
          processor_slug: 'test_transfer',
          destination_number: '**********',
          destination_type: 'bank_account',
          destination_name: 'Korapay Test',
          institution_name: 'Test Bank',
          institution_code: '000',
          currency: 'NGN',
          channel: null,
          threshold: {
            max_balance: 4**********,
            min_balance: 20000,
            max_deposit_amount: 500000,
            max_transfer_amount: 500000
          },
          actions: {
            can_transfer: false,
            can_get_balance: true,
            validate_balance: true
          },
          active: true,
          createdAt: '2019-09-26T07:01:57.000Z',
          updatedAt: '2019-09-26T07:01:57.000Z'
        }
      },
      {
        id: 100,
        source_processor_account_id: 3,
        destination_processor_account_id: 6,
        reference: 'KPY-PTF-202408211708TJ617T38753',
        processor_reference: 'mnnfy-trf-UDc0YfxPTMAaB2T',
        session_id: null,
        amount: '500000.00',
        currency: 'NGN',
        initiated_by: 'Tunde Osborne',
        initiator_email: '<EMAIL>',
        status: 'success',
        completed_at: '2024-08-21T17:08:39.000Z',
        created_at: '2024-08-21T17:08:38.000Z',
        updated_at: '2024-08-21T17:08:39.000Z',
        source_account_number: '**********',
        source_account_name: 'Monnify',
        source_institution_name: 'Monnify Test Bank',
        destination_account_number: '**********',
        destination_account_name: 'Korapay Test',
        destination_institution_name: 'Test Bank',
        source_partner_account: {
          id: 3,
          name: 'Monnify account',
          processor_slug: 'monnify',
          destination_number: '**********',
          destination_type: 'bank_account',
          destination_name: 'Monnify',
          institution_name: 'Monnify Test Bank',
          institution_code: '001',
          currency: 'NGN',
          channel: null,
          threshold: {
            max_balance: 4**********,
            min_balance: 20000,
            max_deposit_amount: 500000,
            max_transfer_amount: 500000
          },
          actions: {
            can_transfer: true,
            can_get_balance: true,
            validate_balance: true
          },
          active: true,
          createdAt: '2019-09-26T07:01:57.000Z',
          updatedAt: '2019-09-26T07:01:57.000Z'
        },
        destination_partner_account: {
          id: 6,
          name: 'Test account',
          processor_slug: 'test_transfer',
          destination_number: '**********',
          destination_type: 'bank_account',
          destination_name: 'Korapay Test',
          institution_name: 'Test Bank',
          institution_code: '000',
          currency: 'NGN',
          channel: null,
          threshold: {
            max_balance: 4**********,
            min_balance: 20000,
            max_deposit_amount: 500000,
            max_transfer_amount: 500000
          },
          actions: {
            can_transfer: false,
            can_get_balance: true,
            validate_balance: true
          },
          active: true,
          createdAt: '2019-09-26T07:01:57.000Z',
          updatedAt: '2019-09-26T07:01:57.000Z'
        }
      },
      {
        id: 99,
        source_processor_account_id: 3,
        destination_processor_account_id: 6,
        reference: 'KPY-PTF-202408210719qXlU0T05590',
        processor_reference: 'mnnfy-trf-NsuX2W0hI8OG5gq',
        session_id: null,
        amount: '2500.00',
        currency: 'NGN',
        initiated_by: 'Dennis Egbo',
        initiator_email: '<EMAIL>',
        status: 'success',
        completed_at: '2024-08-21T07:19:05.000Z',
        created_at: '2024-08-21T07:19:05.000Z',
        updated_at: '2024-08-21T07:19:05.000Z',
        source_account_number: '**********',
        source_account_name: 'Monnify',
        source_institution_name: 'Monnify Test Bank',
        destination_account_number: '**********',
        destination_account_name: 'Korapay Test',
        destination_institution_name: 'Test Bank',
        source_partner_account: {
          id: 3,
          name: 'Monnify account',
          processor_slug: 'monnify',
          destination_number: '**********',
          destination_type: 'bank_account',
          destination_name: 'Monnify',
          institution_name: 'Monnify Test Bank',
          institution_code: '001',
          currency: 'NGN',
          channel: null,
          threshold: {
            max_balance: 4**********,
            min_balance: 20000,
            max_deposit_amount: 500000,
            max_transfer_amount: 500000
          },
          actions: {
            can_transfer: true,
            can_get_balance: true,
            validate_balance: true
          },
          active: true,
          createdAt: '2019-09-26T07:01:57.000Z',
          updatedAt: '2019-09-26T07:01:57.000Z'
        },
        destination_partner_account: {
          id: 6,
          name: 'Test account',
          processor_slug: 'test_transfer',
          destination_number: '**********',
          destination_type: 'bank_account',
          destination_name: 'Korapay Test',
          institution_name: 'Test Bank',
          institution_code: '000',
          currency: 'NGN',
          channel: null,
          threshold: {
            max_balance: 4**********,
            min_balance: 20000,
            max_deposit_amount: 500000,
            max_transfer_amount: 500000
          },
          actions: {
            can_transfer: false,
            can_get_balance: true,
            validate_balance: true
          },
          active: true,
          createdAt: '2019-09-26T07:01:57.000Z',
          updatedAt: '2019-09-26T07:01:57.000Z'
        }
      },
      {
        id: 98,
        source_processor_account_id: 3,
        destination_processor_account_id: 6,
        reference: 'KPY-PTF-202408210712Gebmen23895',
        processor_reference: 'mnnfy-trf-qglTXlvd7ZPVqv2',
        session_id: null,
        amount: '20000.00',
        currency: 'NGN',
        initiated_by: 'Dennis Egbo',
        initiator_email: '<EMAIL>',
        status: 'success',
        completed_at: '2024-08-21T07:12:24.000Z',
        created_at: '2024-08-21T07:12:23.000Z',
        updated_at: '2024-08-21T07:12:24.000Z',
        source_account_number: '**********',
        source_account_name: 'Monnify',
        source_institution_name: 'Monnify Test Bank',
        destination_account_number: '**********',
        destination_account_name: 'Korapay Test',
        destination_institution_name: 'Test Bank',
        source_partner_account: {
          id: 3,
          name: 'Monnify account',
          processor_slug: 'monnify',
          destination_number: '**********',
          destination_type: 'bank_account',
          destination_name: 'Monnify',
          institution_name: 'Monnify Test Bank',
          institution_code: '001',
          currency: 'NGN',
          channel: null,
          threshold: {
            max_balance: 4**********,
            min_balance: 20000,
            max_deposit_amount: 500000,
            max_transfer_amount: 500000
          },
          actions: {
            can_transfer: true,
            can_get_balance: true,
            validate_balance: true
          },
          active: true,
          createdAt: '2019-09-26T07:01:57.000Z',
          updatedAt: '2019-09-26T07:01:57.000Z'
        },
        destination_partner_account: {
          id: 6,
          name: 'Test account',
          processor_slug: 'test_transfer',
          destination_number: '**********',
          destination_type: 'bank_account',
          destination_name: 'Korapay Test',
          institution_name: 'Test Bank',
          institution_code: '000',
          currency: 'NGN',
          channel: null,
          threshold: {
            max_balance: 4**********,
            min_balance: 20000,
            max_deposit_amount: 500000,
            max_transfer_amount: 500000
          },
          actions: {
            can_transfer: false,
            can_get_balance: true,
            validate_balance: true
          },
          active: true,
          createdAt: '2019-09-26T07:01:57.000Z',
          updatedAt: '2019-09-26T07:01:57.000Z'
        }
      }
    ],
    paging: {
      total_items: 5,
      page_size: 10,
      current: 1,
      count: 9
    }
  }
};
export const mockPaymentReversalDetails = {
  data: {
    id: 1726,
    payment_source_id: 14923,
    reference: 'KPY-RFD-LeL3pMoUktch',
    payment_source_reference: 'KPY-CM-RDsF2Tkq07Qmg7P',
    payment_id: 25703,
    account_id: 25,
    type: 'refund',
    amount: '3.00',
    currency: 'NGN',
    status: 'pending',
    status_reason: null,
    completed_at: null,
    destination: 'customer',
    reversal_reason: 'Overpayment',
    source: 'disbursement_wallet',
    metadata: {
      payout_automated: true,
      refunds_operation_account_id: 25
    },
    createdAt: '2024-10-24T18:09:26.000Z',
    updatedAt: '2024-10-24T18:09:26.000Z',
    payment_source: {
      settlement_category: 'bank',
      id: 14923,
      payment_source_type: 'bank_transfer',
      payment_source_id: 4014,
      payment_id: 25703,
      transaction_id: 15843,
      is_processed: true,
      payment_reversals_type: 1,
      settlement_id: 2775432,
      settle_at: '2023-09-14T21:08:04.000Z',
      status: 'success',
      amount: '4000.00',
      fee: '25.00',
      vat: '1.88',
      amount_charged: '4000.00',
      amount_paid: '3973.12',
      amount_collected: '4000.00',
      currency: 'NGN',
      message: 'Successful',
      response_code: 'AA000',
      narration: 'JKdjbl',
      reference: 'KPY-CM-RDsF2Tkq07Qmg7P',
      processor: 'wema',
      processor_reference: 'wema-bt-Gc8AHAacAOSI14y',
      channel: 'modal',
      auth_data: null,
      merchant_bears_cost: true,
      completed_at: '2023-09-14T21:08:04.000Z',
      updatedAt: '2024-10-24T18:09:26.000Z',
      PaymentSourceId: 4014,
      paymentSourceId: 4014
    },
    payment: {
      id: 25703,
      account_id: 25,
      wallet_id: 23,
      disbursement_wallet_id: 20,
      for_disbursement_wallet: false,
      customer_id: 5830,
      reference: 'KPY-PAY-Ya6oD914tfIs',
      unique_reference: 'KPY-PI-202309142107VXYrxV14270',
      currency: 'NGN',
      amount: '4000.00',
      fee: '25.00',
      vat: '1.88',
      description: 'JKdjbl',
      merchant_bears_cost: true,
      status: 'complete',
      type: 'collection',
      requires_approval: 0,
      approved: 0,
      notification_url: null,
      notify_customer: true,
      redirect_url: null,
      payment_request_id: 219,
      completed_at: '2023-09-14T21:08:04.000Z',
      allowed_payment_methods: ['card', 'bank_transfer', 'pay_with_bank'],
      default_payment_method: null,
      metadata: null,
      internal_metadata: null
    },
    account: {
      id: 25,
      client_id: null,
      kora_id: 319,
      name: 'Timi Adesoji',
      email: '<EMAIL>',
      risk_level: 'medium_risk',
      tier_level_id: 2,
      is_active: true,
      createdAt: '2020-08-25T11:24:38.000Z',
      updatedAt: '2024-09-24T15:21:57.000Z'
    },
    payment_reversal_payouts: []
  }
};

export const mockIssuingMerchantDetails: { data: IssuingMerchantDetailsResponseType } = {
  data: {
    reference: 'KPY12345',
    name: 'Demo merchant',
    status: 'active',
    plan: {
      reference: 'planRef123',
      name: 'Example Plan',
      currency: 'USD',
      min_payment_value: '10.00',
      max_payment_value: '1000.00',
      reserved_card_min_payment_value: '5.00',
      reserved_card_max_payment_value: '500.00',
      monthly_card_limit: 100,
      reserved_card_limit: 50,
      card_type: 'virtual',
      fee: {
        customer: {
          funding: {
            type: 'percentage',
            amount: 2.5,
            vat_inclusive: true,
            charge_interval: 'monthly',
            active: true
          },
          issuance: {
            type: 'flat',
            amount: 10,
            vat_inclusive: false,
            charge_interval: 'yearly',
            active: true
          },
          chargeback: {
            type: 'percentage',
            amount: 1.5,
            vat_inclusive: true,
            charge_interval: 'monthly',
            active: true
          },
          withdrawal: {
            type: 'flat',
            amount: 5,
            vat_inclusive: false,
            charge_interval: 'yearly',
            active: true
          },
          subscription: {
            type: 'percentage',
            amount: 3,
            vat_inclusive: true,
            charge_interval: 'monthly',
            active: true
          },
          cross_currency: {
            type: 'flat',
            amount: 15,
            vat_inclusive: false,
            charge_interval: 'yearly',
            active: true
          },
          security_reserve: {
            type: 'percentage',
            amount: 0.5,
            vat_inclusive: true,
            charge_interval: 'monthly',
            active: true
          }
        },
        reserved: {
          funding: {
            type: 'percentage',
            amount: 2.5,
            vat_inclusive: true,
            charge_interval: 'monthly',
            active: true
          },
          issuance: {
            type: 'flat',
            amount: 10,
            vat_inclusive: false,
            charge_interval: 'yearly',
            active: true
          },
          chargeback: {
            type: 'percentage',
            amount: 1.5,
            vat_inclusive: true,
            charge_interval: 'monthly',
            active: true
          },
          withdrawal: {
            type: 'flat',
            amount: 5,
            vat_inclusive: false,
            charge_interval: 'yearly',
            active: true
          },
          subscription: {
            type: 'percentage',
            amount: 3,
            vat_inclusive: true,
            charge_interval: 'monthly',
            active: true
          },
          cross_currency: {
            type: 'flat',
            amount: 15,
            vat_inclusive: false,
            charge_interval: 'yearly',
            active: true
          },
          security_reserve: {
            type: 'percentage',
            amount: 0.5,
            vat_inclusive: true,
            charge_interval: 'monthly',
            active: true
          }
        }
      },
      type: 'standard',
      active: true,
      createdAt: '2023-01-01T00:00:00Z',
      updatedAt: '2023-01-01T00:00:00Z'
    },
    wallet_balance: 1000,
    issued_cards: 10,
    reserved_cards: 5,
    kora_id: 12345,
    transactions_count: 100,
    config: {
      reserved: {
        monthly_billing_enabled: true,
        status: 'active',
        card_creation_enabled: true,
        risk_level_limit: {
          spending_limit: {
            type: 'custom',
            data: {
              daily_max: 500,
              monthly_max: 5000,
              per_transaction_max: 100
            }
          },
          funding_limit: {
            type: 'default',
            data: {
              daily_max: 1000,
              monthly_max: 10000,
              quarterly_max: 30000
            }
          }
        },
        pcidss_level_limit: {
          type: 'custom',
          data: {
            yearly_issued_cards: 1000,
            yearly_transaction_count: 'limitless'
          }
        }
      },
      customer: {
        monthly_billing_enabled: true,
        status: 'inactive',
        card_creation_enabled: false,
        risk_level_limit: {
          spending_limit: {
            type: 'default',
            data: {
              daily_max: 300,
              monthly_max: 3000,
              per_transaction_max: 50
            }
          },
          funding_limit: {
            type: 'custom',
            data: {
              daily_max: 800,
              monthly_max: 8000,
              quarterly_max: 24000
            }
          }
        },
        pcidss_level_limit: {
          type: 'default',
          data: {
            yearly_issued_cards: 500,
            yearly_transaction_count: 10000
          }
        }
      },
      issuing_wallet_funding_fee: {
        type: 'percentage',
        amount: 2.5,
        enabled: true
      }
    },
    risk_level: 'low_risk',
    pci_dss_level: 'level_1',
    date_created: '2023-01-01T00:00:00Z',
    provider: {
      customer: 'passpoint',
      reserved: 'maplerad'
    },
    access_request_reference: {
      reserved: '123456',
      customer: '654321'
    }
  }
};
export const mockedDisputeRefundDetailsData = {
  status: true,
  message: 'Refund details retrieved successfully',
  data: {
    amount: '13847.00',
    status: 'manual',
    currency: 'NGN',
    destination: 'customer',
    reference: 'KPY-RFD-d2mu4S16oS9j',
    reversal_reason: 'Cancelled Transaction',
    metadata: {
      payout_automated: false,
      refunds_operation_account_id: 25
    },
    status_reason: null,
    created_at: '2025-01-03 14:48:38',
    completed_at: null,
    channel: 'card',
    account: {
      name: 'Splendid Sparkle Limited'
    },
    payment: {
      reference: 'KPY-PAY-8Va3TB1QwQtgvAL',
      customer: {
        name: 'John Doe',
        email: '<EMAIL>'
      }
    },
    payment_source: {
      id: 48952,
      reference: 'KPY-CM-MPdphENutfxKOdw',
      amount: '13847.00',
      amount_collected: '13847.00',
      payment_source_type: 'card',
      channel: 'modal',
      payment_source_id: null,
      processor: 'test_card_payment',
      processor_reference: 'test-card-bWJ1UHMbvPJIoT0',
      settlement: {
        reference: 'KPY-SET-47qS9wfFcCei4gMg'
      }
    },
    source: {
      type: 'card',
      details: {
        masked_pan: '************2595',
        card_type: 'mastercard'
      }
    }
  }
};

export const mockedDisputeChargeBack = {
  status: true,
  message: 'Chargeback details retrieved successfully',
  data: {
    status_history: [
      {
        date: '2025-01-07T08:13:00.764Z',
        status: 'accepted'
      }
    ],
    account_id: 607,
    processor_reference: 'test-card-pXfH8chCFkOYt6f',
    processor: 'test_card_payment',
    reference: 'KPY-CHG-0bx0UUagNBFY81V',
    payment_reference: 'KPY-PAY-ltYGitt4JXMcbPB',
    payment_source_reference: 'KPY-CM-Dvm4akLHylfOHqz',
    batch_code: 'KPY-CHG-BTCH-8ItobmefrC7KeJ0',
    status: 'accepted',
    currency: 'NGN',
    amount: '1180.63',
    approved_amount: '1180.63',
    deadline: '2025-02-20T08:12:00.000Z',
    payment_method: 'card',
    payment_reversal_reference: 'KPY-CHG-0bx0UUagNBFY81V',
    log_code: null,
    reason: 'reason',
    merchant: 'Winners Golden Bet Nigeria Limited',
    merchant_email: '<EMAIL>',
    created_at: '2025-01-07 09:12:33',
    account: {
      name: 'Winners Golden Bet Nigeria Limited',
      email: '<EMAIL>'
    },
    source: {
      type: 'card',
      details: {
        masked_pan: '************2787',
        card_type: 'visa'
      }
    }
  }
};

export const mockWhiteListedIPData = {
  data: {
    active: true,
    ip_addresses: [
      {
        created_at: '2025-01-28T08:46:35.373Z',
        ip_address: '************',
        description: 'Error IP'
      },
      {
        created_at: '2025-01-21T14:38:46.909Z',
        ip_address: '************',
        description: 'testing sanity'
      },
      {
        created_at: '2024-02-08T12:18:59.628Z',
        ip_address: 'fc15:e74d:4427:9f76:13c5:4df9:496d:ddc2',
        description: 'testing'
      },
      {
        created_at: '2023-11-16T10:23:25.242Z',
        ip_address: 'aace:e62a:ce51:34b5:7d8c:933e:148e:5968',
        description: 'IPv6'
      }
    ],
    scope: 'default'
  }
};
export const mockedPayOutMetabaseData = {
  status: true,
  message: 'Admin metabase urls',
  data: {
    urls: ['https://iframe.payout']
  }
};

export const mockedLienDetailsData = {
  data: {
    data: {
      merchant_id: 87,
      reference: 'KPY-LIEN-chGzjolFFqsU0Bm',
      amount: '1180.63',
      currency: 'NGN',
      status: 'released',
      source_type: 'payment_source',
      source_reference: 'KPY-CW-Ug7IMemshdYsgN7f',
      event_history: [
        {
          status: 'active',
          reason: 'failure',
          date: '2025-02-21T11:58:47.308Z'
        },
        {
          status: 'released',
          reason: 'all',
          date: '2025-02-22T11:53:32.855Z'
        }
      ],
      date_completed: '2025-02-22T11:53:32.000Z',
      reason: 'failure',
      created_at: '2025-02-21T11:58:47.000Z',
      updated_at: '2025-02-22T11:53:32.000Z',
      merchant_name: 'GIG Logistics'
    }
  }
};

export const mockedProcessorQuery = {
  data: [
    {
      reference: 'test1',
      cleared: false,
      response: {
        status: '02',
        status_desc: ' No data found.',
        transactions: null
      },
      success: true
    },
    {
      reference: 'test2',
      cleared: false,
      response: {
        status: '02',
        status_desc: ' No data found.',
        transactions: null
      },
      success: true
    }
  ]
};

export const mockCardIssuanceLimits = {
  customer: {
    risk_level: {
      low_risk: {
        funding_limit: {
          daily_max: 5000,
          monthly_max: 50000,
          quarterly_max: 150000
        },
        spending_limit: {
          daily_max: 4000,
          monthly_max: 40000,
          per_transaction_max: 1000
        }
      },
      medium_risk: {
        funding_limit: {
          daily_max: 2500,
          monthly_max: 25000,
          quarterly_max: 75000
        },
        spending_limit: {
          daily_max: 2000,
          monthly_max: 20000,
          per_transaction_max: 500
        }
      },
      above_average_risk: {
        funding_limit: {
          daily_max: 1000,
          monthly_max: 10000,
          quarterly_max: 30000
        },
        spending_limit: {
          daily_max: 800,
          monthly_max: 8000,
          per_transaction_max: 200
        }
      },
      high_risk: {
        funding_limit: {
          daily_max: 500,
          monthly_max: 5000,
          quarterly_max: 15000
        },
        spending_limit: {
          daily_max: 400,
          monthly_max: 4000,
          per_transaction_max: 100
        }
      }
    },
    pcidss_level: {
      level_0: {
        yearly_issued_cards: 'limitless',
        yearly_transaction_count: 'limitless'
      },
      level_1: {
        yearly_issued_cards: 'limitless',
        yearly_transaction_count: 'limitless'
      },
      level_2: {
        yearly_issued_cards: 100000,
        yearly_transaction_count: 1000000
      },
      level_3: {
        yearly_issued_cards: 50000,
        yearly_transaction_count: 500000
      },
      level_4: {
        yearly_issued_cards: 1000,
        yearly_transaction_count: 20000
      }
    }
  },
  reserved: {
    risk_level: {
      low_risk: {
        funding_limit: {
          daily_max: 4000,
          monthly_max: 40000,
          quarterly_max: 120000
        },
        spending_limit: {
          daily_max: 3500,
          monthly_max: 35000,
          per_transaction_max: 800
        }
      },
      medium_risk: {
        funding_limit: {
          daily_max: 2000,
          monthly_max: 20000,
          quarterly_max: 60000
        },
        spending_limit: {
          daily_max: 1800,
          monthly_max: 18000,
          per_transaction_max: 400
        }
      },
      above_average_risk: {
        funding_limit: {
          daily_max: 800,
          monthly_max: 8000,
          quarterly_max: 24000
        },
        spending_limit: {
          daily_max: 700,
          monthly_max: 7000,
          per_transaction_max: 150
        }
      },
      high_risk: {
        funding_limit: {
          daily_max: 400,
          monthly_max: 4000,
          quarterly_max: 12000
        },
        spending_limit: {
          daily_max: 300,
          monthly_max: 3000,
          per_transaction_max: 80
        }
      }
    },
    pcidss_level: {
      level_0: {
        yearly_issued_cards: 'limitless',
        yearly_transaction_count: 'limitless'
      },
      level_1: {
        yearly_issued_cards: 1000000,
        yearly_transaction_count: 'limitless'
      },
      level_2: {
        yearly_issued_cards: 80000,
        yearly_transaction_count: 800000
      },
      level_3: {
        yearly_issued_cards: 40000,
        yearly_transaction_count: 400000
      },
      level_4: {
        yearly_issued_cards: 500,
        yearly_transaction_count: 15000
      }
    }
  }
};

export const mockedMerchantRegistration = {
  data: {
    data: [
      {
        id: 10,
        merchant_id: 235,
        key_personnel: [],
        created_at: '2025-04-24T11:14:12.000Z',
        updated_at: '2025-04-24T11:14:12.000Z',
        merchant: {
          id: 656,
          name: 'Nigeria-signup',
          email: '<EMAIL>',
          status: 'active',
          tier_level: '',
          location: 'Nigeria',
          industry: 'cable_or_satellite_or_pay_television',
          number_of_representatives: 0,
          number_of_directors: 0,
          number_of_shareholders: 0
        }
      },
      {
        id: 9,
        merchant_id: 834,
        key_personnel: [
          {
            name: 'PETER DOE',
            role: 'WITNESS',
            address: '8C, NONE STREET, ZONE 4',
            occupation: 'LAWYER',
            share_type: '',
            nationality: 'NIGERIA',
            identification_type: '',
            identification_number: ''
          },
          {
            name: 'JANE DOE  INC',
            role: 'SHAREHOLDER',
            address: '01 COASTAL HIGHWAY,LEWES DE 000000  USA,COUNTY OF SUSSEX',
            occupation: null,
            share_type: 'ordinary',
            nationality: 'UNITED STATES',
            share_count: 9990000,
            share_value: 9990000,
            share_percentage: 99.9,
            identification_type: null,
            identification_number: null
          }
        ],
        created_at: '2025-04-22T14:25:26.000Z',
        updated_at: '2025-04-24T10:52:15.000Z',
        merchant: {
          id: 834,
          name: 'GBD Business Limited',
          email: '<EMAIL>',
          status: 'active',
          tier_level: 'Base Tier',
          location: 'Nigeria',
          industry: 'agriculture',
          number_of_representatives: 6,
          number_of_directors: 1,
          number_of_shareholders: 2
        }
      },
      {
        id: 8,
        merchant_id: 831,
        key_personnel: [
          {
            name: 'SUSAN DOE',
            role: 'SECRETARY_COMPANY',
            address: '11, NONE CRESCENT',
            occupation: 'SECRETARY',
            share_type: '',
            nationality: 'NIGERIA',
            identification_type: '',
            identification_number: ''
          },
          {
            name: 'MICHAEL DOE',
            role: 'DIRECTOR',
            address: '1, NONE STREET, ',
            occupation: 'BUSINESS',
            nationality: 'NIGERIA',
            identification_type: null,
            identification_number: null
          },
          {
            name: 'JOHN DOE INC',
            role: 'PERSONS WITH SIGNIFICANT CONTROL',
            address: '01 COASTAL HIGHWAY,LEWES DE 000000 ',
            occupation: null,
            nationality: 'UNITED STATES',
            identification_type: null,
            identification_number: null
          }
        ],
        created_at: '2025-04-21T20:11:45.000Z',
        updated_at: '2025-04-21T20:11:45.000Z',
        merchant: {
          id: 831,
          name: 'jasmineclark',
          email: '<EMAIL>',
          status: 'active',
          tier_level: 'Tier 3',
          location: 'Nigeria',
          industry: 'agriculture',
          number_of_representatives: 6,
          number_of_directors: 1,
          number_of_shareholders: 2
        }
      }
    ],
    paging: {
      total_items: 4,
      page_size: 10,
      current: 1,
      count: 4,
      next: 2
    },
    links: [
      {
        href: 'https://api.koraapi.com/merchant/api/admin/wallets/swaps/transactions?page=1',
        rel: 'current',
        method: 'GET'
      }
    ]
  }
};
export const mockedMerchantStatistics = {
  data: {
    total: 974,
    status: {
      active: 744,
      deactivated: 9
    },
    kyc_status: {
      signup: 176,
      kyc: 408,
      feedback: 28,
      rejected: 38,
      ready: 52,
      verified: 263,
      responded: 2,
      unverified: 7
    }
  }
};

export const mockedPoolAccounts = {
  data: {
    data: [
      {
        id: 4,
        account_id: 207,
        customer_name: 'Cheche',
        customer_email: '<EMAIL>',
        reference: 'test-ref',
        created_at: '2025-05-26T09:55:07.000Z',
        updated_at: '2025-05-26T09:55:07.000Z',
        account_name: 'ovie5th',
        account_kora_id: 590
      },
      {
        id: 3,
        account_id: 207,
        customer_name: 'Bob',
        customer_email: '<EMAIL>',
        reference: 'KKK-OVI-BOB',
        created_at: '2025-05-25T10:43:06.000Z',
        updated_at: '2025-05-25T10:43:06.000Z',
        account_name: 'oscar',
        account_kora_id: 590
      }
    ],
    paging: {
      total_items: 2,
      page_size: 10,
      current: 1,
      count: 2
    }
  }
};

export const mockedPoolAccountTransactions = {
  data: {
    data: [
      {
        id: 15,
        pool_account_id: 4,
        currency: 'NGN',
        amount_paid: '200.00',
        net_amount: '200',
        transaction_reference: 'transaction-id-1',
        transaction_date: '2025-05-14T09:00:00.000Z',
        status: 'settled',
        source_details: {
          details: 'testing: 4999'
        },
        fee: '0',
        created_at: '2025-05-28T15:55:04.000Z',
        updated_at: '2025-05-28T15:55:04.000Z',
        pool_account_customer_name: 'Cheche',
        pool_account_reference: 'test-ref'
      },
      {
        id: 14,
        pool_account_id: 4,
        currency: 'NGN',
        amount_paid: '150.00',
        net_amount: '150',
        transaction_reference: 'transaction-id-2',
        transaction_date: '2025-05-15T01:00:00.000Z',
        status: 'success',
        source_details: {
          details: 'bank name: UBA'
        },
        fee: '0',
        created_at: '2025-05-28T15:53:43.000Z',
        updated_at: '2025-05-28T15:53:43.000Z',
        pool_account_customer_name: 'Cheche',
        pool_account_reference: 'test-ref'
      }
    ],
    paging: {
      total_items: 2,
      page_size: 10,
      current: 1,
      count: 2
    }
  }
};

export const mockedPoolAccountTransactionDetails = {
  data: {
    id: 16,
    pool_account_id: 4,
    currency: 'NGN',
    amount_paid: '200.00',
    net_amount: '200',
    transaction_reference: 'test-ref',
    transaction_date: '2025-05-15T01:00:00.000Z',
    status: 'settled',
    source_details: {
      details: 'sdaffsd'
    },
    fee: '0',
    created_at: '2025-05-29T08:47:44.000Z',
    updated_at: '2025-05-29T08:47:44.000Z',
    pool_account_customer_name: 'Cheche',
    pool_account_reference: 'KKK-OVI-CHECH'
  }
};
