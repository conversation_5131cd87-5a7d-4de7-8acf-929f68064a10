import { EditDetailsCardPropsType, TransactionLimitType, WithdrawalLimitType } from '+types/productConfig';

/**
 * Central Test Data Factories
 *
 * This file contains reusable mock data builders for testing across the application.
 * Similar to how handlers.ts centralizes API mocking, this file centralizes test data creation.
 *
 * Organization:
 * - Group factories by feature/domain (e.g., Withdrawal Limits, Card Issuance, etc.)
 * - Use clear naming conventions: build[FeatureName][DataType] (e.g., buildWithdrawalLimitConfig)
 * - Provide sensible defaults with override capability
 * - Export all builders for reuse across test files
 * - Use explicit TypeScript types from +types directory (NEVER use 'any')
 */

// =============================================================================
// WITHDRAWAL LIMITS FACTORIES
// =============================================================================
export const buildTransactionLimit = (overrides: Partial<TransactionLimitType> = {}): TransactionLimitType => ({
  min: 100,
  max: 1000,
  ...overrides
});

export const buildWithdrawalLimitConfig = (overrides: Partial<WithdrawalLimitType> = {}): WithdrawalLimitType => ({
  api: {
    per_transaction: buildTransactionLimit({ min: 50, max: 500 })
  },
  web: {
    daily: {
      settlement_account: 100000,
      non_settlement_account: 80000
    },
    per_transaction: {
      settlement_account: buildTransactionLimit({ min: 100, max: 1000 }),
      non_settlement_account: buildTransactionLimit({ min: 200, max: 900 })
    }
  },
  global: {
    daily: 200000
  },
  limit: buildTransactionLimit({ min: 10, max: 5000 }),
  ...overrides
});

export const buildProductConfigResponse = (overrides: Partial<{ withdrawalLimitConfig: WithdrawalLimitType }> = {}) => ({
  withdrawalLimitConfig: buildWithdrawalLimitConfig(),
  ...overrides
});

export const buildEditDetailsCardProps = (overrides: Partial<EditDetailsCardPropsType> = {}): EditDetailsCardPropsType => ({
  title: 'limits',
  content: buildWithdrawalLimitConfig(),
  paymentMethod: 'card',
  currency: 'NGN',
  category: 'card-issuance',
  type: 'bank_account',
  disableEdit: false,
  merchantId: '23',
  ...overrides
});

// Enhanced config builder for useLimit hook tests
export const buildConfigPayload = (
  overrides: Partial<{
    data: {
      setting: {
        transaction: {
          disbursement: {
            limits: {
              web: {
                daily: { settlement_account: number; non_settlement_account: number };
                per_transaction: {
                  settlement_account: TransactionLimitType;
                  non_settlement_account: TransactionLimitType;
                };
              };
              api: { per_transaction: TransactionLimitType };
              global: { daily: number };
            };
            bank_account: {
              limits: {
                web: {
                  daily: { settlement_account: number; non_settlement_account: number };
                  per_transaction: {
                    settlement_account: TransactionLimitType;
                    non_settlement_account: TransactionLimitType;
                  };
                };
                api: { per_transaction: TransactionLimitType };
              };
              transaction_limit: TransactionLimitType;
            };
          };
        };
      };
    };
  }> = {}
) => ({
  data: {
    setting: {
      transaction: {
        disbursement: {
          limits: {
            web: {
              daily: { settlement_account: 100000, non_settlement_account: 80000 },
              per_transaction: {
                settlement_account: { min: 100, max: 1000 },
                non_settlement_account: { min: 200, max: 900 }
              }
            },
            api: { per_transaction: { min: 50, max: 500 } },
            global: { daily: 200000 }
          },
          bank_account: {
            limits: {
              web: {
                daily: { settlement_account: 100000, non_settlement_account: 80000 },
                per_transaction: {
                  settlement_account: { min: 100, max: 1000 },
                  non_settlement_account: { min: 200, max: 900 }
                }
              },
              api: { per_transaction: { min: 50, max: 500 } }
            },
            transaction_limit: { min: 10, max: 5000 }
          }
        }
      }
    }
  },
  ...overrides
});
