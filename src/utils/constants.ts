import { CheckboxType } from '+types';

type queriesParamType =
  | 'current'
  | 'limit'
  | 'status'
  | 'page'
  | 'general'
  | 'compliance'
  | 'kycTab'
  | 'tab'
  | 'verified'
  | 'kyc'
  | 'feedback'
  | 'ready'
  | 'signup'
  | 'rejected'
  | 'submitted'
  | 'sorterType'
  | 'subTab'
  | 'activeCurrency'
  | 'dateFrom'
  | 'dateTo'
  | 'keyword'
  | 'selectedConfig'
  | 'activeTab'
  | 'amountRange'
  | 'dateCreatedTo'
  | 'dateCreatedFrom'
  | 'selectData'
  | 'amountSubfilter'
  | 'product'
  | 'totalItems'
  | 'payment-preferences'
  | 'currency'
  | 'currencyAccessStatus'
  | 'reference'
  | 'responded'
  | 'previousLimit'
  | 'registration-details'
  | 'processorReference'
  | 'transactionId';
// eslint-disable-next-line import/prefer-default-export

export const queriesParams: { [k in queriesParamType]: queriesParamType } = {
  current: 'current',
  limit: 'limit',
  status: 'status',
  page: 'page',
  general: 'general',
  compliance: 'compliance',
  kycTab: 'kycTab',
  tab: 'tab',
  verified: 'verified',
  kyc: 'kyc',
  feedback: 'feedback',
  ready: 'ready',
  signup: 'signup',
  rejected: 'rejected',
  submitted: 'submitted',
  sorterType: 'sorterType',
  subTab: 'subTab',
  activeCurrency: 'activeCurrency',
  currency: 'currency',
  dateFrom: 'dateFrom',
  dateTo: 'dateTo',
  keyword: 'keyword',
  selectedConfig: 'selectedConfig',
  activeTab: 'activeTab',
  amountRange: 'amountRange',
  dateCreatedTo: 'dateCreatedTo',
  dateCreatedFrom: 'dateCreatedFrom',
  selectData: 'selectData',
  amountSubfilter: 'amountSubfilter',
  product: 'product',
  totalItems: 'totalItems',
  'payment-preferences': 'payment-preferences',
  currencyAccessStatus: 'currencyAccessStatus',
  reference: 'reference',
  previousLimit: 'previousLimit',
  responded: 'responded',
  'registration-details': 'registration-details',
  processorReference: 'processorReference',
  transactionId: 'transactionId'
};

export const eventType = {
  kyc: 'Individual (KYC)',
  kyb: 'Business (KYB)'
};

export const statusIcon = {
  failed: {
    icon: 'invalid',
    color: 'red'
  },
  invalid: {
    icon: 'invalid',
    color: 'red'
  },
  valid: {
    icon: 'check',
    color: 'green'
  },
  undefined: {
    icon: 'unknown',
    color: undefined
  }
};

export const regions = {
  ng: 'Nigeria',
  ke: 'Kenya',
  gh: 'Ghana',
  za: 'South Africa'
};

type storageDataKeyType = 'AVAILABLE_CURRENCIES' | 'SINGLE_MERCHANT';

export const storageDataKey: { [k in storageDataKeyType]: storageDataKeyType } = {
  AVAILABLE_CURRENCIES: 'AVAILABLE_CURRENCIES',
  SINGLE_MERCHANT: 'SINGLE_MERCHANT'
};

export const timeMode = {
  startTime: 'startTime',
  endTime: 'endTime'
};
export const hourData: string[] = ['12', '01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11'];
export const minuteData: string[] = [
  '00',
  '01',
  '02',
  '03',
  '04',
  '05',
  '06',
  '07',
  '08',
  '09',
  '10',
  '11',
  '12',
  '13',
  '14',
  '15',
  '16',
  '17',
  '18',
  '19',
  '20',
  '21',
  '22',
  '23',
  '24',
  '25',
  '26',
  '27',
  '28',
  '29',
  '30',
  '31',
  '32',
  '33',
  '34',
  '35',
  '36',
  '37',
  '38',
  '39',
  '40',
  '41',
  '42',
  '43',
  '44',
  '45',
  '46',
  '47',
  '48',
  '49',
  '50',
  '51',
  '52',
  '53',
  '54',
  '55',
  '56',
  '57',
  '58',
  '59'
];

export const cardStatus = {
  pre_authorized: 'Pre-Authorized',
  void_authorization: 'Voided (Auth)',
  void_capture: 'Voided (Capture)'
};

export const currencyOrder: string[] = ['NGN', 'USD', 'GBP', 'EUR', 'GHS', 'KES', 'ZAR', 'XAF', 'XOF', 'EGP', 'TZS'];

export const identityCountries: CheckboxType[] = [
  { label: 'Nigeria (NG)', value: 'ng' },
  { label: 'Ghana (GH)', value: 'gh' },
  { label: 'Kenya (KE)', value: 'ke' },
  { label: 'South Africa (ZA)', value: 'za' }
];

export const identityCountriesWithoutCode: CheckboxType[] = [
  { label: 'Nigeria', value: 'ng' },
  { label: 'Ghana', value: 'gh' },
  { label: 'Kenya', value: 'ke' },
  { label: 'South Africa', value: 'za' }
];

export const kycTitles = {
  ng_passport: 'International Passport',
  ng_bvn: 'Bank Verification Number (BVN)',
  ng_nin: 'National Identity Number (NIN)',
  ng_vnin: 'Virtual NIN',
  ng_nin_phone: 'NIN Phone',
  ng_pvc: 'Voter’s Card',
  ng_phone: 'Phone Number',
  ng_tin: 'Taxpayer Identification Number',
  ng_national_id: 'National ID',
  gh_passport: 'International Passport',
  gh_ssnit: 'Social Security and National Insurance Trust',
  gh_voters_card: "Voter's Card",
  gh_drivers_license: 'Driver’s License',
  ke_passport: 'International Passport',
  ke_national_id: 'National ID',
  ke_tax_pin: 'Tax Pin',
  ke_phone: 'Phone Number',
  ke_get_phone: 'Get Phone',
  za_said: 'South African Identity Document',
  selfie_validation: 'Surported ID'
};

export const kybTitles = {
  ng_cac: 'Registration Number / CAC'
};

export const sessionKeys = {
  ADMIN_USER_TOKEN: 'k8r_adm_21sec',
  ADMIN_USER_TOKEN_EXPIRATION: 'k8r_adm_ty_21exp',
  ADMIN_REFRESH_TOKEN: 'k8r_adm_21ref',
  ADMIN_CLIENT_TOKEN: 'k8r_adm_21lty',
  ADMIN_USER_PROFILE: 'k8r_adm_21chichi',
  EXPORT_LINK: 'k8r_adm_21exp_lion',
  ACCESS: 'k8r_adm_21_tbobo',
  REDIRECT_EXTERNAL_LINK: 'k8r_adm_21_exfits'
};

export const bulkActionTypes = [
  'retry_generate_report',
  'payins',
  'pending_settlements',
  'paused_payments',
  'ready_settlements',
  'approved_settlements',
  'payout_reversals'
];

export const payinsFeeRules = {
  bank_transfer: {
    title: 'Bank Transfers',
    description: 'These are the rules for the fees charged to the merchant when their customer chooses to pay with bank transfer.'
  },
  card: {
    title: 'Card Payments',
    description: 'These are the rules for the fees charged to the merchant when their customer chooses to pay with a debit/credit card.'
  },
  reserved_bank_account: {
    title: 'Reserved Bank Account',
    description:
      'These are the rules for the fees charged to the merchant when their customer chooses to pay through the reserved bank account assigned to them.'
  },
  mobile_money: {
    title: 'Mobile Money',
    description:
      'These are the rules for the fees charged to the merchant when their customer chooses to pay through a mobile money provider.'
  },
  virtual_bank_account: {
    title: 'VBA',
    description:
      'These are the rules for the fees charged to the merchant when their customer chooses to pay through the virtual account assigned to them.'
  },
  pay_with_bank: {
    title: 'Pay with Bank (Direct debit)',
    description:
      'These are the rules for the fees charged to the merchant when their customer chooses to pay through a direct debit on their bank account.'
  },
  pool_account: {
    title: 'Pool Account',
    description:
      'This rules details the fees applicable to the merchant for all transactions processed using a reference generated for a pool account.'
  }
};

export const payoutsFeeRules = {
  bank_account: {
    title: 'Bank Payouts',
    description: 'These are the rules for the fees charged to the merchant when they make payouts to bank accounts.'
  },
  mobile_money: {
    title: 'Mobile Money',
    description: 'These are the rules for the fees charged to the merchant when they pay their customer through a mobile money provider.'
  }
};

export const CATEGORIES = {
  PAYOUTS: 'payouts',
  PAY_INS: 'pay-ins'
} as const;

export const PAYMENT_METHODS = {
  BANK_ACCOUNT: 'bank_account',
  BANK_TRANSFER: 'bank_transfer',
  BULK_BANK_ACCOUNT: 'bulk_bank_account',
  CARD: 'card',
  MOBILE_MONEY: 'mobile_money',
  PAY_WITH_BANK: 'pay_with_bank',
  DISBURSEMENT_WALLET: 'disbursement_wallet',
  VIRTUAL_BANK_ACCOUNT: 'virtual_bank_account'
} as const;

export const CONFIG_TITLES = {
  CHANNELS: 'channels',
  TRANSACTION_LIMIT: 'transaction_limit',
  LIMITS: 'limits',
  VBA_COUNT: 'vba_count',
  CAN_SEND_TO_ANY_MERCHANT: 'can_send_to_any_merchant'
} as const;
