export interface IModalButtonConfig {
  buttonText: string;
  buttonAction: () => void | Promise<void>;
  buttonStyle?: React.CSSProperties;
  hideButton?: boolean;
  buttonDisable?: boolean;
}
export interface IModalProps {
  close: (stage?: string) => void;
  visible?: boolean;
  themeColor?: string;
  modalBanner?: React.ReactNode;
  heading?: string;
  description?: string | React.ReactNode;
  content?: string | React.ReactNode;
  showButtons?: boolean;
  firstButtonText?: string;
  secondButtonText?: string;
  closeAction?: () => void;
  firstButtonColor?: string;
  secondButtonColor?: string;
  firstButtonTextColor?: string;
  showSecondButton?: boolean;
  firstButtonAction?: () => void;
  secondButtonAction?: () => void;
  firstButtonDisable?: boolean;
  secondButtonDisable?: boolean;
  secondButtonStyles?: React.CSSProperties;
  completedHeading?: string;
  completedDescription?: React.ReactNode;
  completedImage?: React.ReactNode;
  maxHeight?: string;
  size?: 'lg' | 'md' | 'sm' | 'mdBase' | 'dynamic';
  secondButtonActionIsTerminal?: boolean;
  headerBottomBorder?: boolean;
  formCenter?: boolean;
  completedModalSize?: 'base' | 'mdBase' | 'md' | 'sm' | 'dialog' | 'dialog-centered';
  equalFooterBtn?: boolean;
  secondaryCompletedModal?: boolean;
  showImage?: boolean;
  completedAction?: () => void;
  completedActionText?: string;
  isScrollable?: boolean;
  hideSecondButton?: boolean;
  showCompleteActionText?: boolean;
  modalBodyClassName?: string;
  noFormCenter?: boolean;
  thirdButtonText?: string;
  thirdButtonAction?: () => void;
  showThirdButton?: boolean;
  thirdButtonStyles?: React.CSSProperties;
  firstButtonStyles?: React.CSSProperties;
  showCompletedModal?: boolean;
  footerButtonWidthIsAuto?: boolean;
  secondButtonTestId?: string;
  buttonsConfig?: IModalButtonConfig[];
}

export type currencyExchangeModal = {
  size: 'md';
  close: () => void;
  secondButtonActionIsTerminal: boolean;
} & IModalProps;
