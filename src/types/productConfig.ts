import { FormikErrors, FormikProps, FormikTouched } from 'formik';

import {
  CurrencyType,
  FeeCategoryType,
  FeeType,
  FilterMerchantType,
  ICardFeeRecord,
  IFeeProps,
  IModalProps,
  MerchantFundingLimitsType,
  MerchantPCIDSSLimitsType,
  MerchantSpendingLimitsType,
  PCIDSSLevelType,
  RiskLevelType,
  VirtualCardCategory
} from '+types';
import { availableProducts } from '+utils';

export type modalState =
  | 'enable'
  | 'disable'
  | 'enable-all'
  | 'disable-all'
  | 'enable-one'
  | 'disable-one'
  | 'enable-product'
  | 'disable-product'
  | 'disable-confirm'
  | 'enable-confirm'
  | 'enable-channel'
  | 'disable-channel'
  | 'enable-product-all'
  | 'disable-product-all'
  | 'enable-channels'
  | 'disable-channels'
  | 'customs'
  | 'customs-product'
  | ''
  | null;

export type availableProductType = keyof typeof availableProducts;

export type productCategoriesType = 'pay-ins' | 'payouts' | 'card-issuance';

export type productType = 'collection' | 'disbursement' | 'card-issuance';

export type MerchantCurrencyModalType = {
  status?: 'active' | 'inactive';
  activeModal: modalState;
  setActiveModal: React.Dispatch<React.SetStateAction<modalState>>;
  currency: CurrencyType;
  hideButton?: boolean;
  merchantId?: string;
  productType?: productType;
  method?: availableProductType;
};

export type updateProductConfigData = {
  type?: 'all_merchants' | 'single_merchant' | 'default' | 'custom_merchants';
  currency: CurrencyType;
  payment_type?: productType;
  payment_method?: availableProductType;
  data: {
    enabled: boolean;
    transaction_limit?: {
      min: number;
      max: number;
    };
    channels?: string[];
    can_send_to_any_merchant?: boolean;
    vba_count?: boolean;
    withdrawal_limit?: {
      daily: {
        other_accounts: number;
        settlement_account: number;
      };
      settlement_account: number;
      other_accounts: number;
    };
    limits?: {
      api: {
        per_transaction: TransactionLimitType;
      };
      web?: {
        per_transaction: {
          settlement_account: TransactionLimitType;
          non_settlement_account: TransactionLimitType;
        };
      };
      global?: {
        daily: number;
      };
    };
  };
  account_id?: string;
};

export type productConfigTableDataType = {
  name: string;
  email: string;
  request_type?: string;
  usd_access?: string;
  subscription_plan?: string;
  risk_level: 'low' | 'medium' | 'high' | 'above';
  status: 'true' | 'false';
  configuration_type: 'custom' | 'default';
  created_at: string;
};

export type TransactionLimitType = {
  min?: number;
  max?: number;
};
export type VbaCountType = {
  used_vba: number;
  total_vba: number;
  unused_vba: number;
  low_balance_warning: boolean;
};

export type WithdrawalLimitType = {
  api: {
    per_transaction: TransactionLimitType;
  };
  web: {
    daily: {
      settlement_account: number;
      non_settlement_account: number;
    };
    per_transaction: {
      settlement_account: TransactionLimitType;
      non_settlement_account: TransactionLimitType;
    };
  };
  global?: {
    daily: number;
  };
  limit?: TransactionLimitType;
};
export type UpdateVbaCountDataType = {
  count: number;
  currency: string;
  reason: string;
};

export type ProductMethodConfigType = {
  enabled: boolean;
  channels: string[];
  transaction_limit: TransactionLimitType;
  vba_count: VbaCountType;
  limits: WithdrawalLimitType;
};
export type ProductConfigType = { enabled: boolean; limits: WithdrawalLimitType } & {
  [key in keyof availableProductType]: ProductMethodConfigType;
};

export type updateProductConfigDataType = keyof updateProductConfigData['data'];
export type updateProductConfigDataTypeWithoutEnabled = Exclude<updateProductConfigDataType, 'enabled' | 'can_send_to_any_merchant'>;
export type productCategoriesPropTypeContentWithoutEnabled =
  | ProductConfigType[keyof availableProductType]['channels']
  | ProductConfigType[keyof availableProductType]['transaction_limit']
  | ProductConfigType[keyof availableProductType]['limits'];

export interface IProductDetails {
  data: any;
  isLoading: boolean;
  currency: CurrencyType;
  method: availableProductType;
  product: productCategoriesType;
}

export type ProductDetailsTabKeyType =
  | 'defaults'
  | 'approved_merchants'
  | 'requesting_merchants'
  | 'merchants-with-access'
  | 'default-config';

export type ProductDetailsTabType = Array<{ label: string; key: ProductDetailsTabKeyType }>;

type PlanFeesType = Record<`${FeeCategoryType}_fee`, IFeeProps>;

export type CustomerMonthlyPaymentType = 'LessThan50K' | 'From50KTo100K' | 'From100KTo500K' | 'From500KTo1M' | 'Above1M';

export type ReservedMonthlyPaymentType = 'LessThan1K' | 'From1KTo5K' | 'From5KTo10K' | 'From10KTo50K' | 'Above50K';

export interface IMerchantInfo extends PlanFeesType {
  merchant: FilterMerchantType;
  risk_level: RiskLevelType;
  pci_dss_level: PCIDSSLevelType;
  total_payment_value: string;
  max_limit_per_trxn: string;
  daily_trxn_cap: string;
  monthly_trxn_cap: string;
  max_daily_funding: string;
  max_monthly_funding: string;
  max_quarterly_funding: string;
  trxn_count: string;
  yearly_issued_cards: string;
  monthly_card_limit: string;
  subscription_plan: string;
  monthly_payment_value: CustomerMonthlyPaymentType | ReservedMonthlyPaymentType;
}

export type MerchantInfoFormikBagType = Pick<
  FormikProps<Partial<IMerchantInfo>>,
  'errors' | 'values' | 'touched' | 'handleBlur' | 'handleChange' | 'getFieldProps' | 'setFieldValue'
>;

export type StepType =
  | 'init'
  | 'set_spending_limit'
  | 'set_funding_limit'
  | 'set_pci_dss_limit'
  | 'set_count_limit'
  | 'set_subscription_fees'
  | 'confirm_submission';

export type LimitStepType = Exclude<StepType, 'init' | 'confirm_submission' | 'set_subscription_fees'>;

export interface ILimitConfigFields extends MerchantInfoFormikBagType {
  isDefault: boolean;
  currency?: CurrencyType;
  category?: 'issued-cards' | 'reserved-cards';
}

export interface IConfirmSubmission {
  category: 'issued-cards' | 'reserved-cards';
  onToggleConfirm: React.ChangeEventHandler<HTMLInputElement>;
  confirmed: boolean;
}

export interface IMerchantInfoForm
  extends Pick<
    FormikProps<Partial<IMerchantInfo>>,
    'errors' | 'values' | 'touched' | 'handleBlur' | 'handleChange' | 'getFieldProps' | 'setFieldValue'
  > {
  currency: CurrencyType;
  category: 'issued-cards' | 'reserved-cards';
}

export type LimitType = 'pci_dss' | 'spending' | 'funding';

export type ChannelType = 'dashboard' | 'api';
export interface ILimitSettingHeader {
  channel: ChannelType;
  type: LimitType;
}

export type ProductCategoryType = 'issued-cards' | 'reserved-cards';

export type PartialIModalPropsType = Partial<IModalProps>;

export type EditActionType = 'edit_pci_dss_limit' | 'edit_funding_limit' | 'edit_spending_limit';

export type CardIssuanceDefaultLimitsResponseType = Record<
  'customer' | 'reserved',
  {
    risk_level: {
      [K in RiskLevelType]: {
        funding_limit: {
          daily_max: number;
          monthly_max: number;
          quarterly_max: number;
        };
        spending_limit: {
          daily_max: number;
          monthly_max: number;
          per_transaction_max: number;
        };
      };
    };
    pcidss_level: {
      [K in PCIDSSLevelType]: {
        yearly_issued_cards: number | 'limitless';
        yearly_transaction_count: number | 'limitless';
      };
    };
  }
>;

export type RiskLevelResponseType = CardIssuanceDefaultLimitsResponseType['customer']['risk_level'];

export type PCIDSSLevelResponseType = CardIssuanceDefaultLimitsResponseType['customer']['pcidss_level'];

export interface ILimitsByPCIDSS {
  values: MerchantPCIDSSLimitsType & {
    pciDssLevel?: PCIDSSLevelType;
  };
  onBlur: React.ChangeEventHandler;
  errors: FormikErrors<MerchantPCIDSSLimitsType>;
  touched: FormikTouched<MerchantPCIDSSLimitsType>;
  setFieldValue: FormikProps<MerchantPCIDSSLimitsType>['setFieldValue'];
  getFieldProps: FormikProps<MerchantPCIDSSLimitsType>['getFieldProps'];
}

export interface ICardFundingLimits {
  currency: CurrencyType;
  values: MerchantFundingLimitsType & {
    riskLevel?: RiskLevelType;
  };
  onBlur: React.ChangeEventHandler;
  errors: FormikErrors<MerchantFundingLimitsType>;
  touched: FormikTouched<MerchantFundingLimitsType>;
  setFieldValue: FormikProps<MerchantFundingLimitsType>['setFieldValue'];
  getFieldProps: FormikProps<MerchantFundingLimitsType>['getFieldProps'];
}

export interface ICardSpendingLimits {
  currency: CurrencyType;
  values: MerchantSpendingLimitsType & {
    riskLevel?: RiskLevelType;
  };
  onBlur: React.ChangeEventHandler;
  errors: FormikErrors<MerchantSpendingLimitsType>;
  touched: FormikTouched<MerchantSpendingLimitsType>;
  setFieldValue: FormikProps<MerchantSpendingLimitsType>['setFieldValue'];
  getFieldProps: FormikProps<MerchantSpendingLimitsType>['getFieldProps'];
}

export type EditMerchantLimitModalPayloadType = (MerchantPCIDSSLimitsType | MerchantFundingLimitsType | MerchantSpendingLimitsType) & {
  riskLevel?: RiskLevelType;
  pciDssLevel?: PCIDSSLevelType;
  type: 'custom' | 'default';
};

export interface IEditMerchantLimitsModal {
  limitType: LimitType;
  onClose: () => void;
  cardCategory: 'customer' | 'reserved';
  defaultValues: EditMerchantLimitModalPayloadType;
  merchantId: string;
  refetchLimits: () => void;
}

export type CustomizePCIDSSLimitsPayloadType = {
  currency: CurrencyType;
  card_category: 'customer' | 'reserved';
  yearly_transaction_count: number | 'limitless';
  yearly_issued_cards: number;
};

export type EditMerchantLimitsPayloadType =
  | {
      type: 'risk_level';
      card_category: VirtualCardCategory;
      risk_level_limits: {
        spending_limit?: {
          per_transaction_max: number;
          monthly_max: number;
          daily_max: number;
        };
        funding_limit?: {
          daily_max: number;
          monthly_max: number;
          quarterly_max: number;
        };
      };
    }
  | {
      type: 'pcidss_level';
      card_category: VirtualCardCategory;
      pcidss_level_limits: {
        yearly_issued_cards: number;
        yearly_transaction_count: number | 'limitless';
      };
    };

export type CustomizeRiskLevelLimitsPayloadType = {
  currency: CurrencyType;
  card_category: 'reserved' | 'customer';
  spending_limit?: Partial<MerchantSpendingLimitsType>;
  funding_limit?: Partial<MerchantFundingLimitsType>;
};

export type TransformedDefaultPlansConfigType = Record<
  VirtualCardCategory,
  Array<{
    name: string;
    cardLimit: string | number;
    fee: ICardFeeRecord['customer'];
  }>
>;

export interface IAddMerchantModal {
  merchantClass: 'new' | 'existing';
  category: 'reserved-cards' | 'issued-cards';
  onClose: () => void;
  defaultLimitsConfig: CardIssuanceDefaultLimitsResponseType['customer'];
  defaultPlansConfig: TransformedDefaultPlansConfigType['customer'];
}

export type AddMerchantModalStepType =
  | 'init'
  | `set_${'spending' | 'funding' | 'count' | 'pci_dss'}_limit`
  | 'set_subscription_fees'
  | 'confirm_submission';

type LimitData = {
  per_transaction_max?: number;
  daily_max?: number;
  monthly_max?: number;
  yearly_issued_cards?: number;
  yearly_transaction_count?: number;
  quarterly_max?: number;
};

type LimitConfig = {
  type: 'custom' | 'default';
  data: LimitData;
};

export type AddMerchantRequestPayload = {
  card_type: 'virtual';
  currency: string;
  kora_id: number;
  risk_level: RiskLevelType;
  pci_dss_level: PCIDSSLevelType;
  subscription_plan: string;
  monthly_payment_value: CustomerMonthlyPaymentType | ReservedMonthlyPaymentType;
  spending_limit?: LimitConfig;
  funding_limit?: LimitConfig;
  pcidss_level_limits?: LimitConfig;
  plan_config?: {
    type: 'custom' | 'standard';
    monthly_card_limit?: number;
    fee: Record<FeeCategoryType, IFeeProps>;
  };
};

export type ToggleCardCreationPayloadType = {
  card_category: 'customer' | 'reserved';
  action: 'enable' | 'disable';
  reference: string;
};

export type CardStatusTogglePayloadType = {
  currency: CurrencyType;
  kora_id: number;
  access_request_reference: string;
  card_category: 'reserved' | 'customer';
};

export type PartnerSwitchingPayloadType = {
  merchantName: string;
  merchantReference: string;
};

export type ProductConfigSettingsType = {
  setting: {
    enabled: boolean;
    settlement: {
      cycle: Record<
        'card' | 'virtual_bank_account',
        {
          default?: number;
          low_risk: number;
          high_risk: number;
          medium_risk: number;
          above_average_risk: number;
        }
      >;
      conversion: Record<
        CurrencyType,
        {
          markup: Record<
            'kora' | 'merchant',
            {
              value: number;
              limit?: number;
              updated_at: string;
            }
          >;
          enabled: boolean;
          activated: boolean;
        }
      >;
      updated_at: string;
      destination: 'disbursement_wallet';
      rolling_reserve: {
        card: Record<
          RiskLevelType,
          {
            rate: number;
            period: number;
          }
        >;
      };
    };
    transaction: {
      collection: {
        enabled: boolean;
        card: {
          enabled: boolean;
          channels: ['modal' | 'api' | 'web'];
          transaction_limit: {
            max: number;
            min: number;
          };
          allowed_card_currency: [];
        };
        disbursement_wallet: {
          enabled: boolean;
          channels: ['web'];
          transaction_limit: {
            max: number;
            min: number;
          };
        };
        virtual_bank_account: {
          enabled: boolean;
          channels: ['api'];
          count_limit: number;
          transaction_limit: {
            individual: Record<'tier_one' | 'tier_two', { daily: 300; single: 100; monthly: 500 }>;
          };
        };
      };
      conversion: {
        limit: {
          max: number;
          min: number;
        };
        markup: {
          NGN: {
            value: number;
            updated_at: string;
          };
        };
        enabled: boolean;
      };
      disbursement: {
        limits: WithdrawalLimitType;
        enabled: boolean;
        bank_account: {
          enabled: boolean;
          channels: ['api', 'web'];
          transaction_limit: {
            max: number;
            min: number;
          };
          limits: WithdrawalLimitType;
        };
        disbursement_wallet: {
          enabled: boolean;
          channels: ['api', 'web'];
          transaction_limit: {
            max: number;
            min: number;
          };
          can_send_to_any_merchant: false;
        };
      };
    };
    card_issuance: {
      enabled: boolean;
      virtual: {
        enabled: boolean;
        reserved: {
          enabled: boolean;
          count_limit: number;
          access_status: 'active' | 'inactive';
        };
        count_limit: number;
        access_status: 'active' | 'inactive';
        funding_limit: {
          max: number;
          min: number;
        };
      };
      updated_at: 'string';
      auto_funding: boolean;
      billing_cycle: {
        day: number;
        type: 'static' | 'dynamic';
      };
      issuing_wallet: {
        funding_limit: {
          min: number;
        };
        funding_fee: {
          type: FeeType;
          amount: number;
          enabled: boolean;
        };
      };
      limit_configurations: {
        customer: {
          risk_level: Record<
            RiskLevelType,
            {
              funding_limit: {
                daily_max: number;
                monthly_max: number;
                quarterly_max: number;
              };
              spending_limit: {
                daily_max: number;
                monthly_max: number;
                per_transaction_max: number;
              };
            }
          >;
          pcidss_level: Record<
            PCIDSSLevelType,
            {
              yearly_issued_cards: number;
              yearly_transaction_count: number;
            }
          >;
        };
        reserved: {
          risk_level: Record<
            RiskLevelType,
            {
              funding_limit: {
                daily_max: number;
                monthly_max: number;
                quarterly_max: number;
              };
              spending_limit: {
                daily_max: number;
                monthly_max: number;
                per_transaction_max: number;
              };
            }
          >;
          pcidss_level: Record<
            PCIDSSLevelType,
            {
              yearly_issued_cards: number;
              yearly_transaction_count: number;
            }
          >;
        };
      };
    };
  };
};

export type SwitchProviderPaylodType = {
  provider: string;
  previous_provider: string;
  card_category: 'reserved' | 'customer';
  reason: string;
};

export type ModifyAccessPayloadType = {
  kora_id: number;
  currency: CurrencyType;
  access_type: 'service' | 'card';
  action?: 'disable' | 'enable';
  decline_request?: boolean;
  reason?: string;
  card_type: 'virtual';
  access_request_reference?: string;
  card_category?: 'reserved' | 'customer';
};
interface BaseCardIssuanceUpdateLimits {
  currency: string;
  card_category: 'customer' | 'reserved';
}

export interface CardIssuanceRequestFundingLimits extends BaseCardIssuanceUpdateLimits {
  funding_limits: {
    level: RiskLevelType | string;
    data: {
      daily_max: number;
      monthly_max: number;
    };
  }[];
}

export interface CardIssuanceRequestSpendingLimits extends BaseCardIssuanceUpdateLimits {
  spending_limits: {
    level: RiskLevelType | string;
    data: {
      daily_max?: number;
      monthly_max?: number;
      per_transaction_max?: number;
    };
  }[];
}

export interface CardIssuanceRequestPCIDSSLimits extends BaseCardIssuanceUpdateLimits {
  pcidss_level_limits: {
    level: PCIDSSLevelType | string;
    data: {
      yearly_issued_cards: number | 'limitless';
      yearly_transaction_count: number | 'limitless';
    };
  }[];
}

export type CardIssuanceUpdateLimits =
  | CardIssuanceRequestFundingLimits
  | CardIssuanceRequestSpendingLimits
  | CardIssuanceRequestPCIDSSLimits;
export interface IVbaLimitDetailsProps {
  content: VbaCountType;
}

export type EditDetailsCardPropsType = {
  title: Exclude<updateProductConfigDataType, 'enabled'>;
  content:
    | ProductConfigType[keyof availableProductType]['channels']
    | ProductConfigType[keyof availableProductType]['transaction_limit']
    | ProductConfigType[keyof availableProductType]['vba_count']
    | ProductConfigType[keyof availableProductType]['limits'];
  merchantId?: string;
  paymentMethod: availableProductType;
  currency: CurrencyType;
  category: productCategoriesType;
  type: string;
  disableEdit: boolean;
};
export type VbaCountsDataType = {
  num: number;
  reason: string;
};

export type ModalType = 'transaction_limit' | 'channels' | 'all' | 'vba_count' | 'withdrawal_limit' | 'limits' | null;

export type WithdrawalLimitTypeKey = Exclude<keyof WithdrawalLimitType, 'limit'>;

export type WebAccountType = 'settlement' | 'non-settlement';
export type WithdrawalChannelType = 'api' | 'web' | 'global';
